"""
自适应梯度惩罚权重配置

定义自适应梯度惩罚权重管理器的配置类。
"""
from dataclasses import dataclass, field
from typing import Any


@dataclass
class AdaptiveLambdaGPConfig:
    """自适应梯度惩罚权重配置类"""

    # 预热步数，在此期间使用固定的lambda_gp
    warmup_steps: int

    # 最小允许的lambda_gp值
    min_lambda_gp: float

    # 最大允许的lambda_gp值
    max_lambda_gp: float

    # 是否启用自适应梯度惩罚权重
    enabled: bool = False

    # 基准梯度惩罚权重，如果为None则根据模型配置自动选择
    base_lambda_gp: float | None = None

    # 适应速率，控制调整的幅度
    adaptation_rate: float = 0.05

    # 更新间隔，每隔多少步更新一次lambda_gp
    update_interval: int = 10

    # 平滑因子，用于指数移动平均
    smoothing_factor: float = 0.7

    # 目标梯度范数
    grad_norm_target: float = 1.0

    # 梯度范数容忍度
    grad_norm_tolerance: float = 0.5

    # 已删除 save_history_plot 和 history_plot_path 字段

    # 是否记录详细日志
    verbose_logging: bool = True

    # 额外配置参数
    extra_params: dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """初始化后的验证和处理"""
        # 确保min_lambda_gp不小于0.0
        if self.min_lambda_gp < 0.0:
            raise ValueError(f"min_lambda_gp不能小于0.0，当前值: {self.min_lambda_gp}")

        # 确保max_lambda_gp不小于min_lambda_gp
        if self.max_lambda_gp < self.min_lambda_gp:
            raise ValueError(f"max_lambda_gp ({self.max_lambda_gp}) 不能小于 min_lambda_gp ({self.min_lambda_gp})")

        # 确保adaptation_rate在合理范围内
        if not (0.001 <= self.adaptation_rate <= 0.2):
            raise ValueError(f"adaptation_rate必须在0.001-0.2范围内，当前值: {self.adaptation_rate}")

        # 确保warmup_steps不小于0
        if self.warmup_steps < 0:
            raise ValueError(f"warmup_steps不能小于0，当前值: {self.warmup_steps}")

    @classmethod
    def from_dict(cls, config_dict: dict[str, Any]) -> 'AdaptiveLambdaGPConfig':
        """从字典创建配置对象"""
        # 过滤掉不在类定义中的键
        valid_keys = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_dict = {k: v for k, v in config_dict.items() if k in valid_keys}

        # 将额外的键存储在extra_params中
        extra_params = {k: v for k, v in config_dict.items() if k not in valid_keys}
        if extra_params:
            filtered_dict['extra_params'] = extra_params

        return cls(**filtered_dict)

    def to_dict(self) -> dict[str, Any]:
        """将配置对象转换为字典"""
        result = {f.name: getattr(self, f.name) for f in self.__dataclass_fields__.values()}
        # 合并extra_params
        if self.extra_params:
            for k, v in self.extra_params.items():
                result[k] = v
            # 移除extra_params本身
            result.pop('extra_params', None)
        return result

    def __str__(self) -> str:
        """返回配置的字符串表示"""
        items = [f"{k}={v}" for k, v in self.to_dict().items()]
        return f"AdaptiveLambdaGPConfig({', '.join(items)})"
