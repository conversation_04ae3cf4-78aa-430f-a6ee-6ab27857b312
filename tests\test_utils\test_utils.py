"""
测试工具模块，提供测试辅助功能
"""

from unittest.mock import MagicMock

import torch


class MockWithFormat(MagicMock):
    """
    支持格式化字符串的MagicMock
    """
    def __format__(self, format_spec):
        try:
            # 处理逗号格式
            if format_spec == ',':
                return f"{self()}"

            # 支持浮点数格式化
            if '.' in format_spec and format_spec[-1] in 'fFeEgGn%':
                return "0.0000"

            # 处理其他格式化规范
            if format_spec:
                # 尝试处理其他格式化规范
                if format_spec in ['s', 'd', 'i', 'x', 'X', 'o', 'b', 'c']:
                    return str(self())
                elif format_spec in ['r', 'a']:
                    return repr(self())

            # 默认格式化
            return super().__format__("")
        except Exception:
            # 如果格式化失败，返回安全的默认值
            return "<mock>"

    def __str__(self):
        try:
            # 尝试调用原始的__str__
            return super().__str__()
        except Exception:
            # 如果失败，返回安全的默认值
            return "<mock>"

    def __repr__(self):
        try:
            # 尝试调用原始的__repr__
            return super().__repr__()
        except Exception:
            # 如果失败，返回安全的默认值
            return "<mock>"

import pytest


@pytest.mark.batch3  # 测试工具模块
def create_mock_gan_model(config):
    """
    创建模拟的GAN模型

    Args:
        config: 配置对象

    Returns:
        模拟的GAN模型
    """
    from src.models.gan.gan_model import GANModel

    model = MockWithFormat(spec=GANModel)
    model.config_manager = config
    model.config = config.model
    model.device = torch.device('cpu')
    model.generator = MockWithFormat()
    model.discriminator = MockWithFormat()
    model.generator.count_parameters = MockWithFormat(return_value=100000)
    model.discriminator.count_parameters = MockWithFormat(return_value=50000)
    model.predict = MockWithFormat(return_value=torch.randn(32, 100, 1))
    model.training = False  # 设置为评估模式
    model.lambda_gp = 10.0
    model.use_amp = False
    model.input_dim = 20  # 设置输入维度

    # 创建虚拟的参数
    param = torch.nn.Parameter(torch.randn(1))
    model.parameters = MockWithFormat(return_value=iter([param]))

    # 创建虚拟的优化器
    model.generator_optimizer = MockWithFormat()
    model.discriminator_optimizer = MockWithFormat()
    model.generator_optimizer.step = MockWithFormat()
    model.discriminator_optimizer.step = MockWithFormat()

    # 创建虚拟的损失计算器
    model.loss_calculator = MockWithFormat()
    model.loss_calculator.calculate_generator_loss = MockWithFormat(return_value=(torch.tensor(1.0), {}))
    model.loss_calculator.calculate_discriminator_loss = MockWithFormat(return_value=(torch.tensor(0.5), {}))

    # 创建虚拟的Scaler
    class DummyScaler:
        def scale(self, loss): return loss
        def unscale_(self, optimizer): pass
        def step(self, optimizer): optimizer.step()
        def update(self, found_inf=False): pass
        def is_enabled(self): return False
        def get_scale(self): return 1.0

    model.generator_scaler = DummyScaler()
    model.discriminator_scaler = DummyScaler()

    # 设置其他属性
    model.initial_feature_dim = 20
    model.current_feature_dim = 20
    model.hidden_dim = 128
    model.noise_dim = 64

    # 设置日志
    model._logger = MockWithFormat()

    # 添加评估方法
    model.evaluate = MockWithFormat(return_value={
        'val_loss': 0.2,
        'val_mse': 0.05,
        'val_mae': 0.2,
        'val_rmse': 0.22,
        # 'val_mape': 0.15, # 移除未计算的指标
        'val_smape': 0.12
    })

    # 添加状态字典方法
    model.state_dict = MockWithFormat(return_value={
        'generator.layer1.weight': torch.randn(10, 20),
        'generator.layer1.bias': torch.randn(10),
        'discriminator.layer1.weight': torch.randn(10, 20),
        'discriminator.layer1.bias': torch.randn(10)
    })
    model.load_state_dict = MockWithFormat()

    # 添加eval方法
    model.eval = MockWithFormat()

    # 添加模型调用方法
    model.__call__ = MockWithFormat(return_value=torch.randn(32, 100, 1))

    return model

@pytest.mark.batch3  # 测试工具模块
def create_mock_tensor_batch(batch_size=32, seq_len=100, feature_dim=20):
    """
    创建模拟的张量批次

    Args:
        batch_size: 批次大小
        seq_len: 序列长度
        feature_dim: 特征维度

    Returns:
        模拟的张量批次
    """
    features = torch.randn(batch_size, seq_len, feature_dim)
    target = torch.randn(batch_size, seq_len, 1)

    return {
        'features': features,
        'target': target
    }

@pytest.mark.batch3  # 测试工具模块
def create_mock_dataloader(batch_size=32, seq_len=100, feature_dim=20, num_batches=10):
    """
    创建模拟的数据加载器

    Args:
        batch_size: 批次大小
        seq_len: 序列长度
        feature_dim: 特征维度
        num_batches: 批次数量

    Returns:
        模拟的数据加载器
    """
    batches = [create_mock_tensor_batch(batch_size, seq_len, feature_dim) for _ in range(num_batches)]

    class MockDataLoader:
        def __init__(self, batches):
            self.batches = batches

        def __iter__(self):
            return iter(self.batches)

        def __len__(self):
            return len(self.batches)

    return MockDataLoader(batches)

@pytest.mark.batch3  # 测试工具模块
def patch_torch_amp():
    """
    修补torch.amp模块，使其在测试中正常工作

    Returns:
        修补后的torch.amp模块
    """
    class MockAutocast:
        def __init__(self, device_type='cuda', dtype=None, enabled=True):
            self.device_type = device_type
            self.dtype = dtype
            self.enabled = enabled

        def __enter__(self):
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            pass

    class MockGradScaler:
        def __init__(self, init_scale=65536.0, growth_factor=2.0, backoff_factor=0.5, growth_interval=2000, enabled=True):
            self.init_scale = init_scale
            self.growth_factor = growth_factor
            self.backoff_factor = backoff_factor
            self.growth_interval = growth_interval
            self.enabled = enabled

        def scale(self, loss):
            return loss

        def unscale_(self, optimizer):
            pass

        def step(self, optimizer):
            optimizer.step()

        def update(self, found_inf=False):
            pass

        def is_enabled(self):
            return self.enabled

        def get_scale(self):
            return self.init_scale

    return MockAutocast, MockGradScaler
