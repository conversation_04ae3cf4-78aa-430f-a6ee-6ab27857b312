"""
测试模块路径：tests/test_utils/test_resource_manager.py
测试目标：验证src/utils/resource_manager.py的资源管理功能

测试要点：
1. 内存监控测试 - 测试内存使用监控和自动清理缓存
2. GPU资源管理测试 - 测试GPU内存使用情况获取
3. 缓存功能测试 - 测试LRU缓存策略的正确实现
4. 资源清理测试 - 测试缓存清理功能
5. 错误处理测试 - 测试异常情况下的错误处理
6. 缓存禁用测试 - 测试缓存禁用时的行为
7. 内存使用情况获取测试 - 测试get_memory_usage方法
8. 配置默认值回退测试 - 测试配置缺失时的默认值行为
"""

import os
from unittest.mock import MagicMock, patch

import numpy as np
import pytest
import torch

from src.utils.config_manager import ConfigManager
from src.utils.resource_manager import ResourceManager


@pytest.mark.batch3  # 资源管理器测试
class TestResourceManager:
    @pytest.fixture
    def mock_config(self):
        """创建模拟配置"""
        config = MagicMock(spec=ConfigManager)
        config.system = MagicMock()
        config.system.memory = MagicMock()
        config.system.memory.memory_limit = 0.8
        config.system.cache = MagicMock()
        config.system.cache.size = 1000
        config.system.cache.enable_memory_cache = True
        return config

    @pytest.fixture
    def resource_manager(self, mock_config):
        """创建资源管理器"""
        # 设置测试模式环境变量
        os.environ['TESTING'] = '1'
        manager = ResourceManager(mock_config)
        yield manager
        # 清理环境变量
        os.environ.pop('TESTING', None)

    def test_memory_monitoring(self, resource_manager):
        """测试内存监控

        测试内存使用超过限制时是否正确触发缓存清理。
        """
        # 模拟内存使用超过限制
        with patch('psutil.virtual_memory') as mock_memory:
            mock_memory.return_value.percent = 90  # 90%内存使用
            with patch.object(resource_manager, 'clear_cache') as mock_clear:
                # 触发监控检查
                resource_manager._monitor_resources()
                # 验证清理缓存方法被调用
                mock_clear.assert_called_once()

    def test_gpu_memory_management(self, resource_manager):
        """测试GPU内存管理

        测试获取GPU内存使用情况的功能，包括GPU可用和不可用的情况。
        """
        # 测试GPU可用情况
        with patch('torch.cuda.is_available', return_value=True), \
             patch('torch.cuda.memory_allocated', return_value=1024*1024*500):  # 500MB
            usage = resource_manager.get_gpu_memory_usage()
            assert usage['available'] is True
            assert abs(usage['allocated'] - 500) < 1  # 允许小于1MB的误差

        # 测试GPU不可用情况
        with patch('torch.cuda.is_available', return_value=False):
            usage = resource_manager.get_gpu_memory_usage()
            assert usage['available'] is False

    def test_cache_management(self, resource_manager):
        """测试缓存管理

        测试预测结果和特征的缓存和获取功能。
        """
        # 测试预测缓存
        features = torch.randn(10, 10)
        result = torch.randn(10, 1)

        # 缓存预测结果
        resource_manager.cache_prediction(features, result, False)

        # 获取缓存结果
        cached = resource_manager.get_prediction_if_cached(features, False, True)
        assert cached is not None
        assert torch.equal(cached, result)

        # 测试特征缓存
        data_key = "test_data"
        feature = np.random.rand(10, 10)

        # 缓存特征
        resource_manager.cache_feature(data_key, feature)

        # 获取缓存特征
        cached_feature = resource_manager.get_feature_if_cached(data_key)
        assert cached_feature is not None
        assert np.array_equal(cached_feature, feature)

    def test_cache_eviction(self, resource_manager):
        """测试缓存淘汰

        测试LRU缓存淘汰策略，验证最久未使用的项被淘汰。
        """
        # 设置小缓存大小
        resource_manager.cache_size = 3

        # 添加3个缓存项
        for i in range(3):
            resource_manager.cache_feature(f"key_{i}", f"value_{i}")

        # 访问第一个项，使其变为最近使用
        resource_manager.get_feature_if_cached("key_0")

        # 添加第4个项，应该淘汰第2个项（最久未使用）
        resource_manager.cache_feature("key_3", "value_3")

        # 检查缓存大小
        assert len(resource_manager.feature_cache) == 3
        assert "key_0" in resource_manager.feature_cache  # 第一个应该保留（最近使用）
        assert "key_1" not in resource_manager.feature_cache  # 第二个应该被淘汰（最久未使用）
        assert "key_2" in resource_manager.feature_cache  # 第三个应该保留
        assert "key_3" in resource_manager.feature_cache  # 第四个应该添加成功

        # 测试预测缓存的LRU行为
        resource_manager.cache_size = 2
        features1 = torch.randn(5, 5)
        features2 = torch.randn(5, 5)
        features3 = torch.randn(5, 5)

        # 添加2个预测缓存
        resource_manager.cache_prediction(features1, "result1", False)
        resource_manager.cache_prediction(features2, "result2", False)

        # 访问第一个项，使其变为最近使用
        resource_manager.get_prediction_if_cached(features1, False, True)

        # 添加第3个项，应该淘汰第2个项
        resource_manager.cache_prediction(features3, "result3", False)

        # 验证第一个项还在缓存中
        assert resource_manager.get_prediction_if_cached(features1, False, True) is not None
        # 验证第二个项已经被淘汰
        assert resource_manager.get_prediction_if_cached(features2, False, True) is None
        # 验证第三个项在缓存中
        assert resource_manager.get_prediction_if_cached(features3, False, True) is not None

    def test_clear_cache(self, resource_manager):
        """测试清理缓存

        测试清理缓存功能，包括特征缓存和预测缓存的清理。
        """
        # 添加缓存数据
        resource_manager.cache_feature("test_key", "test_value")
        resource_manager.cache_prediction(torch.randn(1,1), torch.randn(1,1), False)

        # 清理缓存
        with patch('gc.collect') as mock_gc, patch('torch.cuda.empty_cache') as mock_cuda:
            resource_manager.clear_cache()

            # 验证缓存被清理
            assert len(resource_manager.feature_cache) == 0
            assert len(resource_manager.prediction_cache) == 0
            mock_gc.assert_called_once()
            mock_cuda.assert_called_once()

    def test_error_handling(self, resource_manager):
        """测试错误处理

        测试异常情况下的错误处理机制。
        """
        # 测试GPU内存获取错误
        with patch('torch.cuda.is_available', return_value=True), \
             patch('torch.cuda.memory_allocated', side_effect=RuntimeError("GPU error")):
            usage = resource_manager.get_gpu_memory_usage()
            assert 'error' in usage
            assert 'GPU error' in usage['error']

        # 测试监控线程错误 - 在测试模式下应该抛出异常
        with patch('psutil.virtual_memory', side_effect=Exception("Monitor error")):
            with pytest.raises(RuntimeError) as excinfo:
                resource_manager._monitor_resources()
            assert "Monitor error" in str(excinfo.value)

    def test_cache_disabled(self, mock_config):
        """测试缓存禁用

        测试当缓存禁用时的行为。
        """
        # 禁用缓存
        mock_config.system.cache.enable_memory_cache = False
        manager = ResourceManager(mock_config)

        # 测试预测缓存
        features = torch.randn(5, 5)
        manager.cache_prediction(features, "result", False)
        assert len(manager.prediction_cache) == 0  # 缓存应该为空

        # 测试获取缓存
        cached = manager.get_prediction_if_cached(features, False, True)
        assert cached is None  # 应该返回None

        # 测试特征缓存
        manager.cache_feature("key", "value")
        assert len(manager.feature_cache) == 0  # 缓存应该为空

        # 测试获取特征缓存
        cached = manager.get_feature_if_cached("key")
        assert cached is None  # 应该返回None

    def test_get_memory_usage(self, resource_manager):
        """测试获取内存使用情况

        测试get_memory_usage方法的功能。
        """
        with patch('psutil.Process') as mock_process:
            # 模拟进程内存信息
            mock_process_instance = MagicMock()
            mock_process.return_value = mock_process_instance

            # 设置内存信息
            mock_memory_info = MagicMock()
            mock_memory_info.rss = 1024 * 1024 * 100  # 100MB
            mock_memory_info.vms = 1024 * 1024 * 200  # 200MB
            mock_process_instance.memory_info.return_value = mock_memory_info
            mock_process_instance.memory_percent.return_value = 5.0  # 5%

            # 获取内存使用情况
            memory_usage = resource_manager.get_memory_usage()

            # 验证结果
            assert 'rss' in memory_usage
            assert 'vms' in memory_usage
            assert 'percent' in memory_usage
            assert memory_usage['rss'] == 100.0  # 100MB
            assert memory_usage['vms'] == 200.0  # 200MB
            assert memory_usage['percent'] == 5.0  # 5%

    def test_no_default_values(self, mock_config):
        """测试禁止默认值（规则44）"""
        # 清空配置
        mock_config.system = None

        # 应直接抛出异常而非使用默认值
        with pytest.raises(AttributeError):
            ResourceManager(mock_config)

if __name__ == '__main__':
    pytest.main(['-v'])


@pytest.mark.batch2  # 资源使用规范测试
class TestResourceManagerUsagePatterns:
    """测试资源管理模块的使用规范（新增）"""

    @pytest.fixture
    def mock_config(self):
        """创建模拟配置"""
        config = MagicMock(spec=ConfigManager)
        config.system = MagicMock()
        config.system.memory = MagicMock()
        config.system.memory.memory_limit = 0.8
        config.system.cache = MagicMock()
        config.system.cache.size = 1000
        config.system.cache.enable_memory_cache = True
        return config

    def test_resource_access_pattern(self, mock_config):
        """测试资源访问规范（规则56）"""
        manager = ResourceManager(mock_config)

        # 正确访问方式
        usage = manager.get_memory_usage()
        assert isinstance(usage, dict)

        # 错误访问方式应抛出异常
        with pytest.raises(TypeError): # Expect TypeError for missing __getitem__
            _ = manager['get_memory_usage']()  # type: ignore[misc] # 禁止字典式访问

    def test_no_fallback_mechanism(self, mock_config):
        """测试禁止降级机制（规则44）"""
        manager = ResourceManager(mock_config)

        # 模拟资源操作失败
        with patch('psutil.Process', side_effect=Exception("Resource error")), \
             pytest.raises(Exception, match="Resource error"):
            manager.get_memory_usage()  # 应直接抛出异常而非降级处理

    def test_interface_isolation(self, mock_config):
        """测试接口隔离原则（规则14）"""
        manager = ResourceManager(mock_config)

        # 验证不暴露内部实现细节
        with pytest.raises(AttributeError):
            _ = manager._cache  # type: ignore[attr-defined] # 禁止直接访问内部属性
