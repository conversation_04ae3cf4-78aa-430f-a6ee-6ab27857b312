"""生成器测试模块

相关模块:
1. 被测试模块:
   - src/models/gan/generator.py: 生成器实现
2. 依赖模块:
   - src/utils/config_manager.py: 配置管理
   - src/utils/cuda_manager.py: GPU资源管理
   - src/models/base/base_module.py: 基础模块
"""

from unittest.mock import MagicMock

import pytest
import torch
from torch import nn

from src.models.gan.components.feature_encoder import FeatureEncoder
from src.models.gan.generator import TimeSeriesGenerator
from src.utils.config_manager import ConfigManager


# 创建继承自nn.Module的模拟类
class MockModule(nn.Module):
    def __init__(self, return_value):
        super().__init__()
        self.return_value = return_value

    def forward(self, *_args, **_kwargs):
        return self.return_value
from src.models.gan.components.noise_processor import NoiseProcessor
from src.models.gan.components.sequence_generator import SequenceGenerator


@pytest.fixture
def sample_config():
    """创建测试配置"""
    config = ConfigManager.from_yaml("tests/test_config.yaml")
    # 创建必要的配置结构
    config.model = MagicMock()
    config.model.dimensions = MagicMock()
    config.model.dimensions.base_dim = 128
    config.model.noise_dim = 64

    # 如果需要data配置
    config.data = MagicMock()
    config.data.feature_dim = 20

    return config

@pytest.fixture
def sample_features():
    """创建测试特征数据"""
    return torch.randn(32, 100, 20)  # [batch_size, seq_len, feature_dim]

@pytest.fixture
def sample_noise():
    """创建测试噪声数据"""
    return torch.randn(32, 100, 64)  # [batch_size, seq_len, noise_dim]

@pytest.mark.batch2  # 生成器测试
class TestTimeSeriesGenerator:
    """测试时序生成器"""

    def test_initialization(self, sample_config):
        """测试初始化"""
        # 创建生成器
        generator = TimeSeriesGenerator(sample_config)

        # 验证组件初始化
        assert hasattr(generator, 'feature_encoder'), "缺少feature_encoder组件"
        assert hasattr(generator, 'noise_processor'), "缺少noise_processor组件"
        assert hasattr(generator, 'sequence_generator'), "缺少sequence_generator组件"

        # 验证组件类型
        assert isinstance(generator.feature_encoder, FeatureEncoder)
        assert isinstance(generator.noise_processor, NoiseProcessor)
        assert isinstance(generator.sequence_generator, SequenceGenerator)

        # 验证配置传递
        assert generator.config == sample_config

    def test_forward_pass(self, sample_config, sample_features, sample_noise):
        """测试前向传播"""
        # 创建生成器并移动到CPU
        generator = TimeSeriesGenerator(sample_config)
        generator = generator.to('cpu')

        # 确保输入也在CPU上
        sample_features = sample_features.to('cpu')
        sample_noise = sample_noise.to('cpu')

        # 执行前向传播
        output = generator(sample_features, sample_noise)

        # 验证输出形状
        assert isinstance(output, torch.Tensor), "输出应该是张量"
        assert output.shape == (sample_features.shape[0], sample_features.shape[1], 1), "输出形状不正确"

        # 验证输出数值有效
        assert torch.isfinite(output).all(), "输出包含非有限值"

    def test_noise_input(self, sample_config, sample_features, sample_noise):
        """测试噪声输入处理"""
        # 创建生成器并移动到CPU
        generator = TimeSeriesGenerator(sample_config)
        generator = generator.to('cpu')

        # 确保输入也在CPU上
        sample_features = sample_features.to('cpu')
        sample_noise = sample_noise.to('cpu')

        # 创建模拟噪声处理器（继承自nn.Module）
        mock_return = torch.zeros(sample_noise.shape[0], sample_noise.shape[1], 256, device='cpu')
        mock_noise_processor = MockModule(mock_return)

        # 替换噪声处理器
        generator.noise_processor = mock_noise_processor # type: ignore[assignment]

        # 执行前向传播
        output = generator(sample_features, sample_noise)

        # 验证输出形状
        assert output.shape == (sample_features.shape[0], sample_features.shape[1], 1), "输出形状不正确"

    def test_feature_encoding(self, sample_config, sample_features, sample_noise):
        """测试特征编码"""
        # 创建生成器并移动到CPU
        generator = TimeSeriesGenerator(sample_config)
        generator = generator.to('cpu')

        # 确保输入也在CPU上
        sample_features = sample_features.to('cpu')
        sample_noise = sample_noise.to('cpu')

        # 创建模拟特征编码器（继承自nn.Module）
        mock_return = torch.zeros(sample_features.shape[0], sample_features.shape[1], 256, device='cpu')
        mock_feature_encoder = MockModule(mock_return)

        # 替换特征编码器
        generator.feature_encoder = mock_feature_encoder # type: ignore[assignment]

        # 执行前向传播
        output = generator(sample_features, sample_noise)

        # 验证输出形状
        assert output.shape == (sample_features.shape[0], sample_features.shape[1], 1), "输出形状不正确"

    def test_multi_scale_generation(self, sample_config, sample_features, sample_noise):
        """测试多尺度生成"""
        # 创建生成器并移动到CPU
        generator = TimeSeriesGenerator(sample_config)
        generator = generator.to('cpu')

        # 确保输入也在CPU上
        sample_features = sample_features.to('cpu')
        sample_noise = sample_noise.to('cpu')

        # 创建模拟序列生成器（继承自nn.Module）
        mock_return = torch.zeros(sample_features.shape[0], sample_features.shape[1], 1, device='cpu')
        mock_sequence_generator = MockModule(mock_return)

        # 替换序列生成器
        generator.sequence_generator = mock_sequence_generator # type: ignore[assignment]

        # 执行前向传播
        output = generator(sample_features, sample_noise)

        # 验证输出形状
        assert output.shape == (sample_features.shape[0], sample_features.shape[1], 1), "输出形状不正确"

    def test_gradient_flow(self, sample_config, sample_features, sample_noise):
        """测试梯度流动"""
        # 创建生成器并移动到CPU
        generator = TimeSeriesGenerator(sample_config)
        generator = generator.to('cpu')

        # 确保输入也在CPU上
        sample_features = sample_features.to('cpu')
        sample_noise = sample_noise.to('cpu')

        # 执行前向传播
        output = generator(sample_features, sample_noise)

        # 计算梯度
        loss = output.mean()
        loss.backward()

        # 验证梯度存在
        # 注意：并非所有参数都会有梯度，只检查有梯度的参数数量
        grad_params = [param for param in generator.parameters() if param.grad is not None]
        assert len(grad_params) > 0, "没有参数有梯度"

        # 验证梯度有效
        for param in grad_params:
            # The list comprehension above ensures param.grad is not None here.
            assert torch.isfinite(param.grad).all(), "梯度包含非有限值"  # type: ignore[arg-type] # Pylance false positive, None already filtered

    def test_device_compatibility(self, sample_config, sample_features, sample_noise):
        """测试设备兼容性"""
        # 跳过GPU测试，如果不可用
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过GPU测试")

        # 创建生成器
        generator = TimeSeriesGenerator(sample_config)

        # 将模型移动到GPU
        generator = generator.to('cuda')

        # 将输入移动到GPU
        cuda_features = sample_features.to('cuda')
        cuda_noise = sample_noise.to('cuda')

        # 执行前向传播
        output = generator(cuda_features, cuda_noise)

        # 验证输出在GPU上
        assert output.device.type == 'cuda', "输出应该在GPU上"

    def test_serialization(self, sample_config):
        """测试模型序列化"""
        import os
        import tempfile

        # 创建生成器
        generator = TimeSeriesGenerator(sample_config)

        # 保存模型
        with tempfile.NamedTemporaryFile(suffix='.pt', delete=False) as tmp:
            model_path = tmp.name
            torch.save(generator.state_dict(), model_path)

        try:
            # 创建新的生成器
            new_generator = TimeSeriesGenerator(sample_config)

            # 加载模型
            new_generator.load_state_dict(torch.load(model_path))

            # 验证参数相同
            for p1, p2 in zip(generator.parameters(), new_generator.parameters(), strict=False):
                assert torch.allclose(p1, p2), "加载后的参数与原参数不同"
        finally:
            # 清理临时文件
            if os.path.exists(model_path):
                os.remove(model_path)
