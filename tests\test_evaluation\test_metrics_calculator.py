"""指标计算器测试模块

相关模块:
1. 被测试模块:
   - src/evaluation/metrics_calculator.py: 指标计算器实现
2. 依赖模块:
   - torch: 张量计算
   - src/utils/logger.py: 日志系统
"""

from unittest.mock import MagicMock, patch

import numpy as np
import pytest
import torch

from src.evaluation.metrics_calculator import MetricsCalculator


@pytest.fixture
def sample_predictions():
    """创建测试预测数据

    生成具有已知特征的预测序列:
    1. 线性增长序列
    2. 正弦波序列
    3. 随机波动序列
    """
    # 创建基础序列
    t = torch.linspace(0, 4*np.pi, 100)

    # 线性序列
    linear = torch.linspace(0, 1, 100).unsqueeze(0).unsqueeze(-1)

    # 正弦序列
    sine = torch.sin(t).unsqueeze(0).unsqueeze(-1)

    # 随机序列
    torch.manual_seed(42)
    random = torch.randn(1, 100, 1)

    # 组合所有序列
    return torch.cat([linear, sine, random], dim=0)  # [3, 100, 1]

@pytest.fixture
def sample_targets():
    """创建测试目标数据

    基于预测数据创建目标序列，添加已知的偏差:
    1. 线性序列加固定偏差
    2. 正弦序列加相位偏移
    3. 随机序列加噪声
    """
    # 创建基础序列
    t = torch.linspace(0, 4*np.pi, 100)

    # 线性序列(加0.1的偏差)
    linear = torch.linspace(0.1, 1.1, 100).unsqueeze(0).unsqueeze(-1)

    # 正弦序列(加π/4的相位偏移)
    sine = torch.sin(t + np.pi/4).unsqueeze(0).unsqueeze(-1)

    # 随机序列(加不同的噪声)
    torch.manual_seed(43)
    random = torch.randn(1, 100, 1)

    return torch.cat([linear, sine, random], dim=0)  # [3, 100, 1]

@pytest.fixture
def sample_config():
    """创建测试配置"""
    config = MagicMock()
    config.evaluation = MagicMock()
    config.evaluation.metrics = ["mse", "mae", "rmse"]
    return config

@pytest.mark.batch3  # 指标计算器测试
class TestMetricsCalculator:
    """测试指标计算器"""

    def test_initialization(self, sample_config):
        """测试初始化"""
        calculator = MetricsCalculator(sample_config)

        assert "mse" in calculator.metrics_list
        assert "rmse" in calculator.metrics_list
        assert "mae" in calculator.metrics_list

    def test_mse_calculation(self, sample_predictions, sample_targets, sample_config):
        """测试均方误差计算"""
        calculator = MetricsCalculator(sample_config)
        mse = calculator.calculate_mse(sample_predictions, sample_targets)

        # 验证MSE基本属性
        assert isinstance(mse, float)
        assert mse >= 0

        # 验证已知偏差的MSE
        # 线性序列的MSE应该是0.01 (0.1的固定偏差的平方)
        linear_mse = calculator.calculate_mse(
            sample_predictions[0:1], sample_targets[0:1]
        )
        assert np.isclose(linear_mse, 0.01, atol=1e-3)

    def test_mae_calculation(self, sample_predictions, sample_targets, sample_config):
        """测试平均绝对误差计算"""
        calculator = MetricsCalculator(sample_config)
        mae = calculator.calculate_mae(sample_predictions, sample_targets)

        # 验证MAE基本属性
        assert isinstance(mae, float)
        assert mae >= 0

        # 验证已知偏差的MAE
        # 线性序列的MAE应该是0.1
        linear_mae = calculator.calculate_mae(
            sample_predictions[0:1], sample_targets[0:1]
        )
        assert np.isclose(linear_mae, 0.1, atol=1e-3)

    def test_rmse_calculation(self, sample_predictions, sample_targets, sample_config):
        """测试均方根误差计算"""
        calculator = MetricsCalculator(sample_config)
        rmse = calculator.calculate_rmse(sample_predictions, sample_targets)

        # 验证RMSE基本属性
        assert isinstance(rmse, float)
        assert rmse >= 0

        # 验证RMSE ≥ MAE
        mae = calculator.calculate_mae(sample_predictions, sample_targets)
        assert rmse >= mae

    # 移除趋势准确率测试

    # def test_trend_duration(self, sample_predictions, sample_targets):
    #     """测试趋势持续时间计算"""
    #     calculator = MetricsCalculator()
    #     duration_sim = calculator.calculate_trend_duration(
    #         sample_predictions, sample_targets
    #     )
    #
    #     # 验证相似度范围
    #     assert -1 <= duration_sim <= 1
    #
    #     # 线性序列应该有完全匹配的趋势持续时间
    #     linear_duration = calculator.calculate_trend_duration(
    #         sample_predictions[0:1], sample_targets[0:1]
    #     )
    #     assert np.isclose(linear_duration, 1.0, atol=1e-3)
    #
    # def test_feature_similarity(self, sample_predictions, sample_targets):
    #     """测试特征相似度计算"""
    #     calculator = MetricsCalculator()
    #     similarity = calculator.calculate_feature_similarity(
    #         sample_predictions, sample_targets
    #     )
    #
    #     # 验证相似度范围
    #     assert -1 <= similarity <= 1
    #
    #     # 相同序列的相似度应该为1
    #     self_similarity = calculator.calculate_feature_similarity(
    #         sample_predictions, sample_predictions
    #     )
    #     assert np.isclose(self_similarity, 1.0, atol=1e-3)
    #
    # def test_prediction_confidence(self, sample_predictions, sample_targets):
    #     """测试预测置信度计算"""
    #     calculator = MetricsCalculator()
    #     confidence = calculator.calculate_prediction_confidence(
    #         sample_predictions, sample_targets
    #     )
    #
    #     # 验证置信度范围
    #     assert 0 <= confidence <= 1
    #
    #     # 相同序列的置信度应该为1
    #     self_confidence = calculator.calculate_prediction_confidence(
    #         sample_predictions, sample_predictions
    #     )
    #     assert np.isclose(self_confidence, 1.0, atol=1e-3)

    def test_multiple_metrics(self, sample_predictions, sample_targets, sample_config):
        """测试多指标计算"""
        calculator = MetricsCalculator(sample_config)
        metrics = calculator.calculate_metrics(
            sample_predictions,
            sample_targets,
            metrics_list=["mse", "mae", "rmse"]
        )

        # 验证返回格式
        assert isinstance(metrics, dict)
        assert all(k in metrics for k in ["mse", "mae", "rmse"])
        assert all(isinstance(v, float) for v in metrics.values())

    def test_error_handling(self, sample_config):
        """测试错误处理"""
        calculator = MetricsCalculator(sample_config)

        # 测试维度不匹配
        with pytest.raises(ValueError):
            calculator.calculate_metrics(
                torch.randn(10, 5),  # [batch, features]
                torch.randn(10)      # [batch]
            )

        # 测试空输入
        with pytest.raises(ValueError):
            calculator.calculate_metrics(
                torch.tensor([]),
                torch.tensor([])
            )

        # 测试无效指标
        metrics = calculator.calculate_metrics(
            torch.randn(10, 1),
            torch.randn(10, 1),
            metrics_list=["invalid_metric"]
        )
        assert len(metrics) == 0

    def test_config_loading(self, sample_config):
        """测试配置加载"""
        calculator = MetricsCalculator(sample_config)

        # 验证配置是否正确加载
        # 只验证支持的指标是否包含在指标列表中
        for metric in ["mse", "rmse", "mae"]:
            assert metric in calculator.metrics_list

    @patch('src.evaluation.metrics_calculator.get_logger')
    def test_logging(self, mock_logger, sample_predictions, sample_targets, sample_config):
        """测试日志记录"""
        mock_logger.return_value = MagicMock()

        calculator = MetricsCalculator(sample_config)
        calculator.calculate_metrics(sample_predictions, sample_targets)

        # 验证日志调用
        assert mock_logger.called
        assert mock_logger.return_value.debug.called
