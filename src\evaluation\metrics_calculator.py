"""指标计算器模块 - 提供各种评估指标的计算功能

本模块实现了各种评估指标的计算功能，包括：
1. 回归指标 (MSE, RMSE)
2. 趋势准确率 (trend_accuracy)
3. 其他指标 (已弃用)

主要用途：
- 模型评估
- 预测结果分析
- 性能比较
"""


import torch
import torch.nn.functional as f

from src.utils.logger import get_logger


class MetricsCalculator:
    """指标计算器 - 负责计算各种评估指标"""

    def __init__(self, config):
        """初始化指标计算器

        Args:
            config: 配置对象，不能为None

        Raises:
            ValueError: 如果config为None
        """
        self.logger = get_logger(self.__class__.__name__)

        # 验证配置不为None
        if config is None:
            error_msg = "指标计算器配置不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.config = config

        # 初始化指标参数 (将在_configure_metrics中设置)
        self.metrics_list = []

        # 配置指标计算器
        self._configure_metrics()

        self.logger.info("指标计算器初始化完成")

    def _configure_metrics(self):
        """配置指标计算器

        Raises:
            ValueError: 如果缺少必要的配置项
        """
        try:
            # 获取评估配置
            if not hasattr(self.config, 'evaluation'):
                error_msg = "配置中缺少evaluation部分"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            evaluation_config = self.config.evaluation

            # 更新指标列表
            if not hasattr(evaluation_config, 'metrics'):
                error_msg = "配置中缺少evaluation.metrics定义"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 获取指标列表
            metrics = evaluation_config.metrics
            if metrics is None:
                error_msg = "配置中的evaluation.metrics不能为None"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 确保指标列表只包含支持的指标
            supported_metrics = ["mse", "rmse", "mae"]
            valid_metrics = [metric for metric in metrics if metric in supported_metrics]

            # 验证至少有一个有效指标
            if not valid_metrics:
                error_msg = f"配置的指标列表不包含任何支持的指标，支持的指标: {supported_metrics}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            self.metrics_list = valid_metrics
            self.logger.debug(f"从配置中加载指标列表: {self.metrics_list}")
        except Exception as e:
            error_msg = f"加载指标计算器配置失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

    def calculate_metrics(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor,
        metrics_list: list[str] | None = None
    ) -> dict[str, float]:
        """计算评估指标

        Args:
            predictions: 预测值 [batch_size, seq_length, ...]
            targets: 目标值 [batch_size, seq_length, ...]
            metrics_list: 指标列表，如果为None则使用默认列表

        Returns:
            Dict[str, float]: 指标字典

        Raises:
            ValueError: 如果预测和目标维度不匹配
        """
        # 验证输入维度
        if predictions.dim() != targets.dim():
            raise ValueError(
                f"预测和目标维度不匹配: 预测维度{predictions.dim()}D, 目标维度{targets.dim()}D"
            )

        if predictions.shape[:-1] != targets.shape[:-1]:
            raise ValueError(
                f"预测和目标形状不匹配: 预测形状{predictions.shape}, 目标形状{targets.shape}"
            )

        # --- 添加空输入验证 ---
        if predictions.numel() == 0 or targets.numel() == 0:
            raise ValueError("输入张量 predictions 和 targets 不能为空")
        # --- 验证结束 ---

        # 记录输入参数
        self.logger.debug(
            f"开始计算指标\n"
            f"- 预测形状: {predictions.shape}\n"
            f"- 目标形状: {targets.shape}\n"
            f"- 指标列表: {metrics_list if metrics_list else '默认'}"
        )

        # 使用默认指标列表
        if metrics_list is None:
            metrics_list = self.metrics_list

        # 初始化指标字典
        metrics = {}

        # 计算各种指标
        for metric_name in metrics_list:
            if metric_name == "mse":
                metrics["mse"] = self.calculate_mse(predictions, targets)
            elif metric_name == "rmse":
                metrics["rmse"] = self.calculate_rmse(predictions, targets)
            elif metric_name == "mae":
                metrics["mae"] = self.calculate_mae(predictions, targets)
            # 移除对smape和trend_accuracy的处理
            else:
                self.logger.warning(f"不支持的指标: {metric_name}")

        return metrics

    def calculate_mse(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> float:
        """计算均方误差

        Args:
            predictions: 预测值 [batch_size, seq_length, ...]
            targets: 目标值 [batch_size, seq_length, ...]

        Returns:
            float: 均方误差
        """
        return f.mse_loss(predictions, targets).item()

    def calculate_rmse(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> float:
        """计算均方根误差

        Args:
            predictions: 预测值 [batch_size, seq_length, ...]
            targets: 目标值 [batch_size, seq_length, ...]

        Returns:
            float: 均方根误差
        """
        # 确保预测和目标维度一致
        if predictions.dim() == 3 and targets.dim() == 3:
            predictions = predictions.squeeze(-1)  # [batch_size, seq_length, 1] -> [batch_size, seq_length]
            targets = targets.squeeze(-1)

        # 计算均方误差并开方
        return torch.sqrt(f.mse_loss(predictions, targets)).item()

    # 移除calculate_trend_accuracy方法

    def calculate_mae(
        self,
        predictions: torch.Tensor,
        targets: torch.Tensor
    ) -> float:
        """计算平均绝对误差 (MAE)

        Args:
            predictions: 预测值 [batch_size, seq_length, ...]
            targets: 目标值 [batch_size, seq_length, ...]

        Returns:
            float: 平均绝对误差
        """
        # 使用 L1 Loss 计算 MAE
        return f.l1_loss(predictions, targets).item()

    # 移除calculate_smape方法
