# 时间序列预测系统使用指南

本文档详细介绍了时间序列预测系统的各种运行模式和使用方法。

## 系统概述

本系统是一个基于GAN的时间序列预测系统，专注于预测value15指标。系统提供了完整的数据处理、模型训练、评估和预测功能，通过分析其他特征的先行信号来预测value15的未来变化。

## 运行模式

系统提供了四种主要的运行模式，可以通过命令行参数 `--mode` 来指定：

### 1. 数据处理模式 (process)

数据处理模式专门用于执行数据处理流水线，而不进行训练或预测。这个模式对于数据探索、特征工程实验和数据质量检查非常有用。

#### 命令格式

```bash
# 执行数据处理并保存结果到指定目录
python main.py --mode process --config config.yaml --output_dir my_processed_data --save_processed
```

#### 数据处理流程

数据处理流程包括以下8个主要步骤：

1. **数据加载**：从原始数据源加载数据
2. **数据验证**：验证数据质量和完整性
3. **数据清洗**：处理缺失值和异常值
4. **特征编码**：对分类特征进行编码
5. **特征工程**：创建新的衍生特征
6. **特征选择**：选择最相关的特征子集
7. **滑动窗口创建**：创建时间序列窗口
8. **模型输入准备**：准备模型所需的输入格式

#### 输出文件

当使用 `--save_processed` 参数时，系统会生成以下文件：

- **processed_data.npz**：NumPy压缩格式的处理后数据，包含特征和目标数组
- **processed_data.pkl**：Python Pickle格式的完整数据，包含特征、目标和元数据
- **README.txt**：详细的数据处理结果说明，包括数据形状、处理步骤和使用方法

### 2. 训练模式 (train)

训练模式用于训练GAN模型，以便进行时间序列预测。

#### 命令格式

```bash
# 使用指定配置文件训练模型
python main.py --mode train --config custom_config.yaml

# 从检查点恢复训练
python main.py --mode train --config custom_config.yaml --resume --checkpoint outputs/models/GAN_epoch_5.pt
```

#### 训练流程

训练过程包括以下主要步骤：

1. 加载配置和初始化资源
2. 数据准备（执行完整的数据处理流水线）
3. 模型初始化（生成器和判别器）
4. 训练循环
   - 生成器训练
   - 判别器训练
   - 验证评估
   - 检查点保存
5. 最终模型保存

#### 训练参数

可以通过修改 `config.yaml` 中的 `training` 部分来自定义训练参数：

```yaml
training:
  batch_size: 128              # 批大小
  epochs: 100                  # 训练轮数
  learning_rate: 0.001         # 学习率
  beta1: 0.5                   # Adam优化器参数
  beta2: 0.999                 # Adam优化器参数
```

### 3. 评估模式 (evaluate)

评估模式用于评估已训练模型的性能。

#### 命令格式

```bash
# 评估指定模型
python main.py --mode evaluate --config config.yaml --model outputs/models/GAN_epoch_10.pt
```

#### 评估指标

系统计算以下评估指标：

- **MSE（均方误差）**：预测值与实际值差异的平方的平均值
- **MAE（平均绝对误差）**：预测值与实际值绝对差异的平均值
- **RMSE（均方根误差）**：MSE的平方根，与原始数据单位相同
- **MAPE（平均绝对百分比误差）**：相对误差的平均值，以百分比表示
- **R²（决定系数）**：模型解释的方差比例，1表示完美拟合

#### 输出结果

评估结果将保存在 `outputs/results/evaluation_results.txt` 文件中，同时在控制台显示。

### 4. 预测模式 (predict)

预测模式用于使用训练好的模型进行时间序列预测。

#### 命令格式

```bash
# 对指定输入数据进行预测并保存到指定位置
python main.py --mode predict --config config.yaml --model outputs/models/GAN_epoch_10.pt --input data/test.csv --output outputs/predictions
```

#### 预测流程

预测过程包括以下主要步骤：

1. 加载配置和模型
2. 数据准备（执行数据处理流水线）
3. 模型预测
4. 结果保存

#### 输出结果

预测结果将保存为NumPy数组格式（.npy文件），可以使用以下代码加载：

```python
import numpy as np
predictions = np.load('outputs/results/predictions.npy')
```

## 配置文件说明

系统使用 `config.yaml` 进行配置，主要配置项包括：

### 数据配置

```yaml
data:
  window_size: 16              # 时间窗口大小
  stride: 8                    # 滑动步长
  train_ratio: 0.8             # 训练集比例
  correlation_threshold: 0.5   # 特征选择相关性阈值
```

### 模型配置

```yaml
model:
  generator:
    hidden_dim: 128            # 生成器隐藏层维度
    num_layers: 2              # 生成器层数
  discriminator:
    hidden_dim: 64             # 判别器隐藏层维度
    num_layers: 2              # 判别器层数
```

### 训练配置

```yaml
training:
  batch_size: 128              # 批大小
  epochs: 100                  # 训练轮数
  learning_rate: 0.001         # 学习率
  beta1: 0.5                   # Adam优化器参数
  beta2: 0.999                 # Adam优化器参数
```

### 路径配置

```yaml
paths:
  raw_data: data/raw/combined_data.csv    # 原始数据路径
  model_dir: outputs/models               # 模型保存目录
  log_dir: outputs/logs                   # 日志保存目录
  results_dir: outputs/results            # 结果保存目录
```

## 常见问题解答

### 内存使用率超过限制

如果遇到"内存使用率超过限制"的错误，可以尝试：

1. 减小批大小（batch_size）
2. 减小窗口大小（window_size）
3. 增加滑动步长（stride）
4. 减少特征数量

### 模型训练不稳定

GAN模型训练可能不稳定，可以尝试：

1. 调整学习率
2. 修改判别器和生成器的更新频率
3. 使用梯度裁剪
4. 尝试不同的损失函数

### 预测结果不准确

如果预测结果不准确，可以尝试：

1. 增加训练轮数
2. 调整模型复杂度
3. 添加更多相关特征
4. 尝试不同的特征工程方法

## 高级用法

### 自定义数据处理

可以通过修改 `src/data/` 目录下的相关模块来自定义数据处理流程。主要的数据处理模块包括：

- `data_pipeline.py`：数据处理流水线协调器
- `data_loader.py`：数据加载模块
- `data_validator.py`：数据验证模块
- `feature_engineer.py`：特征工程模块
- `feature_selector.py`：特征选择模块

### 自定义模型架构

可以通过修改 `src/models/gan/` 目录下的相关模块来自定义模型架构。主要的模型模块包括：

- `gan_model.py`：GAN模型定义
- `generator.py`：生成器网络
- `discriminator.py`：判别器网络
- `trainer.py`：训练器

### 自定义评估指标

可以通过修改 `src/models/gan/gan_evaluator.py` 来自定义评估指标。
