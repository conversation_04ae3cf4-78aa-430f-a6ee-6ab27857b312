"""
高级优化功能测试模块

测试超参数优化模块的高级功能，包括：
1. 参数一致性检查
2. 配置加载与验证
3. 异常处理与剪枝
4. 日志记录完整性
5. 参数组合有效性
"""

from unittest.mock import MagicMock, patch

import optuna
import pytest
import torch

# Mock logger early to prevent issues during import
with patch('src.utils.logger.LoggerFactory') as mock_logger_factory:
    mock_logger_factory.return_value = MagicMock()

# Import necessary components AFTER mocking logger
from src.optimization.exceptions import (
    ConfigurationError,
    InvalidConfigValueError,
    MissingConfigError,
)
from src.optimization.hyperparameter_optimizer import objective, run_optimization
from src.optimization.validation import verify_parameter_consistency
from src.utils.config_manager import ConfigManager

# --- Mock Classes ---

# 创建一个模拟的ConfigManager类
class MockConfigManager(ConfigManager):
    """模拟ConfigManager类，用于测试"""

    def __init__(self, model=None, training=None, data=None):
        """初始化模拟配置管理器"""
        # 创建模型配置
        self.model = type('ModelConfig', (), model or {})()

        # 创建训练配置
        self.training = type('TrainingConfig', (), training or {})()

        # 创建数据配置
        self.data = type('DataConfig', (), data or {})()

        # 添加_config_dict属性
        self._config_dict = {
            'model': model or {},
            'training': training or {},
            'data': data or {}
        }

        # 添加其他必需的属性
        self.version = "1.0.0"
        self.paths = type('PathsConfig', (), {})()
        self.system = type('SystemConfig', (), {})()
        self.logging = type('LoggingConfig', (), {})()
        self.feature_engineering = {}
        self.evaluation = {}
        self.prediction = {}

    @classmethod
    def from_yaml(cls, yaml_path=None):
        """模拟从YAML文件加载配置"""
        # 忽略yaml_path参数
        return cls()

    def validate(self):
        """模拟配置验证"""
        pass

# --- 1. 参数一致性检查测试 ---

def test_verify_parameter_consistency_success():
    """测试参数一致性检查在参数一致时通过"""
    # 创建一个模拟的配置对象
    config = MockConfigManager(
        model={"capacity": "medium", "use_self_attention": True},
        training={"lr_balancer": {"target_ratio": 0.7}}
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "model.capacity": "medium",
        "model.use_self_attention": True,
        "training.lr_balancer.target_ratio": 0.7
    }

    # 参数一致，不应该抛出异常
    verify_parameter_consistency(trial, config)

def test_verify_parameter_consistency_path_not_exist():
    """测试参数一致性检查在参数路径不存在时抛出异常"""
    # 创建一个模拟的配置对象
    config = MockConfigManager(
        model={"capacity": "medium"}
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "model.capacity": "medium",
        "model.non_existent_param": "value"  # 不存在的参数
    }

    # 参数路径不存在，应该抛出异常
    with pytest.raises(ConfigurationError, match="参数路径.*不存在"):
        verify_parameter_consistency(trial, config)

def test_verify_parameter_consistency_value_mismatch():
    """测试参数一致性检查在参数值不匹配时抛出异常"""
    # 创建一个模拟的配置对象
    config = MockConfigManager(
        model={"capacity": "medium"}
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "model.capacity": "large"  # 值不匹配
    }

    # 参数值不匹配，应该抛出异常
    with pytest.raises(ConfigurationError, match="值不一致"):
        verify_parameter_consistency(trial, config)

def test_verify_parameter_consistency_nested_params():
    """测试参数一致性检查处理嵌套参数结构"""
    # 创建一个模拟的配置对象，包含嵌套结构
    config = MockConfigManager(
        training={"lr_balancer": {"target_ratio": 0.7, "min_lr": 1e-6}}
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "training.lr_balancer.target_ratio": 0.7,
        "training.lr_balancer.min_lr": 1e-6
    }

    # 嵌套参数一致，不应该抛出异常
    verify_parameter_consistency(trial, config)

def test_verify_parameter_consistency_special_values():
    """测试参数一致性检查处理特殊值（None、空字符串、空列表等）"""
    # 创建一个模拟的配置对象，包含特殊值
    config = MockConfigManager(
        model={"empty_str": "", "none_val": None, "empty_list": []}
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "model.empty_str": "",
        "model.none_val": None,
        "model.empty_list": []
    }

    # 特殊值一致，不应该抛出异常
    verify_parameter_consistency(trial, config)

# --- 3. 配置加载与验证测试 ---

def test_config_file_not_exist():
    """测试配置文件不存在时的错误处理"""
    with pytest.raises(FileNotFoundError, match="基础配置文件不存在"):
        run_optimization(base_config_path="non_existent_config.yaml")

@patch('os.path.exists', return_value=True)  # 模拟文件存在
@patch('src.optimization.hyperparameter_optimizer.objective')
def test_config_format_error(mock_objective, _):
    """测试配置文件格式错误时的错误处理"""
    # 模拟objective函数，使其抛出TrialPruned异常
    mock_objective.side_effect = optuna.exceptions.TrialPruned("Invalid YAML format")

    # 直接模拟optuna.create_study和study.optimize
    with patch('optuna.create_study') as mock_create_study:
        # 创建一个模拟的study对象
        mock_study = MagicMock()
        mock_study.best_trial = None  # 没有最佳试验
        mock_create_study.return_value = mock_study

        # 运行优化
        result = run_optimization(base_config_path="invalid_format.yaml", n_trials=1)

        # 验证study.optimize被调用
        mock_study.optimize.assert_called_once()

        # 验证返回结果为None（没有最佳参数）
        assert result is None

@patch('os.path.exists', return_value=True)  # 模拟文件存在
@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_config_missing_required_fields(mock_from_yaml, _):
    """测试配置文件缺少必需字段时的错误处理"""
    # 创建一个缺少必需字段的配置对象
    incomplete_config = MockConfigManager()
    mock_from_yaml.return_value = incomplete_config

    # 模拟验证函数，检测缺少字段时抛出异常
    with patch('src.optimization.hyperparameter_optimizer.objective') as mock_objective:
        mock_objective.side_effect = MissingConfigError("缺少必需的配置字段")

        with pytest.raises(MissingConfigError):
            run_optimization(base_config_path="incomplete_config.yaml", n_trials=1)

@patch('os.path.exists', return_value=True)  # 模拟文件存在
@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_config_field_type_error(mock_from_yaml, _):
    """测试配置文件字段类型错误时的错误处理"""
    # 创建一个字段类型错误的配置对象
    invalid_config = MockConfigManager(
        training={"num_epochs": "not_an_integer"}  # 应该是整数
    )
    mock_from_yaml.return_value = invalid_config

    # 模拟验证函数，检测字段类型错误时抛出异常
    with patch('src.optimization.hyperparameter_optimizer.objective') as mock_objective:
        mock_objective.side_effect = InvalidConfigValueError("配置字段类型错误")

        with pytest.raises(InvalidConfigValueError):
            run_optimization(base_config_path="invalid_config.yaml", n_trials=1)

# --- 7. 异常处理与剪枝测试 ---

@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_cuda_oom_exception_handling(mock_from_yaml):
    """测试训练过程中的CUDA OOM异常处理"""
    # 创建一个模拟的配置对象
    config = MockConfigManager()
    mock_from_yaml.return_value = config

    # 创建一个模拟的trial对象
    trial = MagicMock()

    # 创建一个模拟的PipelineRunner对象
    mock_runner = MagicMock()
    mock_runner.train.side_effect = torch.cuda.OutOfMemoryError("CUDA out of memory")

    # 模拟PipelineRunner的创建和参数一致性检查
    with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
         patch('src.optimization.validation.verify_parameter_consistency'), \
         pytest.raises(optuna.exceptions.TrialPruned):
        # 应该将CUDA OOM异常转换为TrialPruned异常
        objective(trial, base_config_path="dummy.yaml")

    # 验证清理资源被调用
    mock_runner.cleanup.assert_called_once()

@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_config_validation_exception_handling(mock_from_yaml):
    """测试配置验证失败异常处理"""
    # 创建一个模拟的配置对象
    config = MockConfigManager()
    mock_from_yaml.return_value = config

    # 创建一个模拟的trial对象
    trial = MagicMock()

    # 模拟参数一致性检查抛出异常
    with patch('src.optimization.validation.verify_parameter_consistency') as mock_verify:
        mock_verify.side_effect = ConfigurationError("配置验证失败")

        # 应该将ConfigurationError异常转换为TrialPruned异常
        with pytest.raises(optuna.exceptions.TrialPruned):
            objective(trial, base_config_path="dummy.yaml")

@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_data_loading_exception_handling(mock_from_yaml):
    """测试数据加载失败异常处理"""
    # 创建一个模拟的配置对象
    config = MockConfigManager()
    mock_from_yaml.return_value = config

    # 创建一个模拟的trial对象
    trial = MagicMock()

    # 创建一个模拟的PipelineRunner对象
    mock_runner = MagicMock()
    mock_runner.train.side_effect = FileNotFoundError("数据文件不存在")

    # 模拟PipelineRunner的创建和参数一致性检查
    with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
         patch('src.optimization.validation.verify_parameter_consistency'), \
         pytest.raises(optuna.exceptions.TrialPruned):
        # 应该将FileNotFoundError异常转换为TrialPruned异常
        objective(trial, base_config_path="dummy.yaml")

    # 验证清理资源被调用
    mock_runner.cleanup.assert_called_once()

@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_network_structure_exception_handling(mock_from_yaml):
    """测试网络结构不兼容异常处理"""
    # 创建一个模拟的配置对象
    config = MockConfigManager()
    mock_from_yaml.return_value = config

    # 创建一个模拟的trial对象
    trial = MagicMock()

    # 创建一个模拟的PipelineRunner对象
    mock_runner = MagicMock()
    mock_runner.train.side_effect = RuntimeError("网络结构不兼容")

    # 模拟PipelineRunner的创建和参数一致性检查
    with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
         patch('src.optimization.validation.verify_parameter_consistency'), \
         pytest.raises(optuna.exceptions.TrialPruned):
        # 应该将RuntimeError异常转换为TrialPruned异常
        objective(trial, base_config_path="dummy.yaml")

    # 验证清理资源被调用
    mock_runner.cleanup.assert_called_once()

# --- 8. 日志记录完整性测试 ---

@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_log_levels(mock_from_yaml):
    """测试不同日志级别的输出"""
    # 创建一个模拟的配置对象
    config = MockConfigManager()
    mock_from_yaml.return_value = config

    # 创建一个模拟的logger对象
    mock_logger = MagicMock()

    # 替换hyperparameter_optimizer模块中的logger
    with patch('src.optimization.hyperparameter_optimizer.logger', mock_logger):
        # 创建一个模拟的trial对象
        trial = MagicMock()
        trial.number = 1

        # 创建一个模拟的PipelineRunner对象
        mock_runner = MagicMock()
        mock_runner.train.return_value = {"history": {"val_mae": 0.5}}

        # 模拟PipelineRunner的创建、参数一致性检查和extract_metric函数
        with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
             patch('src.optimization.validation.verify_parameter_consistency'), \
             patch('src.optimization.hyperparameter_optimizer.extract_metric', return_value=0.5):
            # 执行objective函数
            objective(trial, base_config_path="dummy.yaml")

        # 验证不同级别的日志被记录
        assert mock_logger.info.call_count > 0
        assert mock_logger.debug.call_count >= 0  # debug可能被禁用

@patch('src.utils.config_manager.ConfigManager.from_yaml')
def test_exception_logging(mock_from_yaml):
    """测试异常情况下的详细日志"""
    # 创建一个模拟的配置对象
    config = MockConfigManager()
    mock_from_yaml.return_value = config

    # 创建一个模拟的logger对象
    mock_logger = MagicMock()

    # 替换hyperparameter_optimizer模块中的logger
    with patch('src.optimization.hyperparameter_optimizer.logger', mock_logger):
        # 创建一个模拟的trial对象
        trial = MagicMock()
        trial.number = 1

        # 创建一个模拟的PipelineRunner对象
        mock_runner = MagicMock()
        mock_runner.train.side_effect = RuntimeError("测试异常")

        # 模拟PipelineRunner的创建和参数一致性检查
        with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
             patch('src.optimization.validation.verify_parameter_consistency'), \
             pytest.raises(optuna.exceptions.TrialPruned):
            # 执行objective函数，应该捕获异常
            objective(trial, base_config_path="dummy.yaml")

        # 验证异常被记录
        mock_logger.error.assert_called()
        # 验证exc_info=True被传递，表示记录完整堆栈
        assert any(call_args[1].get('exc_info', False) for call_args in mock_logger.error.call_args_list)

def test_log_file_rotation():
    """测试日志文件轮转"""
    # 这个测试需要实际创建日志文件，可能需要在实际环境中手动测试
    # 这里只是一个示例框架
    pass

def test_log_file_permissions():
    """测试日志文件权限问题处理"""
    # 这个测试需要模拟文件权限问题，可能需要在实际环境中手动测试
    # 这里只是一个示例框架
    pass

# --- 9. 参数组合有效性测试 ---

def test_mutually_exclusive_params_detection():
    """测试互斥参数组合检测"""
    # 创建一个模拟的配置对象，包含互斥参数
    config = MockConfigManager(
        model={
            "use_self_attention": True,
            "use_cross_attention": True  # 假设这两个参数是互斥的
        }
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "model.use_self_attention": True,
        "model.use_cross_attention": True
    }

    # 模拟参数一致性检查函数，使其抛出互斥参数错误
    def mock_verify_consistency(trial, config):
        raise ConfigurationError("互斥参数同时启用")

    # 模拟参数一致性检查
    with patch('src.optimization.validation.verify_parameter_consistency',
               side_effect=mock_verify_consistency):
        # 创建一个模拟的PipelineRunner对象
        mock_runner = MagicMock()

        # 模拟PipelineRunner的创建和ConfigManager.from_yaml
        with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
             patch('src.utils.config_manager.ConfigManager.from_yaml', return_value=config), \
             pytest.raises(optuna.exceptions.TrialPruned):
            # 应该检测到互斥参数并抛出异常
            objective(trial, base_config_path="dummy.yaml")

def test_dependent_params_missing_detection():
    """测试依赖参数缺失检测"""
    # 创建一个模拟的配置对象，缺少依赖参数
    config = MockConfigManager(
        model={
            "use_self_attention": True,
            # 缺少n_heads参数，假设use_self_attention=True时需要n_heads
        }
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "model.use_self_attention": True
    }

    # 模拟参数一致性检查函数，使其抛出依赖参数缺失错误
    def mock_verify_consistency(trial, config):
        raise ConfigurationError("缺少依赖参数n_heads")

    # 模拟参数一致性检查
    with patch('src.optimization.validation.verify_parameter_consistency',
               side_effect=mock_verify_consistency):
        # 创建一个模拟的PipelineRunner对象
        mock_runner = MagicMock()

        # 模拟PipelineRunner的创建和ConfigManager.from_yaml
        with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
             patch('src.utils.config_manager.ConfigManager.from_yaml', return_value=config), \
             pytest.raises(optuna.exceptions.TrialPruned):
            # 应该检测到缺少依赖参数并抛出异常
            objective(trial, base_config_path="dummy.yaml")

def test_param_value_range_conflict_detection():
    """测试参数值范围冲突检测"""
    # 创建一个模拟的配置对象，参数值范围冲突
    config = MockConfigManager(
        training={
            "min_lr": 1e-3,
            "max_lr": 1e-4  # min_lr > max_lr，这是冲突的
        }
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "training.min_lr": 1e-3,
        "training.max_lr": 1e-4
    }

    # 模拟参数一致性检查函数，使其抛出参数值范围冲突错误
    def mock_verify_consistency(trial, config):
        raise ConfigurationError("参数值范围冲突: min_lr > max_lr")

    # 模拟参数一致性检查
    with patch('src.optimization.validation.verify_parameter_consistency',
               side_effect=mock_verify_consistency):
        # 创建一个模拟的PipelineRunner对象
        mock_runner = MagicMock()

        # 模拟PipelineRunner的创建和ConfigManager.from_yaml
        with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
             patch('src.utils.config_manager.ConfigManager.from_yaml', return_value=config), \
             pytest.raises(optuna.exceptions.TrialPruned):
            # 应该检测到参数值范围冲突并抛出异常
            objective(trial, base_config_path="dummy.yaml")

def test_param_combination_performance_prediction():
    """测试参数组合性能影响预测"""
    # 创建一个模拟的配置对象，包含可能导致性能问题的参数组合
    config = MockConfigManager(
        model={"capacity": "large"},
        training={"batch_size": 256}  # 假设large模型+大batch_size可能导致内存问题
    )

    # 创建一个模拟的trial对象
    trial = MagicMock()
    trial.params = {
        "model.capacity": "large",
        "training.batch_size": 256
    }

    # 模拟参数一致性检查函数，不抛出异常
    def mock_verify_consistency(trial, config):
        # 调整参数
        if hasattr(config.training, 'batch_size') and config.training.batch_size > 128:
            config.training.batch_size = 128

    # 模拟参数一致性检查
    with patch('src.optimization.validation.verify_parameter_consistency',
               side_effect=mock_verify_consistency):
        # 创建一个模拟的logger对象
        mock_logger = MagicMock()

        # 替换hyperparameter_optimizer模块中的logger
        with patch('src.optimization.hyperparameter_optimizer.logger', mock_logger):
            # 创建一个模拟的PipelineRunner对象
            mock_runner = MagicMock()
            mock_runner.train.return_value = {"history": {"val_mae": 0.5}}

            # 模拟PipelineRunner的创建、ConfigManager.from_yaml和extract_metric函数
            with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=mock_runner), \
                 patch('src.utils.config_manager.ConfigManager.from_yaml', return_value=config), \
                 patch('src.optimization.hyperparameter_optimizer.extract_metric', return_value=0.5):
                # 执行objective函数
                objective(trial, base_config_path="dummy.yaml")

            # 验证参数被调整
            assert getattr(config.training, 'batch_size', None) == 128

if __name__ == '__main__':
    pytest.main(['-v', __file__])

