"""
测试运行器

此脚本用于运行测试文件，确保配置文件可用。
"""

import argparse
import os
import sys

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

def run_test(test_module_name):
    """运行指定的测试模块"""
    print(f"运行测试模块: {test_module_name}")

    # 确保当前工作目录是项目根目录
    os.chdir(project_root)
    print(f"当前工作目录: {os.getcwd()}")

    # 检查配置文件是否存在
    config_path = os.path.join(project_root, 'config.yaml')
    if os.path.exists(config_path):
        print(f"配置文件存在: {config_path}")
    else:
        print(f"错误: 配置文件不存在: {config_path}")
        return

    # 直接运行测试文件
    try:
        # 构建测试文件路径
        test_file_path = os.path.join('tests', 'test_utils', f"{test_module_name}.py")
        print(f"测试文件路径: {test_file_path}")

        # 使用 python 命令运行测试文件
        import subprocess
        # 设置环境变量，指定编码
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        result = subprocess.run([sys.executable, test_file_path], capture_output=True, text=True, encoding='utf-8', env=env, check=False)

        # 打印运行结果
        print(f"运行结果代码: {result.returncode}")
        if result.stdout:
            print("\n标准输出:")
            print(result.stdout)
        if result.stderr:
            print("\n标准错误:")
            print(result.stderr)
    except Exception as e:
        print(f"运行测试模块 {test_module_name} 时出错: {e!s}")
        import traceback
        print(traceback.format_exc())

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='运行测试文件')
    parser.add_argument('test_module', help='要运行的测试模块名称（不包含 .py 扩展名）')
    args = parser.parse_args()

    run_test(args.test_module)

if __name__ == "__main__":
    main()
