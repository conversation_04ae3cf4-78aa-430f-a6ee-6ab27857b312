"""配置验证器基类模块

提供统一的配置验证功能,包括:
1. 必需字段检查
2. 类型验证
3. 值范围验证
4. 清晰的错误信息
"""

import logging
from dataclasses import dataclass
from typing import Any


@dataclass
class ValidationRule:
    """验证规则数据类"""
    field_path: str  # 字段路径,如 "training.early_stopping.patience"
    field_type: type  # 字段类型
    required: bool = True  # 是否必需
    min_value: int | float | None = None  # 最小值
    max_value: int | float | None = None  # 最大值
    allowed_values: list[Any] | None = None  # 允许的值列表


class ConfigValidator:
    """配置验证基类"""

    def __init__(self, logger: logging.Logger | None = None):
        """初始化验证器

        Args:
            logger: 日志记录器实例
        """
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self._validation_rules: list[ValidationRule] = []

    def add_rule(self, rule: ValidationRule):
        """添加验证规则

        Args:
            rule: 验证规则
        """
        self._validation_rules.append(rule)

    def get_field_value(self, config: Any, field_path: str) -> Any:
        """获取配置中指定路径的字段值

        Args:
            config: 配置对象
            field_path: 字段路径,如 "training.early_stopping.patience"

        Returns:
            Any: 字段值

        Raises:
            AttributeError: 如果字段不存在
        """
        current = config
        for part in field_path.split('.'):
            if hasattr(current, part):
                current = getattr(current, part)
            elif isinstance(current, dict) and part in current:
                current = current[part]
            else:
                raise AttributeError(f"找不到配置字段: {field_path}")
        return current

    def validate(self, config: Any) -> dict[str, str]:
        """验证配置

        Args:
            config: 配置对象

        Returns:
            Dict[str, str]: 验证错误信息,key为字段路径,value为错误描述
                          如果验证通过,返回空字典
        """
        errors = {}

        for rule in self._validation_rules:
            try:
                # 获取字段值
                try:
                    value = self.get_field_value(config, rule.field_path)
                except AttributeError:
                    if rule.required:
                        errors[rule.field_path] = f"必需的配置字段'{rule.field_path}'不存在"
                    continue

                # 类型检查
                if not isinstance(value, rule.field_type):
                    errors[rule.field_path] = (
                        f"字段'{rule.field_path}'的类型错误: "
                        f"期望 {rule.field_type.__name__}, "
                        f"实际是 {type(value).__name__}"
                    )
                    continue

                # 值范围检查
                if isinstance(value, int | float):
                    if rule.min_value is not None and value < rule.min_value:
                        errors[rule.field_path] = (
                            f"字段'{rule.field_path}'的值{value}小于最小值{rule.min_value}"
                        )
                    if rule.max_value is not None and value > rule.max_value:
                        errors[rule.field_path] = (
                            f"字段'{rule.field_path}'的值{value}大于最大值{rule.max_value}"
                        )

                # 允许值检查
                if rule.allowed_values is not None and value not in rule.allowed_values:
                    errors[rule.field_path] = (
                        f"字段'{rule.field_path}'的值{value}不在允许的值列表中: "
                        f"{rule.allowed_values}"
                    )

            except Exception as e:
                errors[rule.field_path] = f"验证字段'{rule.field_path}'时发生错误: {e!s}"

        return errors

    def validate_or_raise(self, config: Any):
        """验证配置,如果有错误则抛出异常

        Args:
            config: 配置对象

        Raises:
            ValueError: 如果验证失败
        """
        errors = self.validate(config)
        if errors:
            error_msg = "配置验证失败:\n" + "\n".join(
                f"- {field}: {msg}" for field, msg in errors.items()
            )
            self.logger.error(error_msg)
            raise ValueError(error_msg)
