"""优化配置模块 - 管理参数探索和超参数优化相关配置"""

from __future__ import annotations

from dataclasses import dataclass, field  # Add field

from src.utils.logger import get_logger


@dataclass
class FastModeConfig:
    """快速模式配置

    用于快速测试和调试的配置
    """
    num_epochs: int
    batch_size: int
    log_level: str

    def __post_init__(self):
        """初始化快速模式配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []

        if not hasattr(self, 'num_epochs'): # num_epochs is int, check for existence
            missing_fields.append('num_epochs')
        if not hasattr(self, 'batch_size'): # batch_size is int, check for existence
            missing_fields.append('batch_size')
        if not hasattr(self, 'log_level') or not self.log_level: # log_level is str, check for existence and non-empty
            missing_fields.append('log_level')

        if missing_fields:
            error_msg = f"快速模式配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class ParameterExplorationModeConfig:
    """参数探索专属配置"""
    start_date: str  # 必须提供开始日期，不允许默认值
    end_date: str    # 必须提供结束日期，不允许默认值

    def __post_init__(self):
        """初始化参数探索专属配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []

        try:
            if not self.start_date:
                missing_fields.append('start_date')
        except AttributeError:
            missing_fields.append('start_date')

        try:
            if not self.end_date:
                missing_fields.append('end_date')
        except AttributeError:
            missing_fields.append('end_date')

        if missing_fields:
            error_msg = f"参数探索专属配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

@dataclass
class OptimizationConfig:
    """优化配置类

    主要职责:
    1. 管理参数探索相关配置
    2. 超参数优化设置
    3. 快速模式配置
    """
    # 参数探索模块使用的起始日期，用于过滤数据
    fast_mode: FastModeConfig # 没有默认值的字段放在前面
    optimization_start_date: str | None = None # 全局优化起始日期, 现在是可选的
    parameter_exploration: ParameterExplorationModeConfig | None = field(default=None) # 参数探索专属配置

    def __post_init__(self):
        """初始化优化配置，检查必需字段"""
        logger = get_logger(__name__)
        missing_fields = []

        # 检查必需字段
        # optimization_start_date 现在是可选的，所以移除这里的检查
        # if not hasattr(self, 'optimization_start_date') or not self.optimization_start_date:
        #     missing_fields.append('optimization_start_date')
        if not hasattr(self, 'fast_mode') or not isinstance(self.fast_mode, FastModeConfig):
            missing_fields.append('fast_mode (必须是 FastModeConfig 类型)')
        # parameter_exploration 是可选的，所以不需要在这里检查其存在性，
        # 但如果它存在，它应该是 ParameterExplorationModeConfig 类型或 None
        if hasattr(self, 'parameter_exploration') and \
           self.parameter_exploration is not None and \
           not isinstance(self.parameter_exploration, ParameterExplorationModeConfig):
            missing_fields.append('parameter_exploration (如果提供，必须是 ParameterExplorationModeConfig 类型或 None)')


        if missing_fields:
            error_msg = f"优化配置存在问题: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 记录配置信息
        log_message_parts = ["优化配置已加载:"]
        if self.optimization_start_date:
            log_message_parts.append(f"- 全局优化起始日期: {self.optimization_start_date}")
        else:
            log_message_parts.append("- 全局优化起始日期: 未设置")

        log_message_parts.append(f"- 快速模式轮数: {self.fast_mode.num_epochs}")
        log_message_parts.append(f"- 快速模式批次大小: {self.fast_mode.batch_size}")
        log_message_parts.append(f"- 快速模式日志级别: {self.fast_mode.log_level}")

        if self.parameter_exploration:
            if self.parameter_exploration.start_date and self.parameter_exploration.end_date:
                log_message_parts.append(
                    f"- 参数探索专属日期范围: {self.parameter_exploration.start_date} "
                    f"to {self.parameter_exploration.end_date}"
                )
            elif self.parameter_exploration.start_date:
                log_message_parts.append(f"- 参数探索专属起始日期: {self.parameter_exploration.start_date}")
            elif self.parameter_exploration.end_date:
                log_message_parts.append(f"- 参数探索专属结束日期: {self.parameter_exploration.end_date}")
            else:
                log_message_parts.append("- 参数探索专属配置已加载，但未设置专属起始或结束日期。")
        else:
            log_message_parts.append("- 未配置参数探索专属设置。")

        logger.debug("\n".join(log_message_parts))
