"""
指标提取模块

本模块提供从训练历史中提取和验证指标值的功能。支持从不同格式的训练历史数据中
提取指标，并进行类型和有效性验证。

主要组件:
----------
1. extract_metric: 从训练历史中提取并验证指标值
2. 各种辅助函数: 用于处理不同格式的训练历史数据

使用方式:
----------
```python
from src.optimization.metric_extraction import extract_metric

# 从训练历史中提取指标值
val_mae = extract_metric(history, trial_number)
```
"""

import math
from typing import Any

import torch

from src.utils.logger import LoggerFactory

# 获取日志记录器
logger = LoggerFactory().get_logger("MetricExtractor")


def extract_metric(history: dict[str, Any] | list[dict[str, Any]], trial_number: int | None = None) -> float:
    """
    从训练历史中提取并验证指标值。

    Args:
        history: 训练历史数据，可以是字典或字典列表
        trial_number: 当前试验编号，用于日志记录

    Returns:
        float: 提取的指标值(val_mae)

    Raises:
        MetricError: 当无法提取有效指标值时
    """
    trial_prefix = f"Trial #{trial_number}: " if trial_number is not None else ""

    logger.debug(f"{trial_prefix}开始提取指标，history类型: {type(history)}")

    # 检查历史记录是否为空
    if not history:
        error_msg = "训练历史为空"
        logger.warning(f"{trial_prefix}{error_msg}，将使用无效指标值(inf)替代")
        return float('inf')

    # 记录history的基本结构（调试级别）
    if isinstance(history, dict):
        logger.debug(f"{trial_prefix}训练历史是字典，键: {list(history.keys())}")
        # 记录每个键的类型
        for key, value in history.items():
            logger.debug(f"{trial_prefix}history['{key}'] 类型: {type(value)}")
            # 如果值是列表或字典，记录其长度或键
            if isinstance(value, list):
                logger.debug(f"{trial_prefix}history['{key}'] 长度: {len(value)}")
                if value and isinstance(value[0], dict):
                    logger.debug(f"{trial_prefix}history['{key}'][0] 键: {list(value[0].keys())}")
            elif isinstance(value, dict):
                logger.debug(f"{trial_prefix}history['{key}'] 键: {list(value.keys())}")
    elif isinstance(history, list):
        logger.debug(f"{trial_prefix}训练历史是列表，长度: {len(history)}")
        if history:
            logger.debug(f"{trial_prefix}history[0] 类型: {type(history[0])}")
            if isinstance(history[0], dict):
                logger.debug(f"{trial_prefix}history[0] 键: {list(history[0].keys())}")
            logger.debug(f"{trial_prefix}history[-1] 类型: {type(history[-1])}")
            if isinstance(history[-1], dict):
                logger.debug(f"{trial_prefix}history[-1] 键: {list(history[-1].keys())}")

    # 初始化指标值
    val_mae = None

    # 从列表类型的历史记录中提取
    if isinstance(history, list):
        val_mae = _extract_from_list(history, trial_prefix)
    # 从字典类型的历史记录中提取
    elif isinstance(history, dict):
        val_mae = _extract_from_dict(history, trial_prefix)

    # 检查是否成功提取指标值
    if val_mae is None:
        error_msg = "无法从训练历史中提取val_mae指标"
        logger.warning(f"{trial_prefix}{error_msg}，将使用无效指标值(inf)替代")
        return float('inf')

    logger.info(f"{trial_prefix}提取到val_mae原始值: {val_mae} (类型: {type(val_mae)})")

    # 处理Tensor类型
    if isinstance(val_mae, torch.Tensor):
        val_mae = val_mae.item()
        logger.debug(f"{trial_prefix}转换Tensor为数值: {val_mae}")

    # 验证类型
    if not isinstance(val_mae, float | int):
        error_msg = f"VAL_MAE类型错误: {type(val_mae)}"
        logger.warning(f"{trial_prefix}{error_msg}，将使用无效指标值(inf)替代")
        return float('inf')

    # 验证值
    if val_mae < 0:
        error_msg = f"VAL_MAE为负值: {val_mae}"
        logger.warning(f"{trial_prefix}{error_msg}，将使用无效指标值(inf)替代")
        return float('inf')

    if not math.isfinite(val_mae):
        error_msg = f"VAL_MAE不是有限数值: {val_mae}"
        logger.warning(f"{trial_prefix}{error_msg}，将使用无效指标值(inf)替代")

        # 添加更详细的日志记录，帮助分析为什么会出现NaN值
        if isinstance(history, dict):
            # 记录关键训练指标
            for key in ['train_g_loss', 'train_d_loss', 'val_loss']:
                if key in history:
                    value = history[key]
                    if isinstance(value, list) and value:
                        logger.info(f"{trial_prefix}训练历史中的{key} = {value} (类型: {type(value)})")
                    else:
                        logger.info(f"{trial_prefix}训练历史中的{key} = {value} (类型: {type(value)})")

            # 检查是否存在梯度爆炸/消失的迹象
            if 'gradient_stats' in history:
                grad_stats = history['gradient_stats']
                if isinstance(grad_stats, dict):
                    for model_name, stats in grad_stats.items():
                        if isinstance(stats, dict) and 'max' in stats:
                            logger.info(f"{trial_prefix}模型{model_name}的最大梯度值: {stats['max']}")

        return float('inf')

    logger.info(f"{trial_prefix}成功提取val_mae: {val_mae:.6f}")
    return float(val_mae)


def _extract_from_list(history: list[dict[str, Any]], trial_prefix: str) -> float | int | torch.Tensor | None:
    """
    从列表类型的历史记录中提取指标值。

    Args:
        history: 训练历史数据，列表类型
        trial_prefix: 日志前缀

    Returns:
        Optional[Union[float, int, torch.Tensor]]: 提取的指标值，如果无法提取则返回None
    """
    logger.debug(f"{trial_prefix}从列表中提取val_mae")
    if not history:
        error_msg = "训练历史列表为空"
        logger.warning(f"{trial_prefix}{error_msg}，将使用无效指标值(inf)替代")
        return None

    # 尝试从最后一个元素中提取
    last_epoch_metrics = history[-1]
    logger.debug(f"{trial_prefix}最后轮次指标类型: {type(last_epoch_metrics)}")

    if isinstance(last_epoch_metrics, dict):
        logger.debug(f"{trial_prefix}最后轮次指标键: {list(last_epoch_metrics.keys())}")

        # 尝试直接获取val_mae
        val_mae = last_epoch_metrics.get('val_mae')
        logger.debug(f"{trial_prefix}从最后轮次指标中获取val_mae: {val_mae}")

        # 如果val_mae是列表，取最后一个元素
        if isinstance(val_mae, list):
            if val_mae:  # 非空列表
                logger.debug(f"{trial_prefix}val_mae是列表，长度: {len(val_mae)}")
                val_mae = val_mae[-1]
                logger.debug(f"{trial_prefix}从列表中获取最后一个val_mae: {val_mae}")
            else:  # 空列表
                logger.debug(f"{trial_prefix}val_mae是空列表，设置为None")
                val_mae = None

        # 如果val_mae不存在，尝试获取mae
        if val_mae is None and 'mae' in last_epoch_metrics:
            val_mae = last_epoch_metrics.get('mae')
            logger.debug(f"{trial_prefix}从最后轮次指标中获取mae作为替代: {val_mae}")

            # 如果mae是列表，取最后一个元素
            if isinstance(val_mae, list):
                if val_mae:  # 非空列表
                    logger.debug(f"{trial_prefix}mae是列表，长度: {len(val_mae)}")
                    val_mae = val_mae[-1]
                    logger.debug(f"{trial_prefix}从列表中获取最后一个mae: {val_mae}")
                else:  # 空列表
                    logger.debug(f"{trial_prefix}mae是空列表，设置为None")
                    val_mae = None

        # 如果val_mae不存在，记录所有键的值（调试级别）
        if val_mae is None:
            logger.debug(f"{trial_prefix}在最后轮次指标中未找到val_mae或mae，记录所有键值:")
            for key, value in last_epoch_metrics.items():
                logger.debug(f"{trial_prefix}last_epoch_metrics['{key}'] = {value} (类型: {type(value)})")

        # 确保返回类型是 float, int, torch.Tensor 或 None
        if isinstance(val_mae, list):
            logger.warning(f"{trial_prefix}提取到的val_mae仍然是列表类型，设置为None")
            return None

        return val_mae

    return None


def _extract_from_dict(history: dict[str, Any], trial_prefix: str) -> float | int | torch.Tensor | None:
    """
    从字典类型的历史记录中提取指标值。

    Args:
        history: 训练历史数据，字典类型
        trial_prefix: 日志前缀

    Returns:
        Optional[Union[float, int, torch.Tensor]]: 提取的指标值，如果无法提取则返回None
    """
    logger.debug(f"{trial_prefix}从字典中提取val_mae")

    # 尝试直接获取val_mae
    val_mae = history.get('val_mae')
    logger.debug(f"{trial_prefix}直接从history获取val_mae: {val_mae}")

    # 如果val_mae是列表，取最后一个元素
    if isinstance(val_mae, list):
        if val_mae:  # 非空列表
            logger.debug(f"{trial_prefix}val_mae是列表，长度: {len(val_mae)}")
            val_mae = val_mae[-1]
            logger.debug(f"{trial_prefix}从列表中获取最后一个val_mae: {val_mae}")
        else:  # 空列表
            logger.debug(f"{trial_prefix}val_mae是空列表，设置为None")
            val_mae = None

    # 如果val_mae不存在，尝试从metrics中获取
    if val_mae is None:
        metrics = history.get('metrics')
        if isinstance(metrics, dict):
            logger.debug(f"{trial_prefix}从metrics中获取，键: {list(metrics.keys())}")
            val_mae = metrics.get('mae') or metrics.get('val_mae')
            logger.debug(f"{trial_prefix}从metrics中获取val_mae: {val_mae}")

            # 如果从metrics中获取的是列表，取最后一个元素
            if isinstance(val_mae, list):
                if val_mae:  # 非空列表
                    val_mae = val_mae[-1]
                    logger.debug(f"{trial_prefix}从metrics中获取的列表中取最后一个值: {val_mae}")
                else:  # 空列表
                    logger.debug(f"{trial_prefix}从metrics中获取的是空列表，设置为None")
                    val_mae = None

        # 如果还是不存在，尝试从其他可能的位置获取
        if val_mae is None and 'mae' in history:
            val_mae = history.get('mae')
            logger.debug(f"{trial_prefix}从history直接获取mae作为替代: {val_mae}")

            # 如果mae是列表，取最后一个元素
            if isinstance(val_mae, list):
                if val_mae:  # 非空列表
                    val_mae = val_mae[-1]
                    logger.debug(f"{trial_prefix}从mae列表中获取最后一个值: {val_mae}")
                else:  # 空列表
                    logger.debug(f"{trial_prefix}mae是空列表，设置为None")
                    val_mae = None

        # 如果还是不存在，记录所有键的值（调试级别）
        if val_mae is None:
            logger.debug(f"{trial_prefix}在history中未找到val_mae或mae，记录关键键值:")
            for key, value in history.items():
                if not isinstance(value, list | dict) or (isinstance(value, list) and len(value) < 10):
                    logger.debug(f"{trial_prefix}history['{key}'] = {value} (类型: {type(value)})")
                else:
                    logger.debug(f"{trial_prefix}history['{key}'] 太大，不记录具体内容")

    # 确保返回类型是 float, int, torch.Tensor 或 None
    if isinstance(val_mae, list):
        logger.warning(f"{trial_prefix}提取到的val_mae仍然是列表类型，设置为None")
        return None

    return val_mae
