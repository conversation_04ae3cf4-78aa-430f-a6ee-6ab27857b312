"""
测试配置读取功能

此脚本测试配置文件中的参数是否被正确读取和应用，特别是自适应流管理器的配置读取。
"""

import os
import sys

import torch
import yaml

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.utils.adaptive_stream_manager import AdaptiveStreamManager
from src.utils.cuda import cuda_manager


def print_separator():
    print("\n" + "="*50 + "\n")

def test_config_reading():
    """测试配置读取功能"""
    print("开始测试配置读取功能")

    # 确保CUDA可用
    if not torch.cuda.is_available():
        print("CUDA不可用，无法测试自适应流管理器")
        return

    print(f"CUDA可用: {torch.cuda.get_device_name()}")

    # 步骤1: 读取原始配置文件
    print("\n步骤1: 读取原始配置文件")
    config_path = os.path.join(project_root, 'config.yaml')

    try:
        with open(config_path, encoding='utf-8') as f:
            original_config = yaml.safe_load(f)
        print("成功读取原始配置文件")
    except Exception as e:
        print(f"读取原始配置文件失败: {e!s}")
        return

    # 打印原始配置
    adaptive_config = original_config['system']['cuda']['streams']['adaptive']
    print("原始自适应流配置:")
    print(f"  - history_size: {adaptive_config.get('history_size', '未设置')}")
    print(f"  - monitoring_interval: {adaptive_config.get('monitoring_interval', '未设置')}")
    print(f"  - low_threshold: {adaptive_config.get('low_threshold', '未设置')}")
    print(f"  - high_threshold: {adaptive_config.get('high_threshold', '未设置')}")
    print(f"  - adjustment_step: {adaptive_config.get('adjustment_step', '未设置')}")
    print(f"  - min_streams: {adaptive_config.get('min_streams', '未设置')}")
    print(f"  - max_streams: {original_config['system']['cuda']['streams'].get('max_streams', '未设置')}")

    # 步骤2: 使用原始配置初始化自适应流管理器
    print("\n步骤2: 使用原始配置初始化自适应流管理器")

    # 配置 cuda_manager
    print("配置 cuda_manager...")
    cuda_manager.configure(original_config['system']['cuda'])
    print("cuda_manager 配置完成")

    # 获取流管理器统计信息
    stats = cuda_manager.get_stream_stats()
    print(f"初始流统计信息: {stats}")

    # 检查是否使用自适应流管理器
    if stats.get('manager_type') != 'adaptive':
        print("警告: 未使用自适应流管理器，请检查配置")
        return
    else:
        print("确认: 使用自适应流管理器")

    # 获取自适应流管理器
    # 使用流管理器的公共接口获取流统计信息
    stream_stats = cuda_manager.get_stream_stats()
    if stream_stats.get('adaptive_enabled'):
        # 打印自适应流管理器参数
        print("自适应流管理器参数:")
        print(f"  - 流统计信息: {stream_stats}")

        # 验证参数是否与配置文件一致
        print("\n验证参数是否与配置文件一致:")

        # 从流统计信息中获取配置值
        if 'config' in stream_stats:
            config_values = stream_stats['config']
            print(f"  - 配置值: {config_values}")

            # 检查配置文件中的值
            print("\n配置文件中的值:")
            print(f"  - adaptive_config: {adaptive_config}")
            print(f"  - max_streams: {original_config['system']['cuda']['streams'].get('max_streams', 16)}")
        else:
            print("  - 流统计信息中没有配置数据")
    else:
        print("错误: 自适应流管理器未初始化")
        return

    print_separator()

    # 步骤3: 创建临时配置文件，修改参数
    print("\n步骤3: 创建临时配置文件，修改参数")

    # 创建临时配置文件的副本
    temp_config_path = os.path.join(project_root, 'temp_config.yaml')
    temp_config = original_config.copy()

    # 修改配置参数
    temp_config['system']['cuda']['streams']['adaptive']['history_size'] = 3
    temp_config['system']['cuda']['streams']['adaptive']['monitoring_interval'] = 2
    temp_config['system']['cuda']['streams']['adaptive']['low_threshold'] = 45
    temp_config['system']['cuda']['streams']['adaptive']['high_threshold'] = 75
    temp_config['system']['cuda']['streams']['adaptive']['adjustment_step'] = 3
    temp_config['system']['cuda']['streams']['adaptive']['min_streams'] = 5
    temp_config['system']['cuda']['streams']['max_streams'] = 24

    # 保存临时配置文件
    try:
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(temp_config, f, default_flow_style=False)
        print(f"成功创建临时配置文件: {temp_config_path}")
    except Exception as e:
        print(f"创建临时配置文件失败: {e!s}")
        return

    # 打印修改后的配置
    temp_adaptive_config = temp_config['system']['cuda']['streams']['adaptive']
    print("修改后的自适应流配置:")
    print(f"  - history_size: {temp_adaptive_config.get('history_size', '未设置')}")
    print(f"  - monitoring_interval: {temp_adaptive_config.get('monitoring_interval', '未设置')}")
    print(f"  - low_threshold: {temp_adaptive_config.get('low_threshold', '未设置')}")
    print(f"  - high_threshold: {temp_adaptive_config.get('high_threshold', '未设置')}")
    print(f"  - adjustment_step: {temp_adaptive_config.get('adjustment_step', '未设置')}")
    print(f"  - min_streams: {temp_adaptive_config.get('min_streams', '未设置')}")
    print(f"  - max_streams: {temp_config['system']['cuda']['streams'].get('max_streams', '未设置')}")

    # 步骤4: 使用修改后的配置初始化新的自适应流管理器
    print("\n步骤4: 使用修改后的配置初始化新的自适应流管理器")

    # 直接创建自适应流管理器实例
    try:
        new_adaptive_manager = AdaptiveStreamManager(temp_config['system']['cuda'])
        print("成功创建新的自适应流管理器实例")

        # 打印新的自适应流管理器参数
        print("新的自适应流管理器参数:")
        print(f"  - history_size: {new_adaptive_manager.history_size}")
        print(f"  - monitoring_interval: {new_adaptive_manager.monitoring_interval}")
        print(f"  - low_threshold: {new_adaptive_manager.low_threshold}")
        print(f"  - high_threshold: {new_adaptive_manager.high_threshold}")
        print(f"  - adjustment_step: {new_adaptive_manager.adjustment_step}")
        print(f"  - min_streams: {new_adaptive_manager.min_streams}")
        print(f"  - max_streams: {new_adaptive_manager.max_streams}")
        print(f"  - current_limit: {new_adaptive_manager.current_limit}")

        # 验证参数是否与修改后的配置文件一致
        print("\n验证参数是否与修改后的配置文件一致:")

        # 检查 history_size
        if new_adaptive_manager.history_size == temp_adaptive_config['history_size']:
            print(f"  - history_size: 一致 ({temp_adaptive_config['history_size']})")
        else:
            print(f"  - history_size: 不一致 (配置: {temp_adaptive_config['history_size']}, 实际: {new_adaptive_manager.history_size})")

        # 检查 monitoring_interval
        if new_adaptive_manager.monitoring_interval == temp_adaptive_config['monitoring_interval']:
            print(f"  - monitoring_interval: 一致 ({temp_adaptive_config['monitoring_interval']})")
        else:
            print(f"  - monitoring_interval: 不一致 (配置: {temp_adaptive_config['monitoring_interval']}, 实际: {new_adaptive_manager.monitoring_interval})")

        # 检查 low_threshold
        if new_adaptive_manager.low_threshold == temp_adaptive_config['low_threshold']:
            print(f"  - low_threshold: 一致 ({temp_adaptive_config['low_threshold']})")
        else:
            print(f"  - low_threshold: 不一致 (配置: {temp_adaptive_config['low_threshold']}, 实际: {new_adaptive_manager.low_threshold})")

        # 检查 high_threshold
        if new_adaptive_manager.high_threshold == temp_adaptive_config['high_threshold']:
            print(f"  - high_threshold: 一致 ({temp_adaptive_config['high_threshold']})")
        else:
            print(f"  - high_threshold: 不一致 (配置: {temp_adaptive_config['high_threshold']}, 实际: {new_adaptive_manager.high_threshold})")

        # 检查 adjustment_step
        if new_adaptive_manager.adjustment_step == temp_adaptive_config['adjustment_step']:
            print(f"  - adjustment_step: 一致 ({temp_adaptive_config['adjustment_step']})")
        else:
            print(f"  - adjustment_step: 不一致 (配置: {temp_adaptive_config['adjustment_step']}, 实际: {new_adaptive_manager.adjustment_step})")

        # 检查 min_streams
        if new_adaptive_manager.min_streams == temp_adaptive_config['min_streams']:
            print(f"  - min_streams: 一致 ({temp_adaptive_config['min_streams']})")
        else:
            print(f"  - min_streams: 不一致 (配置: {temp_adaptive_config['min_streams']}, 实际: {new_adaptive_manager.min_streams})")

        # 检查 max_streams
        if new_adaptive_manager.max_streams == temp_config['system']['cuda']['streams']['max_streams']:
            print(f"  - max_streams: 一致 ({temp_config['system']['cuda']['streams']['max_streams']})")
        else:
            print(f"  - max_streams: 不一致 (配置: {temp_config['system']['cuda']['streams']['max_streams']}, 实际: {new_adaptive_manager.max_streams})")

        # 清理资源
        new_adaptive_manager.cleanup()
    except Exception as e:
        print(f"创建新的自适应流管理器实例失败: {e!s}")
        import traceback
        print(traceback.format_exc())

    # 步骤5: 清理临时文件
    print("\n步骤5: 清理临时文件")
    try:
        os.remove(temp_config_path)
        print(f"成功删除临时配置文件: {temp_config_path}")
    except Exception as e:
        print(f"删除临时配置文件失败: {e!s}")

    print("\n测试完成!")

if __name__ == "__main__":
    test_config_reading()
