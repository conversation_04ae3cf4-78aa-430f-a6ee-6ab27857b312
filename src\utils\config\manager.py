"""配置管理器模块 - 提供全局配置管理和访问接口

此模块提供了配置管理的核心功能，包括：
1. 配置加载与初始化
2. 配置验证与访问
3. 配置更新与转换
4. GAN训练器专用配置管理
"""

from __future__ import annotations

from pathlib import Path
from typing import Any

from src.utils.config.base import BaseConfig
from src.utils.config.data import (  # Added FeatureSelectionConfig
    DataConfig,
    FeatureEngineeringConfig,
    FeatureSelectionConfig,
)
from src.utils.config.model import BaseModelConfig, GANModelConfig
from src.utils.config.optimization import (
    OptimizationConfig,  # Added OptimizationConfig import
)
from src.utils.config.paths import PathsConfig
from src.utils.config.prediction import (
    PredictionConfig,  # Added PredictionConfig import
)
from src.utils.config.system import LoggingConfig, SystemConfig

# Import all necessary training config classes
from src.utils.config.training import EvaluationConfig, TrainingConfig

# 配置管理器单例实例
_config_instance: ConfigManager | None = None

def get_config() -> ConfigManager:
    """获取全局配置实例"""
    global _config_instance
    if not _config_instance:
        config_path = Path(__file__).parent.parent.parent.parent / "config.yaml"
        _config_instance = ConfigManager.from_yaml(config_path)
    return _config_instance

class ConfigManager:
    """全局配置管理器"""

    def __init__(
        self,
        version: str,
        paths: PathsConfig,
        system: SystemConfig,
        logging: LoggingConfig,
        data: DataConfig,
        training: TrainingConfig,
        model: BaseModelConfig | GANModelConfig,
        feature_engineering: FeatureEngineeringConfig,
        evaluation: EvaluationConfig,
        preprocessing: dict[str, Any], # 移除默认值 None
        feature_selection: FeatureSelectionConfig, # Added feature_selection
        prediction: PredictionConfig, # Added prediction parameter
        optimization: OptimizationConfig | None = None # Added optimization parameter
    ):
        """初始化配置管理器"""
        # DEBUG LOGGING for incoming optimization parameter
        from src.utils.logger import (
            get_logger as get_internal_logger,  # Use a different alias to avoid conflict
        )
        init_logger = get_internal_logger("ConfigManagerInit")
        init_logger.debug(f"ConfigManager.__init__ called. Received optimization param: {optimization}, type: {type(optimization)}")

        self.version = version
        self.paths = paths
        self.system = system
        self.logging = logging
        self.data = data
        self.training = training
        self.model = model
        self.feature_engineering = feature_engineering
        self.evaluation = evaluation
        self.preprocessing = preprocessing # 直接赋值，不再使用 or {} 回退
        self.feature_selection = feature_selection # Added feature_selection
        self.prediction = prediction # Added prediction assignment
        self.optimization = optimization # Added optimization assignment
        self._logger = None

        # DEBUG LOGGING for self.optimization after assignment
        init_logger.debug(f"ConfigManager.__init__: self.optimization set to: {self.optimization}, type: {type(self.optimization)}")


    @classmethod
    def from_yaml(cls, yaml_path: str | Path) -> ConfigManager:
        """从YAML文件加载配置"""
        from .loader import ConfigLoader
        return ConfigLoader.load_from_yaml(yaml_path)

    @classmethod
    def from_config(cls, config: dict[str, Any] | ConfigManager | str) -> ConfigManager:
        """从各种配置源创建配置管理器实例

        Args:
            config: 配置源，可以是ConfigManager实例、配置字典或配置文件路径

        Returns:
            ConfigManager: 配置管理器实例

        Raises:
            ValueError: 如果配置源类型不支持或配置初始化失败
        """
        from src.utils.logger import get_logger
        logger = get_logger("ConfigManager")

        if isinstance(config, ConfigManager):
            return config

        try:
            if isinstance(config, str):
                config_path = Path(config)
                if not config_path.exists():
                    raise FileNotFoundError(f"配置文件不存在: {config_path}")
                return ConfigManager.from_yaml(config_path)

            if isinstance(config, dict):
                if 'config_path' in config:
                    return ConfigManager.from_yaml(config['config_path'])
                # 使用字典更新默认配置
                config_path = Path(__file__).parent.parent.parent.parent / "config.yaml"
                config_manager = ConfigManager.from_yaml(config_path)
                config_manager.update(config)
                return config_manager

            raise ValueError(f"不支持的配置类型: {type(config)}")

        except Exception as e:
            logger.error(f"配置初始化失败: {e!s}")
            raise ValueError(f"配置初始化失败: {e!s}") from e

    def get_model_config(self) -> BaseModelConfig:
        """获取模型配置(带类型注解)

        Returns:
            BaseModelConfig: 模型配置对象
        """
        return self.model

    def safe_get(self, path: str) -> Any:
        """安全获取配置项，如果不存在则抛出异常

        Args:
            path: 配置路径，使用点号分隔(如'model.dimensions.base_dim')

        Returns:
            配置值

        Raises:
            KeyError: 如果路径中的某个键在字典中不存在
            AttributeError: 如果路径中的某个键在对象属性中不存在或访问出错
        """
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        keys = path.split('.')
        val = self # Start from the ConfigManager instance itself
        current_path_list = []
        for key in keys:
            current_path_list.append(key)
            current_path_str = '.'.join(current_path_list)
            error_msg = "" # Initialize error message
            # 简化访问逻辑，移除多重回退机制
            if isinstance(val, BaseConfig):
                # 对于 BaseConfig 实例，直接使用属性访问
                if not hasattr(val, key):
                    error_msg = f"配置项 '{current_path_str}' 不存在于 {type(val).__name__} 中 (完整路径: {path})"
                    logger.error(error_msg)
                    raise AttributeError(error_msg)
                val = getattr(val, key)
            elif isinstance(val, dict):
                # 对于字典，直接使用键访问
                if key not in val:
                    error_msg = f"配置项 '{current_path_str}' 不存在于字典中 (完整路径: {path})"
                    logger.error(error_msg)
                    raise KeyError(error_msg)
                val = val[key]
            else:
                # 对于其他对象，使用属性访问
                if not hasattr(val, key):
                    error_msg = f"配置项 '{current_path_str}' 不存在于对象 {type(val).__name__} 中 (完整路径: {path})"
                    logger.error(error_msg)
                    raise AttributeError(error_msg)
                val = getattr(val, key)

        return val



    def __post_init__(self):
        """验证配置完整性并执行交叉验证"""
        required_fields = ['version', 'paths', 'system', 'logging', 'data',
                         'training', 'model', 'feature_engineering', 'evaluation', 'preprocessing', 'feature_selection', 'prediction', 'optimization']
        missing = [field for field in required_fields if not hasattr(self, field) or getattr(self, field) is None]
        if missing:
            raise ValueError(f"ConfigManager 初始化后缺少必需字段或字段为 None: {', '.join(missing)}")

        # 调用其他验证方法
        self._validate_core_config()
        self._cross_validate()

    def _validate_core_config(self):
        """核心配置验证"""
        # 验证 batch_size
        batch_size = getattr(self.training, 'batch_size', None)
        if batch_size is None:
            raise ValueError("配置缺失 training.batch_size")
        try:
            batch_size_int = int(batch_size)
            if not (0 < batch_size_int <= 1024):
                 raise ValueError(f"batch_size ({batch_size_int}) 必须在 (0, 1024] 范围内")
        except (ValueError, TypeError) as e:
             raise ValueError(f"无效的 batch_size 配置: {batch_size} - {e}") from e

        if not isinstance(self.model, BaseModelConfig | GANModelConfig):
            raise TypeError("模型配置类型必须为BaseModelConfig或GANModelConfig")

    def _cross_validate(self):
        """跨配置项验证"""
        # 确保噪声维度一致
        if self.model.noise_dim != self.training.noise_dim:
            raise ValueError(f"模型噪声维度({self.model.noise_dim})与训练配置({self.training.noise_dim})不一致")

        # 特征维度完全由数据配置决定
        if hasattr(self.training, 'feature_dim'):
            delattr(self.training, 'feature_dim')

    # 移除 validate_config 方法，验证逻辑已移至 __post_init__
    # def validate_config(self) -> bool:
    #     ... (移除整个方法实现) ...

    def get(self, key: str) -> Any:
        """安全获取嵌套配置值，如果不存在则抛出异常 (等同于 safe_get)

        Args:
            key: 配置项路径，使用点号分隔，如"model.dimensions.hidden_size"

        Returns:
            配置项的值

        Raises:
            KeyError: 如果路径中的某个键在字典中不存在
            AttributeError: 如果路径中的某个键在对象属性中不存在或访问出错
        """
        # 直接调用 safe_get，因为它们的功能现在完全相同
        return self.safe_get(key)


    def update(self, config_dict: dict[str, Any]) -> None:
        """更新配置值

        Args:
            config_dict: 配置字典，可以是嵌套结构
        """
        for key, value in config_dict.items():
            if '.' in key:
                # 处理嵌套路径，如'data.window_size'
                parts = key.split('.')
                obj = self
                for part in parts[:-1]:
                    if hasattr(obj, part):
                        obj = getattr(obj, part)
                    else:
                        break
                else:
                    # 如果循环正常结束，设置最终属性
                    setattr(obj, parts[-1], value)
            elif hasattr(self, key):
                # 直接设置顶级属性
                if isinstance(value, dict) and isinstance(getattr(self, key), BaseConfig):
                    # 如果是配置对象，使用字典更新
                    obj = getattr(self, key)
                    for k, v in value.items():
                        if hasattr(obj, k):
                            setattr(obj, k, v)
                else:
                    # 直接设置值
                    setattr(self, key, value)

    def __getitem__(self, key: str) -> Any:
        """支持字典式访问配置"""
        try:
            return self.get(key)
        except Exception:
            raise KeyError(f"Config key not found: {key}")

    def __getattr__(self, name: str) -> Any:
        """严格的属性访问。如果属性不存在，直接抛出 AttributeError。"""
        # 移除所有回退逻辑和动态加载/创建行为。
        # 如果属性不在实例的 __dict__ 中，标准的 Python 行为就是抛出 AttributeError。
        # 我们不需要显式检查 __dict__，只需要让 Python 的默认机制工作。
        # 这个方法实际上可以被移除，除非有特殊原因需要覆盖默认行为（但这里不需要）。
        # 为了明确表示移除了旧逻辑，我们显式抛出错误。
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def to_dict(self) -> dict[str, Any]:
        """将配置管理器对象递归转换为字典"""
        from .converter import ConfigConverter
        return ConfigConverter.to_dict(self)

    # 禁止使用默认配置
    @classmethod
    def default_config(cls) -> ConfigManager:
        """已禁用 - 配置必须从yaml文件加载"""
        raise NotImplementedError("不允许使用默认配置，必须从yaml文件加载配置")
