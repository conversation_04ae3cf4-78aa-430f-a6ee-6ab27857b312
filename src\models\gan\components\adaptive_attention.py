"""自适应注意力机制 - 提供自适应扩张率和多层注意力机制

本模块实现了两种高级注意力机制：
1. 自适应扩张率注意力 (AdaptiveDilationAttention)
2. 多层注意力机制 (MultiLayerAttention)

这些机制可以增强模型捕捉不同时间尺度和复杂时序关系的能力。
"""

import time

import torch
from torch import nn

from src.models.base.base_module import BaseModule
from src.utils.cuda import cuda_manager


class AdaptiveDilationAttention(BaseModule):
    """自适应扩张率注意力 - 根据输入数据特性动态调整扩张率

    此实现使用CUDA流进行并行计算，显著提高性能。
    """

    def __init__(
        self,
        embed_dim: int,
        num_heads: int,  # 注意力头数
        num_scales: int,  # 尺度数量
        dropout: float,
        use_streams: bool,  # 是否使用CUDA流
        max_batch_streams: int,  # 最大批次流数量
        max_scale_streams: int,  # 最大尺度流数量
        verbose_logging: bool  # 是否启用详细日志
    ):
        """初始化自适应扩张率注意力

        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            num_scales: 尺度数量
            dropout: Dropout比率
            use_streams: 是否使用CUDA流
            max_batch_streams: 最大批次流数量
            max_scale_streams: 最大尺度流数量
            verbose_logging: 是否启用详细日志
        """
        super().__init__("AdaptiveDilationAttention")

        # 参数验证
        if embed_dim <= 0:
            self.logger.error(f"嵌入维度必须为正数，当前值: {embed_dim}")
            raise ValueError(f"嵌入维度必须为正数，当前值: {embed_dim}")

        # 记录参数使用情况
        self.logger.info(f"初始化自适应扩张率注意力 - 嵌入维度: {embed_dim}")

        # 参数验证
        if num_heads <= 0:
            error_msg = f"注意力头数必须为正数，当前值: {num_heads}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if num_scales <= 0:
            error_msg = f"尺度数量必须为正数，当前值: {num_scales}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if dropout < 0 or dropout >= 1:
            error_msg = f"Dropout比率必须在[0, 1)范围内，当前值: {dropout}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if max_batch_streams <= 0:
            error_msg = f"最大批次流数量必须为正数，当前值: {max_batch_streams}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if max_scale_streams <= 0:
            error_msg = f"最大尺度流数量必须为正数，当前值: {max_scale_streams}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_scales = num_scales
        self.dropout_p = dropout

        # 流管理参数
        # 检查CUDA是否可用
        if use_streams and not cuda_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法使用CUDA流"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

        self.use_streams = use_streams
        self.max_batch_streams = max_batch_streams
        self.max_scale_streams = max_scale_streams

        # 记录CUDA流使用情况
        if self.use_streams:
            self.logger.info(f"启用CUDA流并行计算 - 批次流: {max_batch_streams}, 尺度流: {max_scale_streams}")
        else:
            self.logger.info("使用串行计算模式（未启用CUDA流）")

        # 初始化流
        self.batch_streams = []
        self.scale_streams = []

        # 日志设置
        self.verbose_logging = verbose_logging
        mode = "详细" if verbose_logging else "标准"
        self.logger.info(f"日志记录模式: {mode}")

        # 性能统计
        self.total_forward_time = 0.0
        self.forward_count = 0
        self.logger.info("性能统计初始化完成")

        # 基础扩张率 - 注册为缓冲区，不参与梯度计算
        self.register_buffer('base_dilation_rates', torch.tensor([1, 2, 4, 8, 16][:num_scales], dtype=torch.float))

        # 可学习的扩张率调整因子
        self.dilation_factors = nn.Parameter(torch.ones(num_scales))

        # 序列长度感知网络
        self.length_encoder = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.ReLU(),
            nn.Linear(embed_dim // 2, num_scales),
            nn.Softplus()  # 确保输出为正值
        )

        # 注意力层
        self.logger.info(f"创建注意力层 - 嵌入维度: {embed_dim}, 注意力头数: {num_heads}, 层数: {num_scales}")
        self.attention_layers = nn.ModuleList([
            nn.MultiheadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout,
                batch_first=True
            ) for _ in range(num_scales)
        ])

        # 验证注意力头数
        for i, layer in enumerate(self.attention_layers):
            if hasattr(layer, 'num_heads') and layer.num_heads != num_heads:
                error_msg = f"注意力层 {i} 的头数({layer.num_heads})与配置的头数({num_heads})不一致"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

        # 输出投影
        self.output_proj = nn.Linear(embed_dim, embed_dim)

        # 层归一化
        self.layer_norm = nn.LayerNorm(embed_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def _ensure_dtype_consistency(self, tensor: torch.Tensor, reference_tensor: torch.Tensor) -> torch.Tensor:
        """确保张量数据类型与参考张量一致

        Args:
            tensor: 需要检查的张量
            reference_tensor: 参考张量

        Returns:
            torch.Tensor: 数据类型一致的张量
        """
        if tensor.dtype != reference_tensor.dtype:
            self.logger.debug(f"数据类型不匹配，从 {tensor.dtype} 转换为 {reference_tensor.dtype}")
            return tensor.to(dtype=reference_tensor.dtype)
        return tensor

    def _init_streams(self):
        """初始化CUDA流"""
        if not self.use_streams:
            # 如果不使用流，保持空列表
            if self.verbose_logging:
                self.logger.debug("不使用CUDA流，跳过初始化")
            return

        # 如果使用流且列表为空，创建新流
        if len(self.batch_streams) == 0:
            self.logger.info(f"初始化批次CUDA流 - 数量: {self.max_batch_streams}")
            try:
                self.batch_streams = [
                    cuda_manager.create_stream(f"adaptive_attention_batch_{i}")
                    for i in range(self.max_batch_streams)
                ]
                self.logger.info(f"批次CUDA流创建成功 - 数量: {len(self.batch_streams)}")
            except Exception as e:
                error_msg = f"创建批次CUDA流失败: {e!s}"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg) from e

        if len(self.scale_streams) == 0:
            self.logger.info(f"初始化尺度CUDA流 - 数量: {self.max_scale_streams}")
            try:
                self.scale_streams = [
                    cuda_manager.create_stream(f"adaptive_attention_scale_{i}")
                    for i in range(self.max_scale_streams)
                ]
                self.logger.info(f"尺度CUDA流创建成功 - 数量: {len(self.scale_streams)}")
            except Exception as e:
                error_msg = f"创建尺度CUDA流失败: {e!s}"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg) from e

    def forward(self, x: torch.Tensor, mask: torch.Tensor | None = None) -> tuple[torch.Tensor, list[torch.Tensor]]:
        """前向传播 - 使用CUDA流并行化

        Args:
            x: 输入序列 [batch_size, seq_length, embed_dim]
            mask: 注意力掩码 [batch_size, seq_length, seq_length]

        Returns:
            Tuple[torch.Tensor, List[torch.Tensor]]:
                输出 [batch_size, seq_length, embed_dim]
                注意力权重列表
        """
        # 记录开始时间
        start_time = time.time()

        # 输入验证
        if x is None:
            error_msg = "输入张量不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if x.dim() != 3:
            error_msg = f"输入张量必须为3维 [batch_size, seq_length, embed_dim]，当前维度: {x.dim()}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 记录输入形状
        batch_size, seq_length, embed_dim = x.shape
        self.logger.info(f"开始处理输入 - 形状: [{batch_size}, {seq_length}, {embed_dim}]")

        # 确保输入在正确的设备上
        device = next(self.parameters()).device
        self.logger.debug(f"当前计算设备: {device}")

        if x.device != device:
            self.logger.debug(f"将输入张量从 {x.device} 移动到 {device}")
            x = x.to(device)

        if mask is not None and mask.device != device:
            self.logger.debug(f"将掩码张量从 {mask.device} 移动到 {device}")
            mask = mask.to(device)

        # 初始化CUDA流
        self._init_streams()

        # 1. 计算序列特性 - 在主流上执行
        global_repr = x.mean(dim=1)  # [batch_size, embed_dim]
        length_factors = self.length_encoder(global_repr)  # [batch_size, num_scales]

        # 2. 计算自适应扩张率 - 在主流上执行
        # 验证必要的参数是否存在
        if not hasattr(self, 'base_dilation_rates') or self.base_dilation_rates is None:
            error_msg = "基础扩张率未初始化"
            self._logger.error(error_msg)
            raise ValueError(error_msg)

        if not hasattr(self, 'dilation_factors') or self.dilation_factors is None:
            error_msg = "扩张率调整因子未初始化"
            self._logger.error(error_msg)
            raise ValueError(error_msg)

        adaptive_rates = []
        for b in range(batch_size):
            length_factor_b = length_factors[b].to(self.base_dilation_rates.device)
            batch_rates = []
            for i in range(self.num_scales):
                # 验证索引是否有效
                if i >= len(self.base_dilation_rates):
                    error_msg = f"基础扩张率索引超出范围: {i} >= {len(self.base_dilation_rates)}"
                    self._logger.error(error_msg)
                    raise IndexError(error_msg)

                if i >= len(self.dilation_factors):
                    error_msg = f"扩张率调整因子索引超出范围: {i} >= {len(self.dilation_factors)}"
                    self._logger.error(error_msg)
                    raise IndexError(error_msg)

                if i >= len(length_factor_b):
                    error_msg = f"长度因子索引超出范围: {i} >= {len(length_factor_b)}"
                    self._logger.error(error_msg)
                    raise IndexError(error_msg)

                rate = self.base_dilation_rates[i] * self.dilation_factors[i] * length_factor_b[i]
                rate = torch.clamp(rate.round(), min=1).int()
                batch_rates.append(rate)
            adaptive_rates.append(batch_rates)

        # 记录自适应扩张率
        if self.training and torch.rand(1).item() < 0.01:  # 1%的概率记录
            rates_values = [rate.item() for rate in adaptive_rates[0]]
            self.logger.info(f"自适应扩张率: {rates_values}")

        # 3. 层归一化 - 在主流上执行
        x_norm = self.layer_norm(x)

        self.logger.info(f"AdaptiveDilationAttention 开始处理多尺度注意力 - 输入形状: {x.shape}")
        self.logger.info(f"AdaptiveDilationAttention 自适应扩张率数量: {len(adaptive_rates)}")
        self.logger.info(f"AdaptiveDilationAttention 归一化后: "
                        f"min={x_norm.min().item():.4e}, max={x_norm.max().item():.4e}, "
                        f"mean={x_norm.mean().item():.4e}, std={x_norm.std().item():.4e}")

        # 4. 并行处理批次
        outputs = []
        attention_weights_list = []

        # 非流版本 - 串行处理
        if not self.use_streams:
            batch_outputs = []
            batch_weights = []

            for b in range(batch_size):
                # 处理当前批次的所有尺度
                batch_output, batch_weight = self._process_single_batch(
                    x_norm[b:b+1],
                    adaptive_rates[b],
                    mask[b:b+1] if mask is not None else None
                )
                batch_outputs.append(batch_output)
                batch_weights.append(batch_weight)

            # 合并所有批次输出
            outputs = [torch.cat(batch_outputs, dim=0)]
            attention_weights_list = batch_weights
        else:
            # 流版本 - 并行处理
            # 计算每个流处理的批次数
            batches_per_stream = (batch_size + len(self.batch_streams) - 1) // len(self.batch_streams)

            # 在不同流上并行处理批次
            for stream_idx in range(min(len(self.batch_streams), batch_size)):
                start_batch = stream_idx * batches_per_stream
                end_batch = min(start_batch + batches_per_stream, batch_size)

                if start_batch >= batch_size:
                    break

                with torch.cuda.stream(self.batch_streams[stream_idx]):
                    stream_outputs = []
                    stream_weights = []

                    # 处理当前流负责的批次
                    for b in range(start_batch, end_batch):
                        # 处理当前批次的所有尺度
                        batch_output, batch_weight = self._process_single_batch(
                            x_norm[b:b+1],
                            adaptive_rates[b],
                            mask[b:b+1] if mask is not None else None
                        )
                        stream_outputs.append(batch_output)
                        stream_weights.append(batch_weight)

                    # 合并当前流的结果
                    if stream_outputs:
                        outputs.append(torch.cat(stream_outputs, dim=0))
                        attention_weights_list.extend(stream_weights)

            # 同步所有批次流
            for stream_idx in range(min(len(self.batch_streams), (batch_size + batches_per_stream - 1) // batches_per_stream)):
                try:
                    cuda_manager.synchronize_stream(self.batch_streams[stream_idx])
                    self.logger.debug(f"批次流 {stream_idx} 同步完成")
                except Exception as e:
                    error_msg = f"批次流 {stream_idx} 同步失败: {e!s}"
                    self.logger.error(error_msg)
                    raise RuntimeError(error_msg) from e

        # 5. 合并所有批次的输出
        multi_scale_output = torch.cat(outputs, dim=0) if len(outputs) > 1 else outputs[0]

        self.logger.info(f"AdaptiveDilationAttention 多尺度输出形状: {multi_scale_output.shape}")

        # 6. 应用输出投影
        output = self.output_proj(multi_scale_output)

        self.logger.info(f"AdaptiveDilationAttention 投影后输出形状: {output.shape}")

        # 7. 应用Dropout
        output = self.dropout(output)

        # 8. 残差连接
        output = x + output

        self.logger.info(f"AdaptiveDilationAttention 最终输出形状: {output.shape}, "
                        f"min={output.min().item():.4e}, max={output.max().item():.4e}, "
                        f"mean={output.mean().item():.4e}, std={output.std().item():.4e}")

        # 记录结束时间和性能统计
        end_time = time.time()
        self.total_forward_time += (end_time - start_time)
        self.forward_count += 1

        if self.forward_count % 100 == 0:
            avg_time = self.total_forward_time / self.forward_count * 1000  # 转换为毫秒
            self.logger.info(f"自适应扩张率注意力平均执行时间: {avg_time:.2f}毫秒 (样本数: {self.forward_count})")

        return output, attention_weights_list

    def _process_single_batch(
        self,
        x_batch: torch.Tensor,
        batch_rates: list[torch.Tensor],
        mask_batch: torch.Tensor | None
    ) -> tuple[torch.Tensor, list[torch.Tensor]]:
        """处理单个批次的所有尺度

        Args:
            x_batch: 单个批次的输入 [1, seq_length, embed_dim]
            batch_rates: 当前批次的扩张率列表
            mask_batch: 当前批次的掩码 [1, seq_length, seq_length]

        Returns:
            Tuple[torch.Tensor, List[torch.Tensor]]:
                处理后的批次输出 [1, seq_length, embed_dim]
                注意力权重列表
        """
        # 输入验证
        if x_batch is None:
            error_msg = "批次输入不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if batch_rates is None or len(batch_rates) == 0:
            error_msg = "扩张率列表不能为None或空"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if len(batch_rates) != self.num_scales:
            error_msg = f"扩张率列表长度({len(batch_rates)})与尺度数量({self.num_scales})不匹配"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 记录处理信息
        self.logger.debug(f"开始处理批次 - 输入形状: {x_batch.shape}, 尺度数量: {len(batch_rates)}")

        # 并行处理不同尺度
        scale_outputs = []
        scale_weights = []

        # 非流版本 - 串行处理
        if not self.use_streams:
            self.logger.debug("使用串行处理模式")
            for i in range(self.num_scales):
                try:
                    # 获取当前尺度的扩张率
                    dilation = batch_rates[i].item()

                    if self.verbose_logging:
                        self.logger.info(f"AdaptiveDilationAttention 处理尺度 {i+1}/{self.num_scales}, 扩张率: {dilation}")
                except Exception as e:
                    error_msg = f"AdaptiveDilationAttention 获取扩张率失败: {e!s}"
                    self.logger.error(error_msg, exc_info=True)
                    raise RuntimeError(error_msg) from e

                # 处理当前尺度
                self.logger.debug(f"处理尺度 {i+1}/{self.num_scales} - 扩张率: {dilation}")
                scale_output, scale_weight = self._process_single_scale(
                    x_batch,
                    int(dilation),  # 确保是整数
                    mask_batch,
                    i
                )
                scale_outputs.append(scale_output)
                scale_weights.append(scale_weight)
        else:
            # 流版本 - 并行处理
            self.logger.debug("使用CUDA流并行处理模式")

            # 检查流是否可用
            if len(self.scale_streams) == 0:
                error_msg = "尺度CUDA流不可用，无法并行处理"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg)
            else:
                # 计算每个流处理的尺度数
                scales_per_stream = (self.num_scales + len(self.scale_streams) - 1) // len(self.scale_streams)
                self.logger.debug(f"并行处理尺度 - 流数量: {len(self.scale_streams)}, 每流尺度数: {scales_per_stream}")

                # 在不同流上并行处理尺度
                for stream_idx in range(min(len(self.scale_streams), self.num_scales)):
                    start_scale = stream_idx * scales_per_stream
                    end_scale = min(start_scale + scales_per_stream, self.num_scales)

                    if start_scale >= self.num_scales:
                        break

                    self.logger.debug(f"流 {stream_idx} 处理尺度范围: [{start_scale}, {end_scale})")

                    try:
                        with torch.cuda.stream(self.scale_streams[stream_idx]):
                            stream_outputs = []
                            stream_weights = []

                            # 处理当前流负责的尺度
                            for i in range(start_scale, end_scale):
                                try:
                                    # 获取当前尺度的扩张率
                                    dilation = batch_rates[i].item()
                                    self.logger.debug(f"流 {stream_idx} 处理尺度 {i} - 扩张率: {dilation}")
                                except Exception as e:
                                    error_msg = f"流 {stream_idx} 获取尺度 {i} 扩张率失败: {e!s}"
                                    self.logger.error(error_msg)
                                    raise RuntimeError(error_msg) from e

                                # 处理当前尺度
                                scale_output, scale_weight = self._process_single_scale(
                                    x_batch,
                                    int(dilation),  # 确保是整数
                                    mask_batch,
                                    i
                                )
                                stream_outputs.append(scale_output)
                                stream_weights.append(scale_weight)

                            # 添加当前流的结果
                            scale_outputs.extend(stream_outputs)
                            scale_weights.extend(stream_weights)
                            self.logger.debug(f"流 {stream_idx} 处理完成 - 结果数量: {len(stream_outputs)}")
                    except Exception as e:
                        error_msg = f"流 {stream_idx} 处理失败: {e!s}"
                        self.logger.error(error_msg)
                        raise RuntimeError(error_msg) from e

                # 同步所有尺度流
                self.logger.debug("同步所有尺度CUDA流")
                for stream_idx in range(min(len(self.scale_streams), (self.num_scales + scales_per_stream - 1) // scales_per_stream)):
                    try:
                        cuda_manager.synchronize_stream(self.scale_streams[stream_idx])
                        self.logger.debug(f"流 {stream_idx} 同步完成")
                    except Exception as e:
                        error_msg = f"流 {stream_idx} 同步失败: {e!s}"
                        self.logger.error(error_msg)
                        raise RuntimeError(error_msg) from e

        # 合并所有尺度的输出
        multi_scale_output = torch.stack(scale_outputs, dim=-1).mean(dim=-1)

        return multi_scale_output, scale_weights

    def _process_single_scale(
        self,
        x_batch: torch.Tensor,
        dilation: int,
        mask_batch: torch.Tensor | None,
        scale_idx: int
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """处理单个尺度

        Args:
            x_batch: 单个批次的输入 [1, seq_length, embed_dim]
            dilation: 当前尺度的扩张率
            mask_batch: 当前批次的掩码 [1, seq_length, seq_length]
            scale_idx: 尺度索引

        Returns:
            Tuple[torch.Tensor, torch.Tensor]:
                处理后的尺度输出 [1, seq_length, embed_dim]
                注意力权重 [1, num_heads, seq_length, seq_length]
        """
        # 输入验证
        if x_batch is None:
            error_msg = "尺度处理输入不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if scale_idx < 0 or scale_idx >= self.num_scales:
            error_msg = f"尺度索引超出范围 [0, {self.num_scales}), 当前值: {scale_idx}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if dilation < 1:
            error_msg = f"扩张率必须大于等于1，当前值: {dilation}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 记录处理信息
        seq_length = x_batch.size(1)
        self.logger.debug(f"处理尺度 {scale_idx+1}/{self.num_scales} - 扩张率: {dilation}, 序列长度: {seq_length}")

        try:
            # 记录开始时间
            start_time = time.time() if self.verbose_logging else None

            if dilation > 1:
                # 创建扩张索引
                indices = torch.arange(0, seq_length, dilation, device=x_batch.device)
                if indices.size(0) < 2:  # 确保至少有2个时间步
                    indices = torch.cat([indices, torch.tensor([seq_length-1], device=x_batch.device)])
                    self.logger.debug(f"扩张索引数量不足，添加结束索引，新数量: {indices.size(0)}")

                self.logger.debug(f"扩张索引数量: {indices.size(0)}, 范围: [{indices[0].item()}, {indices[-1].item()}]")

                # 提取子序列
                sub_seq = x_batch[:, indices, :]
                self.logger.debug(f"子序列形状: {sub_seq.shape}")

                # 创建子序列的掩码
                sub_mask = None
                if mask_batch is not None:
                    try:
                        sub_mask = mask_batch[:, indices][:, :, indices]
                        self.logger.debug(f"子掩码形状: {sub_mask.shape}")
                    except Exception as e:
                        error_msg = f"创建子掩码失败: {e!s}"
                        self.logger.error(error_msg)
                        raise RuntimeError(error_msg) from e

                # 应用注意力
                self.logger.debug(f"调用注意力层 {scale_idx+1} - 开始")

                try:
                    # 请求返回每个头的权重
                    attn_output, attn_weight = self.attention_layers[scale_idx](
                        query=sub_seq,
                        key=sub_seq,
                        value=sub_seq,
                        attn_mask=sub_mask,
                        need_weights=True,
                        average_attn_weights=False
                    )
                    # attn_weight 形状现在是 [N, H, L, S]
                    self.logger.debug(f"注意力计算成功 - 输出形状: {attn_output.shape}, 权重形状: {attn_weight.shape}")
                    self.logger.debug(f"数据类型 - 输出: {attn_output.dtype}, 权重: {attn_weight.dtype}, 输入: {x_batch.dtype}")
                except Exception as e:
                    self.logger.error(f"注意力计算失败: {e!s}")
                    raise

                # 创建完整输出 - 确保与attn_output具有相同的数据类型
                full_output = torch.zeros_like(x_batch, dtype=attn_output.dtype)

                # 使用高效的索引操作填充完整输出
                full_output[:, indices] = attn_output

                # 创建完整的注意力权重 - 确保与attn_weight具有相同的数据类型
                full_weight = torch.zeros(
                    (1, self.num_heads, seq_length, seq_length),
                    device=x_batch.device,
                    dtype=attn_weight.dtype  # 确保数据类型匹配
                )

                # 使用高效的索引操作填充完整权重
                # 验证注意力权重形状 (现在是 [N, H, L, S])
                self.logger.debug(f"注意力权重形状: {attn_weight.shape}, 配置头数: {self.num_heads}")
                self.logger.debug(f"注意力权重数据类型: {attn_weight.dtype}, 完整权重数据类型: {full_weight.dtype}")

                # 移除错误的头数检查逻辑

                # 使用配置的头数进行循环
                for h in range(self.num_heads):
                    try:
                        # attn_weight[0, h] 是 [L, S]
                        # 确保数据类型一致性
                        attn_weight_h = self._ensure_dtype_consistency(attn_weight[0, h], full_weight)
                        full_weight[0, h, indices[:, None], indices[None, :]] = attn_weight_h
                    except Exception as e:
                        error_msg = f"填充注意力权重失败 - 头 {h}: {e!s}"
                        self.logger.error(error_msg)
                        raise RuntimeError(error_msg) from e

                # 记录执行时间
                if self.verbose_logging and start_time is not None:
                    elapsed_time = (time.time() - start_time) * 1000  # 毫秒
                    self.logger.info(f"尺度 {scale_idx+1} 处理完成 - 扩张率: {dilation}, 耗时: {elapsed_time:.2f}ms")

                return full_output, full_weight
            else:
                # 无扩张，直接应用注意力
                self.logger.debug(f"无扩张处理 - 直接应用注意力层 {scale_idx+1}")

                try:
                    # 请求返回每个头的权重
                    full_output, full_weight = self.attention_layers[scale_idx](
                        query=x_batch,
                        key=x_batch,
                        value=x_batch,
                        attn_mask=mask_batch,
                        need_weights=True,
                        average_attn_weights=False
                    )
                    # full_weight 形状现在是 [N, H, L, S]
                    self.logger.debug(f"注意力计算成功 - 输出形状: {full_output.shape}, 权重形状: {full_weight.shape}")
                    self.logger.debug(f"数据类型 - 输出: {full_output.dtype}, 权重: {full_weight.dtype}, 输入: {x_batch.dtype}")
                except Exception as e:
                    self.logger.error(f"注意力计算失败: {e!s}")
                    raise

                # 记录执行时间
                if self.verbose_logging and start_time is not None:
                    elapsed_time = (time.time() - start_time) * 1000  # 毫秒
                    self.logger.info(f"尺度 {scale_idx+1} 处理完成 - 无扩张, 耗时: {elapsed_time:.2f}ms")

                return full_output, full_weight
        except Exception as e:
            # 记录错误并抛出异常
            error_msg = f"处理尺度 {scale_idx+1} 失败: {e!s}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e


class MultiLayerAttention(BaseModule):
    """多层注意力机制 - 堆叠多个注意力层以增强表示能力"""

    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        num_layers: int,
        dropout: float
    ):
        """初始化多层注意力

        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            num_layers: 层数
            dropout: Dropout比率
        """
        super().__init__("MultiLayerAttention")

        # 参数验证 - 不使用默认值，必须提供有效参数
        if embed_dim <= 0:
            error_msg = f"嵌入维度必须为正数，当前值: {embed_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if num_heads <= 0:
            error_msg = f"注意力头数必须为正数，当前值: {num_heads}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if num_layers <= 0:
            error_msg = f"层数必须为正数，当前值: {num_layers}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if dropout < 0 or dropout >= 1:
            error_msg = f"Dropout比率必须在[0, 1)范围内，当前值: {dropout}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 记录参数使用情况
        self.logger.info(f"初始化多层注意力 - 嵌入维度: {embed_dim}, 注意力头数: {num_heads}, 层数: {num_layers}, dropout: {dropout}")

        # 保存参数
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.dropout_p = dropout

        # 注意力层
        self.logger.info(f"创建 {num_layers} 个注意力层，每层 {num_heads} 个注意力头")
        self.attention_layers = nn.ModuleList([
            nn.MultiheadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout,
                batch_first=True
            ) for _ in range(num_layers)
        ])

        # 层归一化
        self.logger.debug(f"创建层归一化模块 - 嵌入维度: {embed_dim}")
        self.layer_norms1 = nn.ModuleList([
            nn.LayerNorm(embed_dim) for _ in range(num_layers)
        ])
        self.layer_norms2 = nn.ModuleList([
            nn.LayerNorm(embed_dim) for _ in range(num_layers)
        ])

        # 前馈网络 - 使用固定的扩展比例4
        ffn_dim = embed_dim * 4
        self.logger.debug(f"创建前馈网络 - 内部维度: {ffn_dim}")
        self.ffns = nn.ModuleList([
            nn.Sequential(
                nn.Linear(embed_dim, ffn_dim),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(ffn_dim, embed_dim)
            ) for _ in range(num_layers)
        ])

        # Dropout
        self.dropout = nn.Dropout(dropout)
        self.logger.info(f"多层注意力初始化完成 - 总层数: {num_layers}")

    def forward(self, x: torch.Tensor, mask: torch.Tensor | None = None) -> tuple[torch.Tensor, list[torch.Tensor]]:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, embed_dim]
            mask: 注意力掩码 [batch_size, seq_length, seq_length]

        Returns:
            Tuple[torch.Tensor, List[torch.Tensor]]:
                输出 [batch_size, seq_length, embed_dim]
                注意力权重列表
        """
        from datetime import datetime

        # 输入验证
        validation_start = time.time()
        if x is None:
            error_msg = "输入张量不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if x.dim() != 3:
            error_msg = f"输入张量必须为3维 [batch_size, seq_length, embed_dim]，当前维度: {x.dim()}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if x.size(2) != self.embed_dim:
            error_msg = f"输入张量的嵌入维度({x.size(2)})与模型的嵌入维度({self.embed_dim})不匹配"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 输入验证完成 - 耗时: {time.time() - validation_start:.2f}秒")

        # 记录开始时间
        start_time = time.time()

        # 记录输入形状
        batch_size, seq_length, _ = x.shape
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 多层注意力开始处理 - 输入形状: [{batch_size}, {seq_length}, {self.embed_dim}]")

        attention_weights_list = []

        # 应用多层注意力
        for i in range(len(self.attention_layers)):
            layer_start_time = time.time()
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始处理注意力层 {i+1}/{self.num_layers}")

            # 注意力层
            norm_start = time.time()
            residual = x
            x_norm = self.layer_norms1[i](x)
            self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 层归一化完成 - 耗时: {time.time() - norm_start:.2f}秒")

            # 应用注意力
            attn_start = time.time()
            try:
                self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始应用注意力机制")
                attn_output, attn_weights = self.attention_layers[i](
                    query=x_norm,
                    key=x_norm,
                    value=x_norm,
                    attn_mask=mask
                )
                self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 注意力计算完成 - 输出形状: {attn_output.shape}, 耗时: {time.time() - attn_start:.2f}秒")
            except Exception as e:
                self.logger.error(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 注意力层 {i+1} 计算失败: {e!s}")
                raise

            # 残差连接
            residual_start = time.time()
            x = residual + self.dropout(attn_output)
            self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 残差连接完成 - 耗时: {time.time() - residual_start:.2f}秒")

            # 前馈网络
            ffn_start = time.time()
            residual = x
            x_norm = self.layer_norms2[i](x)
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始前馈网络计算")

            try:
                ffn_output = self.ffns[i](x_norm)
                self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 前馈网络计算完成 - 输出形状: {ffn_output.shape}, 耗时: {time.time() - ffn_start:.2f}秒")
            except Exception as e:
                self.logger.error(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 前馈网络层 {i+1} 计算失败: {e!s}")
                raise

            x = residual + self.dropout(ffn_output)

            # 收集注意力权重
            attention_weights_list.append(attn_weights)

            # 检查内存使用情况
            if torch.cuda.is_available():
                mem_allocated = torch.cuda.memory_allocated() / (1024 ** 2)
                mem_reserved = torch.cuda.memory_reserved() / (1024 ** 2)
                self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] CUDA内存使用情况 - 已分配: {mem_allocated:.2f}MB, 已保留: {mem_reserved:.2f}MB")

            # 记录层执行时间
            layer_time = (time.time() - layer_start_time) * 1000  # 毫秒
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 注意力层 {i+1}/{self.num_layers} 处理完成 - 耗时: {layer_time:.2f}ms")

        # 记录总执行时间
        total_time = (time.time() - start_time) * 1000  # 毫秒
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 多层注意力处理完成 - 总耗时: {total_time:.2f}ms, 输出形状: {x.shape}")

        return x, attention_weights_list
