"""
测试模块路径：tests/test_predict.py
测试目标：验证src/predict.py的核心预测功能

测试要点：
1. 预测流程完整性
2. 输入数据验证
3. 输出结果格式
4. 异常处理
"""

import unittest

import numpy as np
import pytest

from src.predict import Predictor
from src.utils.config_manager import ConfigManager
from src.utils.cuda.manager import CUDAManager


@pytest.mark.batch1  # 核心预测功能测试
class TestPredictor(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        cls.config = ConfigManager.from_yaml("tests/test_config.yaml")
        cls.cuda_manager = CUDAManager()

    def test_predict_flow(self):
        """测试完整预测流程"""
        # 模拟输入数据 - 支持动态特征维度
        mock_data = np.random.rand(10, 15)  # 任意特征维度

        # 创建预测器实例
        predictor = Predictor(self.config, self.cuda_manager)

        # 执行预测
        result = predictor.predict(mock_data)

        # 验证输出格式
        self.assertIsInstance(result, np.ndarray)
        self.assertEqual(result.shape[0], 10)  # 应与输入行数一致

    def test_invalid_input(self):
        """测试无效输入处理"""
        predictor = Predictor(self.config, self.cuda_manager)

        with self.assertRaises(ValueError):
            # 输入数据形状无效
            invalid_data = np.random.rand(0, 10)  # 空数组
            predictor.predict(invalid_data)

if __name__ == '__main__':
    unittest.main()
