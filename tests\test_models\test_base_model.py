"""
测试模块路径：tests/test_base_model.py
测试目标：验证src/models/base/base_model.py的基础模型功能

测试要点：
1. 配置验证测试
2. 训练流程测试
3. 优化器初始化测试
4. 混合精度训练测试
5. 错误处理测试
"""

import unittest
from unittest.mock import MagicMock, patch

import torch
from torch import nn
from torch.utils.data import Dataset

from src.models.base.base_model import BaseModel
from src.utils.config_manager import ConfigManager


class MockModel(BaseModel):
    """用于测试的模拟模型"""
    def __init__(self, config_manager):
        super().__init__(config_manager, "MockModel")
        self.layer = nn.Linear(10, 1)

    def forward(self, x):
        return self.layer(x)

class MockDataset(Dataset):
    """用于测试的模拟数据集"""
    def __init__(self, num_samples=100):
        self.data = torch.randn(num_samples, 10)
        self.targets = torch.randn(num_samples, 1)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return {
            'features': self.data[idx],
            'target': self.targets[idx]
        }

import contextlib

import pytest


@pytest.mark.batch1  # 核心基础模型测试
class TestBaseModel(unittest.TestCase):
    def setUp(self):
        """测试前准备"""
        # 创建完整模拟配置
        self.config = MagicMock(spec=ConfigManager)
        self.config.model = MagicMock()
        self.config.model.type = "test"
        self.config.model.input_dim = 10
        self.config.model.output_dim = 1
        self.config.model.loss_type = "mse"
        self.config.training = MagicMock()
        self.config.training.num_epochs = 2
        self.config.training.batch_size = 10
        self.config.training.optimizer = MagicMock()
        self.config.training.optimizer.type = "adam"
        self.config.training.optimizer.learning_rate = 0.001
        self.config.training.optimizer.beta1 = 0.9
        self.config.training.optimizer.beta2 = 0.999
        self.config.training.optimizer.eps = 1e-8
        self.config.training.optimizer.weight_decay = 0.0
        self.config.training.scheduler = MagicMock()
        self.config.training.scheduler.type = "none"
        self.config.training.scheduler.monitor = "loss"
        self.config.training.mixed_precision = MagicMock()
        self.config.training.mixed_precision.enabled = False
        self.config.training.mixed_precision.init_scale = 65536.0
        self.config.training.mixed_precision.growth_factor = 2.0
        self.config.training.mixed_precision.backoff_factor = 0.5
        self.config.training.mixed_precision.growth_interval = 2000

        # 创建测试模型
        self.model = MockModel(self.config)

    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        with patch.object(self.model._logger, 'debug') as mock_debug:
            self.model._validate_config()
            mock_debug.assert_called_once()

        # 直接测试内部逻辑
        # 检查hasattr逻辑
        self.assertTrue(hasattr(self.config, 'model'))
        self.assertTrue(hasattr(self.config.model, 'type'))

    def test_forward_not_implemented(self):
        """测试未实现forward方法"""
        class InvalidModel(BaseModel):
            def __init__(self, config):
                super().__init__(config, "InvalidModel")

        with self.assertRaises(NotImplementedError):
            model = InvalidModel(self.config)
            model(torch.randn(1, 10))

    def test_optimizer_initialization(self):
        """测试优化器初始化"""
        # 测试Adam优化器
        optimizer = self.model.get_optimizer()
        self.assertIsInstance(optimizer, torch.optim.Adam)
        self.assertEqual(optimizer.param_groups[0]['lr'], 0.001)

        # 测试SGD优化器
        self.config.training.optimizer.type = "sgd"
        self.config.training.optimizer.momentum = 0.9
        optimizer = self.model.get_optimizer()
        self.assertIsInstance(optimizer, torch.optim.SGD)
        self.assertEqual(optimizer.param_groups[0]['momentum'], 0.9)

    def test_training_loop(self):
        """测试训练循环"""
        # 创建模拟数据集
        train_dataset = MockDataset(100)
        val_dataset = MockDataset(20)

        # 运行训练 (按照新签名传递参数)
        history = self.model.fit(train_dataset=train_dataset, batch_size=10, val_dataset=val_dataset)

        # 验证训练结果
        self.assertEqual(len(history['train_loss']), 2)
        self.assertEqual(len(history['val_loss']), 2)
        self.assertLess(history['train_loss'][-1], history['train_loss'][0])

    def test_mixed_precision(self):
        """测试混合精度训练"""
        # 启用混合精度
        self.config.training.mixed_precision.enabled = True
        self.config.training.mixed_precision.init_scale = 65536.0
        self.config.training.mixed_precision.growth_factor = 2.0
        self.config.training.mixed_precision.backoff_factor = 0.5
        self.config.training.mixed_precision.growth_interval = 2000

        # 创建新模型
        model = MockModel(self.config)

        # 验证混合精度初始化
        self.assertTrue(hasattr(model, 'scaler'))
        self.assertEqual(model.scaler._init_scale, 65536.0)

    def test_error_handling(self):
        """测试错误处理"""
        # 模拟无效输入 - 使用1D张量测试维度验证
        invalid_input = torch.randn(10)
        with contextlib.suppress(ValueError):
            self.model.validate_input(invalid_input)

        # 模拟训练错误
        with patch.object(self.model, '_train_epoch', side_effect=RuntimeError("模拟训练错误")), contextlib.suppress(RuntimeError):
            # 传递必需的 batch_size
            self.model.fit(train_dataset=MockDataset(10), batch_size=5)

    def test_input_validation(self):
        """测试输入验证"""
        # 测试有效输入
        valid_input = torch.randn(10, 10)
        with patch.object(self.model._logger, 'debug') as mock_debug:
            self.model.validate_input(valid_input)
            mock_debug.assert_called_once()

        # 无效输入类型 - 测试维度验证
        invalid_input = torch.randn(10)
        with patch.object(self.model._logger, 'error') as mock_error:
            with contextlib.suppress(ValueError):
                self.model.validate_input(invalid_input)
            # 确保logger.error被调用
            mock_error.assert_called_with("输入验证失败: 输入维度过低: 1")

        # 测试NaN检测
        invalid_input = torch.randn(10, 10)
        invalid_input[0, 0] = float('nan')
        with patch.object(self.model._logger, 'error') as mock_error:
            with contextlib.suppress(ValueError):
                self.model.validate_input(invalid_input)
            # 确保logger.error被调用
            mock_error.assert_called_with("输入验证失败: 输入包含NaN值")

    def test_learning_rate_scheduler(self):
        """测试学习率调度"""
        # 配置调度器
        scheduler_config = MagicMock()
        scheduler_config.type = "plateau"
        scheduler_config.mode = "min"
        scheduler_config.factor = 0.1
        scheduler_config.patience = 2
        scheduler_config.min_lr = 1e-5
        scheduler_config.monitor = "loss"
        self.config.training.scheduler = scheduler_config

        # 初始化模型并模拟训练
        model = MockModel(self.config)
        with patch.object(model, 'update_learning_rate') as mock_update:
            # 按照新签名传递参数
            model.fit(train_dataset=MockDataset(10), batch_size=5, val_dataset=MockDataset(5))
            self.assertTrue(mock_update.called)

        # 验证调度器存在
        self.assertIsNotNone(model.scheduler)

    def test_resource_monitoring(self):
        """测试资源监控"""
        # 直接调用监控方法
        with patch.object(self.model._logger, 'debug') as mock_debug:
            self.model._monitor_resources()
            mock_debug.assert_called()

        # 验证训练过程中调用
        original_monitor = self.model._monitor_resources
        self.model._monitor_resources = MagicMock()
        try:
            # 传递必需的 batch_size
            self.model.fit(train_dataset=MockDataset(10), batch_size=5)
            self.assertGreaterEqual(self.model._monitor_resources.call_count, 1)
        finally:
            self.model._monitor_resources = original_monitor

if __name__ == '__main__':
    unittest.main()
