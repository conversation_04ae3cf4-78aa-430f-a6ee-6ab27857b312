"""测试GAN工具函数模块"""

from typing import Any

import pytest
import torch

from src.models.gan.utils import (
    check_device_consistency,
    check_numerical_stability,
    check_shape_compatibility,
    validate_tensor,
)


def test_check_numerical_stability():
    # 创建测试张量
    valid_tensor = torch.tensor([1.0, 2.0, 3.0])
    nan_tensor = torch.tensor([1.0, float('nan'), 3.0])
    inf_tensor = torch.tensor([1.0, float('inf'), 3.0])

    # 测试有效张量
    assert check_numerical_stability(valid_tensor, "valid", fix_tensor=False) is None
    assert check_numerical_stability(valid_tensor, "valid", fix_tensor=True) == valid_tensor

    # 测试包含NaN的张量
    fixed_nan = check_numerical_stability(nan_tensor, "nan", fix_tensor=True)
    assert fixed_nan is not None
    assert not torch.isnan(fixed_nan).any()

    # 测试包含Inf的张量
    fixed_inf = check_numerical_stability(inf_tensor, "inf", fix_tensor=True)
    assert fixed_inf is not None
    assert not torch.isinf(fixed_inf).any()

def test_check_device_consistency():
    # 创建测试张量
    tensor1 = torch.tensor([1.0])
    tensor2 = torch.tensor([2.0])

    # 测试相同设备的张量
    check_device_consistency(tensor1, tensor2)  # 不应抛出异常

    # 测试单个张量
    check_device_consistency(tensor1)  # 不应抛出异常

    # 测试空输入
    check_device_consistency()  # 不应抛出异常

    if torch.cuda.is_available():
        gpu_tensor = torch.tensor([1.0]).cuda()
        with pytest.raises(ValueError):
            check_device_consistency(tensor1, gpu_tensor)

def test_check_shape_compatibility():
    # 创建测试张量
    tensor1 = torch.randn(2, 3, 4)
    tensor2 = torch.randn(2, 3, 4)
    tensor3 = torch.randn(3, 4, 5)

    # 测试相同形状的张量
    check_shape_compatibility(tensor1, tensor2)  # 不应抛出异常

    # 测试不同形状的张量
    with pytest.raises(ValueError):
        check_shape_compatibility(tensor1, tensor3)

    # 测试期望形状
    check_shape_compatibility(
        tensor1, tensor2,
        expected_shapes=[(2, 3, 4), (2, 3, 4)]
    )  # 不应抛出异常

    # 测试期望形状不匹配
    with pytest.raises(ValueError):
        check_shape_compatibility(
            tensor1, tensor2,
            expected_shapes=[(2, 3, 4), (3, 4, 5)]
        )

def test_validate_tensor():
    # 创建测试张量
    valid_tensor = torch.randn(2, 3, 4)
    tensor_with_nan = torch.tensor([1.0, float('nan'), 3.0])
    tensor_with_inf = torch.tensor([1.0, float('inf'), 3.0])

    # 测试有效张量
    assert validate_tensor(valid_tensor, "valid") is True

    # 测试无效类型 - 使用Any来避免类型检查错误
    invalid_input: Any = "not a tensor"
    with pytest.raises(ValueError):
        validate_tensor(invalid_input, "invalid_type")

    # 测试维度检查
    with pytest.raises(ValueError):
        validate_tensor(valid_tensor, "wrong_dims", expected_dims=2)

    # 测试NaN检查
    with pytest.raises(ValueError):
        validate_tensor(tensor_with_nan, "nan")

    # 测试Inf检查
    with pytest.raises(ValueError):
        validate_tensor(tensor_with_inf, "inf")
