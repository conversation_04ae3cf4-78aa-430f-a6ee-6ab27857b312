"""
超参数优化日志配置模块

本模块提供专门针对超参数优化过程的日志配置功能，支持灵活的日志级别设置、
多目标输出（控制台和文件）以及从配置文件加载设置。

主要功能:
----------
1. 创建专用于优化过程的日志记录器
2. 支持同时输出到控制台和文件
3. 支持从配置文件加载日志设置
4. 提供不同级别的日志记录
5. 自动创建日志目录和文件
6. 支持日志文件轮转

使用方式:
----------
1. 直接使用setup_optimization_logger创建日志记录器:

```python
from src.optimization.logging_config import setup_optimization_logger

logger = setup_optimization_logger(
    log_dir="logs/optimization",
    console_level="INFO",
    file_level="DEBUG",
    config_path="config.yaml"
)

logger.info("这是一条信息日志")
logger.debug("这是一条调试日志")
```

2. 使用get_optimization_logger获取已配置的日志记录器:

```python
from src.optimization.logging_config import get_optimization_logger

logger = get_optimization_logger()
logger.info("使用已配置的日志记录器")
```

配置参数:
----------
- log_dir: 日志文件目录，默认为"logs/optimization"
- console_level: 控制台日志级别，默认为"INFO"
- file_level: 文件日志级别，默认为"DEBUG"
- config_path: 配置文件路径，如果提供则从配置文件加载设置

日志文件:
----------
日志文件命名格式为 optimization_YYYYMMDD_HHMMSS.log，保存在指定的log_dir目录下。
文件大小超过10MB时会自动轮转，最多保留5个备份文件。

相关文件:
----------
- optimize.py: 优化入口脚本
- src/optimization/hyperparameter_optimizer.py: 优化核心模块
- config.yaml: 基础配置文件（可能包含日志设置）
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler

import yaml

# 日志级别映射
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

def setup_optimization_logger(
    log_dir: str = "logs/optimization",
    console_level: str = "INFO",
    file_level: str = "DEBUG",
    config_path: str | None = None
) -> logging.Logger:
    """
    设置优化过程专用的日志记录器。

    Args:
        log_dir: 日志文件目录
        console_level: 控制台日志级别
        file_level: 文件日志级别
        config_path: 配置文件路径，如果提供则从配置文件加载设置

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)

    # 如果提供了配置文件路径，从配置文件加载设置
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 从配置中获取日志设置
            logging_config = config.get('logging', {})
            console_level = logging_config.get('handlers', {}).get('console', {}).get('level', console_level)
            file_level = logging_config.get('handlers', {}).get('file', {}).get('level', file_level)
            log_dir = logging_config.get('file', {}).get('path', log_dir)
            os.makedirs(log_dir, exist_ok=True)
        except Exception as e:
            print(f"加载日志配置失败: {e}，使用默认设置")

    # 创建日志记录器
    logger = logging.getLogger("OptimizationLogger")
    logger.setLevel(logging.DEBUG)  # 设置为最低级别，让处理器决定过滤级别

    # 清除现有处理器
    if logger.handlers:
        logger.handlers.clear()

    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(LOG_LEVELS.get(console_level, logging.INFO))
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 创建文件处理器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"optimization_{timestamp}.log")
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(LOG_LEVELS.get(file_level, logging.DEBUG))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 记录初始化信息
    logger.info("优化日志记录器初始化完成")
    logger.info(f"- 控制台日志级别: {console_level}")
    logger.info(f"- 文件日志级别: {file_level}")
    logger.info(f"- 日志文件: {log_file}")

    return logger

def get_optimization_logger() -> logging.Logger:
    """
    获取优化过程的日志记录器。
    如果记录器不存在，则创建一个新的。

    Returns:
        logging.Logger: 优化过程的日志记录器
    """
    logger = logging.getLogger("OptimizationLogger")

    # 如果记录器没有处理器，则进行初始化
    if not logger.handlers:
        return setup_optimization_logger()

    return logger
