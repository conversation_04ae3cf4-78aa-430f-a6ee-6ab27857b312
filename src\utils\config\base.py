"""配置基类模块 - 提供所有配置类的基础功能"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any


@dataclass
class BaseConfig:
    """配置基类"""
    # 移除默认值，强制子类或加载时提供
    noise_dim: int
    dimensions: dict[str, Any]

    def __post_init__(self):
        """基础配置初始化方法 (空的默认实现)"""
        pass

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            'dimensions': self.dimensions,
            'noise_dim': self.noise_dim
        }
