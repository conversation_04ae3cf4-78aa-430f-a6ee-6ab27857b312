"""
测试模块路径：tests/test_temporal.py
测试目标：验证src/models/gan/components/temporal.py的时序组件功能

测试要点：
1. 时序一致性测试
2. 注意力权重测试
3. 卷积输出形状测试
4. 投影维度转换测试
5. 残差连接测试
"""

import unittest

import pytest
import torch

from src.models.gan.components.common import (
    OutputProjection,  # Import from new location
)
from src.models.gan.components.temporal import (
    TemporalCoherence,
    TemporalConvolution,
    TemporalMultiHeadWrapper,  # Renamed from TemporalAttention
)


@pytest.mark.batch3  # 时序组件测试
class TestTemporalComponents(unittest.TestCase):
    def setUp(self):
        """测试前准备"""
        self.batch_size = 4
        self.seq_length = 50
        self.hidden_dim = 64
        self.input_dim = 32
        self.output_dim = 16

    def test_temporal_coherence(self):
        """测试时序一致性模块"""
        # 创建模块
        coherence = TemporalCoherence(self.hidden_dim)

        # 生成测试输入
        x = torch.randn(self.batch_size, self.seq_length, self.hidden_dim)

        # 前向传播
        output = coherence(x)

        # 验证输出形状
        self.assertEqual(output.shape, (self.batch_size, self.seq_length, self.hidden_dim))

        # 验证残差连接
        self.assertNotEqual(torch.sum(output - x), 0)

    def test_temporal_attention(self):
        """测试时序注意力模块"""
        # 创建模块
        attention = TemporalMultiHeadWrapper(self.hidden_dim) # Renamed

        # 生成测试输入
        x = torch.randn(self.batch_size, self.seq_length, self.hidden_dim)

        # 前向传播
        output, weights = attention(x)

        # 验证输出形状
        self.assertEqual(output.shape, (self.batch_size, self.seq_length, self.hidden_dim))

        # 验证注意力权重形状 (注意: nn.MultiheadAttention 默认 average_attn_weights=True, 返回形状为 [batch_size, seq_length, seq_length])
        self.assertEqual(weights.shape, (self.batch_size, self.seq_length, self.seq_length))

    def test_temporal_convolution(self):
        """测试时序卷积模块"""
        # 创建模块
        conv = TemporalConvolution(self.input_dim, self.output_dim)

        # 生成测试输入
        x = torch.randn(self.batch_size, self.seq_length, self.input_dim)

        # 前向传播
        output = conv(x)

        # 验证输出形状
        self.assertEqual(output.shape, (self.batch_size, self.seq_length, self.output_dim))

        # 验证序列长度不变
        self.assertEqual(output.size(1), self.seq_length)

    def test_output_projection(self):
        """测试输出投影模块"""
        # 创建模块
        projection = OutputProjection(self.input_dim, self.output_dim)

        # 生成测试输入
        x = torch.randn(self.batch_size, self.seq_length, self.input_dim)

        # 前向传播
        output = projection(x)

        # 验证输出形状
        self.assertEqual(output.shape, (self.batch_size, self.seq_length, self.output_dim))

        # 验证维度转换
        self.assertEqual(output.size(-1), self.output_dim)

    def test_components_together(self):
        """测试组件组合使用"""
        # 创建各组件
        coherence = TemporalCoherence(self.hidden_dim)
        attention = TemporalMultiHeadWrapper(self.hidden_dim) # Renamed
        conv = TemporalConvolution(self.hidden_dim, self.hidden_dim)
        projection = OutputProjection(self.hidden_dim, self.output_dim)

        # 生成测试输入
        x = torch.randn(self.batch_size, self.seq_length, self.hidden_dim)

        # 依次应用各组件
        x = coherence(x)
        x, _ = attention(x)
        x = conv(x)
        output = projection(x)

        # 验证最终输出形状
        self.assertEqual(output.shape, (self.batch_size, self.seq_length, self.output_dim))

if __name__ == '__main__':
    unittest.main()
