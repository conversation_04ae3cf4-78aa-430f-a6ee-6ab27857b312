"""特征选择系统 - 多阶段、聚焦领先信号的特征筛选

注意：此模块已重构为按功能领域组织的模块结构，位于src.data.preprocessing包中。
为保持向后兼容性，此模块重新导出所有特征选择类和函数。

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理 (读取滞后相关性参数等)
   - src/utils/logger.py: 日志系统 (记录筛选过程)

2. 共用模块:
   - src/data/data_loader.py: 数据加载 (提供数据来源)
   - src/data/preprocessing/base_selector.py: 特征选择器基类
   - src/data/preprocessing/feature_selector.py: 特征选择器实现

3. 配置文件:
   - config.yaml:
     └── feature_selection:
         ├── lagged_corr:
         │   ├── min_abs_corr: 最小绝对滞后相关性阈值 (可配置)
         │   └── max_lag: 最大滞后步长 (可配置)
         └── noise_detection: # 噪声检测配置
             └── low_variance_threshold: 低方差阈值
             └── high_correlation_threshold: 高相关性阈值

4. 父类模块:
   - src/data/preprocessing/base_selector.py: BaseFeatureSelector，选择器基类

5. 同阶段数据处理模块:
   - src/data/data_pipeline.py: 调用本模块
   - src/data/feature_engineer.py: 特征工程 (本模块处理其输出)
   - src/data/standardization.py: 标准化 (本模块处理其输出)

核心功能：
1. 多阶段特征筛选:
   - 阶段一: 基础质量检查 (零方差, 高冗余)
   - 阶段二: 领先信号筛选 (滞后相关性, 模型重要性)
   - 阶段三: 最终审查与日志记录
2. 下游维度约束兼容: 确保输出特征数满足后续模型要求

使用规范：
- 输入为 Pandas DataFrame (包含特征和目标 'value15')
- 输出为选定特征的名称列表
- 依赖 ConfigManager 和 LoggerFactory
- 部分关键参数可通过 config.yaml 配置
"""

# 从新模块导入所有特征选择类和函数
from src.data.preprocessing.base_selector import BaseFeatureSelector
from src.data.preprocessing.feature_selector import FeatureSelector

# 导出所有公共接口
__all__ = [
    'BaseFeatureSelector',
    'FeatureSelector'
]
