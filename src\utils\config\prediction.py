"""Prediction Configuration Module"""
from __future__ import annotations

from dataclasses import dataclass

from src.utils.config.base import BaseConfig


@dataclass
class PredictionConfig(BaseConfig):
    """Configuration for prediction processes."""
    # 移除默认值，强制从配置文件加载 (规则 35)
    batch_size: int
    confidence_level: float
    device: str | None # device 可以是 None，但必须显式配置
    num_workers: int
    return_confidence: bool
    use_cache: bool
    n_samples: int # Number of samples for prediction, e.g., for Monte Carlo dropout or uncertainty estimation
    # Add any other prediction-specific fields from config.yaml if they exist
    # For now, these match the structure observed in the provided config.yaml

    def __post_init__(self):
        """Post-initialization validation."""
        super().__post_init__() # Call BaseConfig's post_init if it has one

        # 检查必需字段 (移除默认值后)
        missing_fields = []
        required_fields = [
            'batch_size', 'confidence_level', 'device', 'num_workers',
            'return_confidence', 'use_cache', 'n_samples'
        ]
        for field_name in required_fields:
            # device 允许为 None，所以不检查 is None
            if (field_name != 'device' and (not hasattr(self, field_name) or getattr(self, field_name) is None)) or (field_name == 'device' and not hasattr(self, field_name)):
                 missing_fields.append(field_name)


        if missing_fields:
            from src.utils.logger import get_logger
            logger = get_logger(__name__)
            error_msg = f"PredictionConfig 缺失必需字段或字段为 None: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(self.batch_size, int) or self.batch_size <= 0:
            raise ValueError(f"prediction.batch_size must be a positive integer, got {self.batch_size}")
        if not (0 < self.confidence_level < 1):
            raise ValueError(f"prediction.confidence_level must be between 0 and 1, got {self.confidence_level}")
        if not isinstance(self.num_workers, int) or self.num_workers < 0:
            raise ValueError(f"prediction.num_workers must be a non-negative integer, got {self.num_workers}")
        if not isinstance(self.n_samples, int) or self.n_samples <= 0:
            raise ValueError(f"prediction.n_samples must be a positive integer, got {self.n_samples}")
