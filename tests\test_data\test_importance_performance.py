"""特征重要性与模型性能关系测试模块

本模块测试基于LightGBM特征重要性选择的特征对下游模型性能的影响，包括：
1. 使用重要特征子集的性能与使用全部特征的性能比较
2. 不同重要性阈值对模型性能的影响
3. 特征重要性选择与随机选择的性能比较
4. 特征重要性选择与其他特征选择方法的性能比较

相关模块:
1. 被测试模块:
   - src/data/preprocessing/feature_selector.py: 特征选择器实现
2. 依赖模块:
   - lightgbm: LightGBM模型库
   - sklearn: 机器学习工具库
"""


import lightgbm as lgb
import numpy as np
import pandas as pd
import pytest
from sklearn.datasets import make_regression
from sklearn.feature_selection import SelectKBest, f_regression
from sklearn.metrics import mean_squared_error
from sklearn.model_selection import train_test_split

from src.data.preprocessing.feature_selector import FeatureSelector


def create_regression_dataset(n_samples=1000, n_features=20, n_informative=5, random_state=42):
    """
    创建回归数据集

    Args:
        n_samples: 样本数量
        n_features: 特征总数
        n_informative: 有信息量的特征数量
        random_state: 随机种子

    Returns:
        X: 特征数据框
        y: 目标变量
    """
    X, y, _ = make_regression(
        n_samples=n_samples,
        n_features=n_features,
        n_informative=n_informative,
        random_state=random_state
    )

    # 创建特征名称
    feature_names = [f'feature_{i}' for i in range(n_features)]

    # 转换为DataFrame
    X_df = pd.DataFrame(X, columns=feature_names)

    # 添加目标列
    df = X_df.copy()
    df['value15'] = y

    return df


@pytest.fixture
def regression_dataset():
    """创建用于测试的回归数据集"""
    return create_regression_dataset(n_samples=1000, n_features=20, n_informative=5)


def test_importance_vs_full_feature_performance(regression_dataset):
    """测试使用重要特征子集的性能与使用全部特征的性能比较"""
    # 1. 准备数据
    df = regression_dataset
    X = df.drop(columns=['value15'])
    y = df['value15']

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 2. 使用LightGBM计算特征重要性
    # DEFAULT_LGBM_PARAMS已被移除，使用默认参数
    model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    model.fit(X_train, y_train)

    # 获取特征重要性
    importance = pd.Series(model.feature_importances_, index=X.columns)
    importance = importance / importance.sum()

    # 3. 选择不同数量的重要特征
    feature_counts = [5, 10, 15, 20]  # 全部特征数量为20
    test_errors = []

    for count in feature_counts:
        # 选择前count个重要特征
        selected_features = importance.sort_values(ascending=False).index[:count].tolist()

        # 使用选定特征训练模型
        subset_model = lgb.LGBMRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            verbose=-1
        )
        subset_model.fit(X_train[selected_features], y_train)

        # 评估模型
        y_pred = subset_model.predict(X_test[selected_features])
        mse = mean_squared_error(y_test, np.asarray(y_pred))
        test_errors.append(mse)

    # 4. 验证结果
    # 验证使用前5个特征的性能是否已经很好
    assert test_errors[0] < test_errors[-1] * 1.5, "使用前5个重要特征的性能应该接近使用全部特征"

    # 验证性能是否随特征数量增加而提高或保持稳定
    for i in range(1, len(test_errors)):
        assert test_errors[i] <= test_errors[i-1] * 1.2, f"使用{feature_counts[i]}个特征的性能应该不会显著差于使用{feature_counts[i-1]}个特征"


def test_importance_threshold_impact(regression_dataset):
    """测试不同重要性阈值对模型性能的影响"""
    # 1. 准备数据
    df = regression_dataset
    X = df.drop(columns=['value15'])
    y = df['value15']

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 2. 使用LightGBM计算特征重要性
    model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    model.fit(X_train, y_train)

    # 获取特征重要性
    importance = pd.Series(model.feature_importances_, index=X.columns)
    importance = importance / importance.sum()

    # 3. 测试不同的重要性阈值
    thresholds = [0.01, 0.03, 0.05, 0.1]
    test_errors = []
    selected_counts = []

    for threshold in thresholds:
        # 选择重要性大于阈值的特征
        selected_features = importance[importance > threshold].index.tolist()
        selected_counts.append(len(selected_features))

        if len(selected_features) == 0:
            # 如果没有特征被选中，使用最重要的特征
            selected_features = [importance.idxmax()]

        # 使用选定特征训练模型
        subset_model = lgb.LGBMRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=6,
            random_state=42,
            verbose=-1
        )
        subset_model.fit(X_train[selected_features], y_train)

        # 评估模型
        y_pred = subset_model.predict(X_test[selected_features])
        mse = mean_squared_error(y_test, np.asarray(y_pred))
        test_errors.append(mse)

    # 4. 验证结果
    # 验证阈值增加时选择的特征数量减少
    for i in range(1, len(selected_counts)):
        assert selected_counts[i] <= selected_counts[i-1], f"阈值{thresholds[i]}应该选择的特征数量不多于阈值{thresholds[i-1]}"

    # 验证性能变化是否合理
    # 注意：性能可能会随着特征减少而略有下降，但不应该显著下降
    for i in range(1, len(test_errors)):
        assert test_errors[i] <= test_errors[0] * 2.0, f"阈值{thresholds[i]}的性能不应该显著差于最低阈值"


def test_importance_vs_random_selection(regression_dataset):
    """测试特征重要性选择与随机选择的性能比较"""
    # 1. 准备数据
    df = regression_dataset
    X = df.drop(columns=['value15'])
    y = df['value15']

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 2. 使用LightGBM计算特征重要性
    model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    model.fit(X_train, y_train)

    # 获取特征重要性
    importance = pd.Series(model.feature_importances_, index=X.columns)
    importance = importance / importance.sum()

    # 3. 选择前5个重要特征
    top_features = importance.sort_values(ascending=False).index[:5].tolist()

    # 4. 随机选择5个特征
    np.random.seed(42)
    random_features = np.random.choice(X.columns, 5, replace=False).tolist()

    # 5. 使用重要特征训练模型
    top_model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    top_model.fit(X_train[top_features], y_train)

    # 6. 使用随机特征训练模型
    random_model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    random_model.fit(X_train[random_features], y_train)

    # 7. 评估两个模型
    top_pred = top_model.predict(X_test[top_features])
    random_pred = random_model.predict(X_test[random_features])

    top_mse = mean_squared_error(y_test, np.asarray(top_pred))
    random_mse = mean_squared_error(y_test, np.asarray(random_pred))

    # 8. 验证结果
    assert top_mse < random_mse, "使用重要特征的模型应该优于使用随机特征的模型"
    assert top_mse / random_mse < 0.8, "使用重要特征的模型性能应该显著优于使用随机特征的模型"


def test_importance_vs_other_selection_methods(regression_dataset):
    """测试特征重要性选择与其他特征选择方法的性能比较"""
    # 1. 准备数据
    df = regression_dataset
    X = df.drop(columns=['value15'])
    y = df['value15']

    # 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

    # 2. 使用LightGBM计算特征重要性
    lgb_model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    lgb_model.fit(X_train, y_train)

    # 获取特征重要性
    importance = pd.Series(lgb_model.feature_importances_, index=X.columns)
    importance = importance / importance.sum()

    # 选择前5个重要特征
    lgb_features = importance.sort_values(ascending=False).index[:5].tolist()

    # 3. 使用F检验选择特征
    f_selector = SelectKBest(f_regression, k=5)
    f_selector.fit(X_train, y_train)
    f_scores = pd.Series(np.asarray(f_selector.scores_), index=X.columns)
    f_features = f_scores.sort_values(ascending=False).index[:5].tolist()

    # 4. 使用LightGBM重要特征训练模型
    lgb_importance_model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    lgb_importance_model.fit(X_train[lgb_features], y_train)

    # 5. 使用F检验选择的特征训练模型
    f_model = lgb.LGBMRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=42,
        verbose=-1
    )
    f_model.fit(X_train[f_features], y_train)

    # 6. 评估两个模型
    lgb_pred = lgb_importance_model.predict(X_test[lgb_features])
    f_pred = f_model.predict(X_test[f_features])

    lgb_mse = mean_squared_error(y_test, np.asarray(lgb_pred))
    f_mse = mean_squared_error(y_test, np.asarray(f_pred))

    # 7. 验证结果
    # 两种方法都应该有不错的性能，但LightGBM可能更好，因为它可以捕捉非线性关系
    assert lgb_mse <= f_mse * 1.2, "LightGBM特征重要性选择的性能应该不显著差于F检验选择"

    # 8. 检查两种方法选择的特征重叠度
    overlap = len(set(lgb_features).intersection(set(f_features)))
    assert overlap > 0, "两种方法选择的特征应该有一定重叠"
