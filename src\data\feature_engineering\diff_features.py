"""差分特征生成器 - 计算时序差分特征

此模块负责计算时序数据的差分特征，包括：
- 一阶差分：计算相邻时间点的差值
- 二阶差分：计算一阶差分的差分
- 高阶差分：根据配置计算更高阶的差分
"""


import torch

from src.data.feature_engineering.base import BaseFeatureGenerator
from src.utils.config.manager import ConfigManager
from src.utils.logger import get_logger


class DiffFeatureGenerator(BaseFeatureGenerator):
    """差分特征生成器

    计算时序数据的差分特征
    """

    def __init__(self, config: ConfigManager):
        """初始化差分特征生成器

        Args:
            config: 配置管理器实例
        """
        if not isinstance(config, ConfigManager):
            raise TypeError("config参数必须为ConfigManager实例")

        self.config = config
        self.logger = get_logger(__name__)
        self.feature_names: list[str] = []
        self._feature_count = 0

        # 添加类型注解的类属性
        self._enabled: bool = False
        self._orders: list[int]  # 必须在_check_config中初始化

        # 初始化配置
        self._check_config()

    def _check_config(self) -> None:
        """检查全局配置，仅设置启用状态"""
        self._enabled = False # 默认禁用
        try:
            if hasattr(self.config, 'feature_engineering'):
                feature_eng = self.config.feature_engineering
                if hasattr(feature_eng, 'enable') and feature_eng.enable:
                    # 全局特征工程已启用，此生成器可能被层级配置调用
                    self._enabled = True
                    self.logger.info("差分特征生成器已启用（可能由层级配置驱动）。")
                else:
                    self.logger.info("特征工程全局开关已关闭，差分特征生成器将禁用。")
            else:
                self.logger.warning("配置中缺少 feature_engineering 配置项，差分特征生成器将禁用。")
        except AttributeError as e:
             # 配置项缺失是预期的，因为ConfigLoader会处理，这里只记录信息
             self.logger.info(f"访问配置项时出错 (可能缺失): {e}。差分特征生成器默认禁用，除非被层级配置启用。")
             self._enabled = False # 明确禁用，除非层级配置覆盖
        except Exception as e:
             # 其他意外错误应明确抛出
             error_msg = f"检查全局差分特征配置时发生意外错误: {e}"
             self.logger.error(error_msg)
             raise ValueError(error_msg) from e

    def generate(self, data: torch.Tensor, **kwargs) -> torch.Tensor:
        """生成差分特征

        Args:
            data: 输入数据张量 [n_samples, n_features]
            **kwargs: 其他参数，必须包含 'orders' 列表

        Returns:
            torch.Tensor: 生成的差分特征张量 [n_samples, n_features * len(orders)]

        Raises:
            ValueError: 如果未提供有效的 'orders' 参数或计算失败
        """
        # Roo-Fix: 强制要求通过 kwargs 提供 orders
        if 'orders' not in kwargs:
            error_msg = "调用 generate 时必须通过 kwargs 提供 'orders' 参数。"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        orders_to_use = kwargs['orders']
        source = "kwargs" # 明确来源

        # 检查生成器是否启用 (基于全局配置)
        if not self.is_enabled:
            self.logger.info("差分特征生成器(全局)已禁用，跳过。")
            return torch.empty((data.shape[0], 0), dtype=data.dtype, device=data.device)

        # 验证要使用的 orders
        if not isinstance(orders_to_use, list) or not orders_to_use:
             error_msg = f"无效的差分阶数列表 (来源: {source}): {orders_to_use}"
             self.logger.error(error_msg)
             raise ValueError(error_msg)
        valid_orders = []
        for order in orders_to_use:
            if not isinstance(order, int) or order <= 0:
                error_msg = f"无效的差分阶数 (来源: {source}): {order}，必须为正整数"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            valid_orders.append(order)
        orders_to_use = valid_orders # 使用验证过的列表

        if not orders_to_use:
             error_msg = f"验证后差分阶数列表为空 (来源: {source})"
             self.logger.error(error_msg)
             raise ValueError(error_msg)


        self.logger.info(f"开始差分特征计算 (使用 {source} 的阶数: {orders_to_use})")
        self.logger.debug(
            f"DiffFeatureGenerator - 输入数据统计:\n"
            f"- 形状: {data.shape}\n"
            f"- 数据类型: {data.dtype}\n"
            f"- 设备: {data.device}\n"
            f"- 均值(每列): {data.mean(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 标准差(每列): {data.std(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最小值(每列): {data.min(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最大值(每列): {data.max(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 是否包含NaN: {torch.isnan(data).any().item()}\n"
            f"- 是否包含Inf: {torch.isinf(data).any().item()}"
        )

        try:
            # 检查输入数据是否有 Inf 值
            if torch.isinf(data).any():
                error_msg = "输入数据包含无穷大值，无法进行差分计算。"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 计算各阶差分
            diff_results = []
            current_feature_names = [] # 使用局部变量存储本次生成的名称

            for order in orders_to_use:
                current_diff = data
                # 检查样本数是否足够进行差分
                if data.shape[0] <= order:
                    error_msg = f"样本数 ({data.shape[0]}) 不足以计算 {order} 阶差分。"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                for k in range(order):
                    # 第一次差分前的数据
                    prev_data = current_diff
                    self.logger.debug(f"Order {order}, Iteration {k+1}: Before torch.diff, current_diff shape: {current_diff.shape}")
                    current_diff = torch.diff(current_diff, dim=0)
                    self.logger.debug(f"Order {order}, Iteration {k+1}: After torch.diff, current_diff shape: {current_diff.shape}")
                    # 检查差分后的形状是否正确
                    expected_rows = prev_data.shape[0] - 1
                    if current_diff.shape[0] != expected_rows:
                         error_msg = f"第 {k+1}/{order} 次差分形状异常: 期望 {expected_rows} 行, 得到 {current_diff.shape[0]} 行"
                         self.logger.error(error_msg)
                         raise ValueError(error_msg)


                # 使用 NaN 填充以保持原始样本数，而不是补零
                nan_padding = torch.full((order, data.shape[1]), float('nan'), dtype=data.dtype, device=data.device)
                padded_diff = torch.cat([nan_padding, current_diff], dim=0)

                # 检查填充后的形状
                if padded_diff.shape[0] != data.shape[0]:
                     error_msg = f"{order}阶差分填充后形状异常: 期望 {data.shape[0]} 行, 得到 {padded_diff.shape[0]} 行"
                     self.logger.error(error_msg)
                     raise ValueError(error_msg)


                diff_results.append(padded_diff)

                # 更新特征名称
                for i in range(data.shape[1]):
                    current_feature_names.append(f"diff{order}_feature{i}")

                self.logger.debug(f"{order}阶差分计算完成 | 形状: {padded_diff.shape}")

            if not diff_results:
                 # 如果 orders_to_use 不为空，但 diff_results 为空，说明所有阶数都因错误（如样本不足）而失败
                 error_msg = "未能成功计算任何请求的差分阶数。"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)


            # 合并所有阶数的差分特征
            diff_features = torch.cat(diff_results, dim=1)
            self._feature_count = diff_features.shape[1]
            self.feature_names = current_feature_names # 更新实例变量

            self.logger.info(f"差分特征生成完成 | 阶数: {orders_to_use} | 总形状: {diff_features.shape}")

            # 验证差分特征有效性 (NaN 是预期的，因为有填充)
            # if torch.isnan(diff_features).any() or torch.isinf(diff_features).any():
            #     # 检查非填充部分是否有问题
            #     non_nan_inf_mask = ~torch.isnan(diff_features) & ~torch.isinf(diff_features)
            #     # 如果所有值都是 NaN/inf (理论上不应发生，除非输入有问题)，或者非 NaN/inf 部分仍有问题
            #     # 这里简化检查：只检查无穷大值，因为NaN是填充引入的
            if torch.isinf(diff_features).any():
                error_msg = "生成的差分特征中包含无穷大值。"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            return diff_features

        except Exception as e:
            error_msg = f"计算时序差分特征失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

    def get_feature_names(self) -> list[str]:
        """获取生成的特征名称列表

        Returns:
            List[str]: 特征名称列表
        """
        return self.feature_names

    @property
    def feature_count(self) -> int:
        """获取生成的特征数量

        Returns:
            int: 特征数量
        """
        return self._feature_count

    @property
    def is_enabled(self) -> bool:
        """检查特征生成器是否启用

        Returns:
            bool: 是否启用
        """
        return self._enabled
