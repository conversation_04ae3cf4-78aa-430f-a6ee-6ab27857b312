# 设置输出编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8

# 直接获取所有带有注释的提交哈希
$notedCommits = git notes list | ForEach-Object {
    $parts = $_ -split " "
    $parts[1]  # 提取提交哈希
}

# 获取这些提交的详细信息
$commits = $notedCommits | ForEach-Object {
    $hash = $_
    $date = git log -1 --pretty=format:"%ai" $hash
    
    [PSCustomObject]@{
        Hash = $hash
        Date = $date
    }
}

# 按日期排序（最新的在前）
$sortedCommits = $commits | Sort-Object -Property Date -Descending

# 遍历排序后的提交
foreach ($commit in $sortedCommits) {
    # 获取短哈希（前8位）
    $shortHash = $commit.Hash.Substring(0, 8)
    
    # 获取提交信息（仅标题）
    $commitMsg = git log -1 --pretty=format:"%s" $commit.Hash
    
    # 获取注释内容
    $noteContent = git notes show $commit.Hash
    
    # 输出信息
    Write-Host "`n[$($commit.Date)] [$shortHash] $commitMsg" -ForegroundColor Yellow
    Write-Host "-------------------------------------------------------------" -ForegroundColor Gray
    Write-Host $noteContent
}
