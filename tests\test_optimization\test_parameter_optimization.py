"""
测试参数优化模块
"""

from dataclasses import dataclass, field
from typing import Any
from unittest.mock import MagicMock, PropertyMock, patch

import optuna
import pytest
import torch

# Mock logger early to prevent issues during import
with patch('src.utils.logger.LoggerFactory') as mock_logger_factory:
    mock_logger_factory.return_value = MagicMock()

# Import necessary components AFTER mocking logger
import contextlib

from src.optimization.exceptions import ConfigurationError, MetricError
from src.optimization.hyperparameter_optimizer import objective, run_optimization
from src.optimization.metric_extraction import extract_metric

# Import actual config types for comparison/structure reference if needed

# --- Metric Extraction Tests ---

def test_extract_metric_with_empty_history():
    with pytest.raises(MetricError, match="训练历史为空"):
        extract_metric({})

def test_extract_metric_with_invalid_type():
    with pytest.raises(MetricError, match="VAL_MAE类型错误"):
        extract_metric({"val_mae": "invalid"})

def test_extract_metric_with_negative_value():
    with pytest.raises(Metric<PERSON>rror, match="VAL_MAE为负值"):
        extract_metric({"val_mae": -1.0})

def test_extract_metric_with_valid_list():
    history = [{"val_mae": 2.0}, {"val_mae": 1.5}, {"val_mae": 1.0}]
    assert extract_metric(history) == 1.0

def test_extract_metric_with_tensor():
    assert extract_metric({"val_mae": torch.tensor(1.5)}) == 1.5

def test_extract_metric_with_nan():
     with pytest.raises(MetricError, match="VAL_MAE不是有限数值"):
        extract_metric({"val_mae": float('nan')})

def test_extract_metric_with_inf():
     with pytest.raises(MetricError, match="VAL_MAE不是有限数值"):
        extract_metric({"val_mae": float('inf')})

# --- Mocking Fixtures ---

@pytest.fixture
def mock_cuda_manager_fixture():
    """Fixture to mock cuda_manager and provide a device."""
    mock_manager = MagicMock()
    type(mock_manager).device = PropertyMock(return_value="cpu")
    mock_manager.is_cuda_available = False
    mock_manager.get_memory_stats.return_value = {
        'allocated_mb': 0, 'reserved_mb': 0, 'max_allocated_mb': 0
    }
    mock_manager.move_to_device = lambda x: x
    # Patch where cuda_manager is imported and used (likely BaseModel)
    # Remove the incorrect patch target for hyperparameter_optimizer
    with patch('src.models.base.base_model.cuda_manager') as mock_manager_patch:
        mock_manager_patch.return_value = mock_manager
        yield mock_manager

# --- Mock Config Classes (Reflecting actual structure) ---
@dataclass
class MockLrBalancerConfig:
    enabled: bool = True
    target_ratio: float = 1.0
    min_lr: float = 1e-6
    max_lr: float = 1e-3

@dataclass
class MockOptimizerConfig:
    type: str = "adam"
    generator_lr: float = 1e-4
    discriminator_lr: float = 1e-4
    weight_decay: float = 1e-5
    beta1: float = 0.9
    beta2: float = 0.999
    momentum: float = 0.0
    nesterov: bool = False
    eps: float = 1e-8

@dataclass
class MockBalanceConfig:
    lower_threshold: float = 0.5
    upper_threshold: float = 2.0
    min_n_critic: int = 1
    max_n_critic: int = 5
    min_g_steps: int = 1
    max_g_steps: int = 1

@dataclass
class MockTrainingConfig:
    num_epochs: int = 50
    batch_size: int = 128
    lambda_gp: float = 10.0
    n_critic: int = 5
    dropout_rate: float = 0.3

    seed: int = 42
    save_dir: str = "outputs/mock_models"
    mixed_precision: dict | None = field(default_factory=lambda: {"enabled": False})
    optimizer: MockOptimizerConfig = field(default_factory=MockOptimizerConfig)
    scheduler_g: dict | None = None
    scheduler_d: dict | None = None
    lr_scheduler: dict | None = None
    early_stopping: dict | None = None
    checkpoint: dict | None = None
    batch_size_optimizer: dict | None = None
    lr_balancer: MockLrBalancerConfig = field(default_factory=MockLrBalancerConfig)
    balance: MockBalanceConfig = field(default_factory=MockBalanceConfig)
    dynamic_batch_size: bool = False

@dataclass
class MockModelConfig:
    type: str = "gan"
    hidden_dim: int = 128
    noise_dim: int = 64
    dropout_rate: float = 0.3
    attention: dict | None = None
    adaptive_attention: dict | None = None
    loss: dict | None = None

@dataclass
class MockDataConfig:
    window_size: int = 24
    batch_size: int = 128
    stride: int = 8
    feature_dim: int = 20
    target_col: str = "target"

@dataclass
class MockLoggingConfig:
    level: str = "INFO"

@dataclass
class MockPathsConfig:
    raw_data: str = "mock/path/data.csv"
    processed_data: str = "mock/path/processed"
    log_dir: str = "mock/path/logs"
    output_dir: str = "mock/path/output"

@dataclass
class MockSystemConfig:
    device: str = "cpu"
    cuda: dict | None = None
    memory: dict | None = None
    cache: dict | None = None

# --- Mock ConfigManager ---
class MockFullConfigManager:
    """Mocks the full structure expected by ConfigManager.from_yaml"""
    def __init__(self):
        self.version: str = "mock_v1"
        self.paths: MockPathsConfig = MockPathsConfig()
        self.system: MockSystemConfig = MockSystemConfig()
        self.logging: MockLoggingConfig = MockLoggingConfig()
        self.data: MockDataConfig = MockDataConfig()
        self.training: MockTrainingConfig = MockTrainingConfig()
        self.model: MockModelConfig = MockModelConfig()
        self.feature_engineering: dict = {}
        self.evaluation: dict = {}
        self.prediction: dict = {"batch_size": 32}

    # No need for from_yaml here, we patch ConfigLoader instead

# --- Mock PipelineRunner ---
class MockPipelineRunner:
    """Mocks PipelineRunner, ensuring it has a compatible config"""
    def __init__(self, config_path=None, **kwargs):
        self.config = MockFullConfigManager()
        self._cleanup_called = False
        self._train_side_effect = None
        self._train_return_value = {"history": {"val_mae": [1.5]}}

    def set_train_side_effect(self, side_effect):
        self._train_side_effect = side_effect

    def set_train_return_value(self, return_value):
        self._train_return_value = return_value

    def train(self, resume=False, checkpoint_path=None):
        print("MockPipelineRunner.train called")
        if self._train_side_effect:
            print(f"MockPipelineRunner raising side effect: {self._train_side_effect}")
            raise self._train_side_effect
        print(f"MockPipelineRunner returning: {self._train_return_value}")
        return self._train_return_value

    def cleanup(self):
        print("MockPipelineRunner.cleanup called")
        self._cleanup_called = True

    @property
    def cleanup_called(self):
        return self._cleanup_called

@pytest.fixture
def mock_pipeline_runner_fixture():
    """Fixture to patch main.PipelineRunner"""
    runner = MockPipelineRunner()
    with patch('src.optimization.hyperparameter_optimizer.PipelineRunner', return_value=runner):
        yield runner

# --- Mock Trial ---
class MockTrial:
    """Mock Optuna trial for testing"""
    def __init__(self, number=0):
        self.number = number
        self.params = {}
        self._suggested_params = {}

    def suggest_float(self, name: str, low: float, high: float, log: bool = False) -> float:
        val = (low + high) / 2
        self.params[name] = val
        self._suggested_params[name] = val
        return val

    def suggest_categorical(self, name: str, choices: list[Any]) -> Any: # Changed type hint from list to List[Any]
        val = choices[0]
        self.params[name] = val
        self._suggested_params[name] = val
        return val

    def suggest_int(self, name: str, low: int, high: int) -> int:
        val = (low + high) // 2
        self.params[name] = val
        self._suggested_params[name] = val
        return val

@pytest.fixture
def mock_trial_fixture():
    """Fixture to create a mock Optuna trial"""
    return MockTrial()

# --- Objective Function Tests ---

@patch('torch.cuda.empty_cache')
@patch('src.utils.config.loader.ConfigLoader.load_from_yaml', return_value=MockFullConfigManager())
@patch('src.optimization.hyperparameter_optimizer._verify_parameter_consistency')  # 添加对参数一致性检查的模拟
def test_objective_config_error_prunes(mock_verify_consistency, mock_load_yaml, mock_empty_cache, mock_cuda_manager_fixture, mock_pipeline_runner_fixture, mock_trial_fixture):
    """Test objective prunes trial on ConfigurationError"""
    mock_pipeline_runner_fixture.set_train_side_effect(ConfigurationError("Simulated config error"))

    with pytest.raises(optuna.exceptions.TrialPruned):
        objective(mock_trial_fixture, base_config_path="dummy.yaml")

    assert mock_pipeline_runner_fixture.cleanup_called
    mock_empty_cache.assert_called_once()


@patch('torch.cuda.empty_cache')
@patch('src.utils.config.loader.ConfigLoader.load_from_yaml', return_value=MockFullConfigManager())
@patch('src.optimization.hyperparameter_optimizer._verify_parameter_consistency')  # 添加对参数一致性检查的模拟
def test_objective_metric_error_prunes(mock_verify_consistency, mock_load_yaml, mock_empty_cache, mock_cuda_manager_fixture, mock_pipeline_runner_fixture, mock_trial_fixture):
    """Test objective prunes trial on MetricError"""
    mock_pipeline_runner_fixture.set_train_return_value({"history": {"val_mae": "not a number"}})

    # Call objective directly and assert the exception type
    try:
        objective(mock_trial_fixture, base_config_path="dummy.yaml")
        pytest.fail("Expected optuna.exceptions.TrialPruned but no exception was raised.")
    except optuna.exceptions.TrialPruned:
        # This is the expected outcome
        pass
    except Exception as e:
        pytest.fail(f"Expected optuna.exceptions.TrialPruned but got {type(e).__name__}: {e}")

    assert mock_pipeline_runner_fixture.cleanup_called
    mock_empty_cache.assert_called_once()


@patch('torch.cuda.empty_cache')
@patch('src.utils.config.loader.ConfigLoader.load_from_yaml', return_value=MockFullConfigManager())
@patch('src.optimization.hyperparameter_optimizer._verify_parameter_consistency')  # 添加对参数一致性检查的模拟
def test_objective_other_exception_raises(mock_verify_consistency, mock_load_yaml, mock_empty_cache, mock_cuda_manager_fixture, mock_pipeline_runner_fixture, mock_trial_fixture):
    """Test objective re-raises other exceptions"""
    mock_pipeline_runner_fixture.set_train_side_effect(ValueError("Some other error"))

    # 由于我们现在将所有异常都转换为TrialPruned，所以我们期望TrialPruned而不是ValueError
    with pytest.raises(optuna.exceptions.TrialPruned):
        objective(mock_trial_fixture, base_config_path="dummy.yaml")

    assert mock_pipeline_runner_fixture.cleanup_called
    mock_empty_cache.assert_called_once()


@patch('torch.cuda.empty_cache')
@patch('src.utils.config.loader.ConfigLoader.load_from_yaml', return_value=MockFullConfigManager())
@patch('src.optimization.hyperparameter_optimizer._verify_parameter_consistency')  # 添加对参数一致性检查的模拟
def test_objective_always_uses_fast_mode(mock_verify_consistency, mock_load_yaml, mock_empty_cache, mock_cuda_manager_fixture, mock_pipeline_runner_fixture, mock_trial_fixture):
    """测试objective始终应用快速模式设置"""
    objective(mock_trial_fixture, base_config_path="dummy.yaml")

    runner_config = mock_pipeline_runner_fixture.config
    # 验证快速模式设置已应用
    assert runner_config.training.num_epochs < 50
    assert runner_config.data.batch_size < 128
    assert runner_config.logging.level == "WARNING"

    # 验证参数一致性检查被调用
    mock_verify_consistency.assert_called_once()

    # 验证资源清理
    assert mock_pipeline_runner_fixture.cleanup_called
    mock_empty_cache.assert_called_once()


# --- run_optimization Tests ---

# Patch objective *before* calling run_optimization
@patch('src.optimization.hyperparameter_optimizer.objective', return_value=0.6)
def test_run_optimization_creates_study_and_optimizes(mock_objective):
    """测试优化运行创建study并调用optimize"""
    mock_trial_1 = MagicMock(spec=optuna.trial.FrozenTrial)
    mock_trial_1.state = optuna.trial.TrialState.COMPLETE
    mock_trial_1.value = 0.5
    mock_trial_1.number = 1
    mock_trial_1.params = {"param1": 0.1}

    mock_trial_2 = MagicMock(spec=optuna.trial.FrozenTrial)
    mock_trial_2.state = optuna.trial.TrialState.COMPLETE
    mock_trial_2.value = 0.8
    mock_trial_2.number = 2
    mock_trial_2.params = {"param1": 0.2}

    mock_study = MagicMock(spec=optuna.study.Study)
    mock_study.trials = [mock_trial_1, mock_trial_2]

    # Simulate the behavior of optimize calling the objective
    def mock_optimize(objective_func, n_trials, **kwargs):
        for i in range(n_trials):
            # Create a new mock trial for each call simulation
            trial = MockTrial(number=i)
            with contextlib.suppress(optuna.exceptions.TrialPruned):
                objective_func(trial) # Ignore pruned trials for call count

    mock_study.optimize = mock_optimize # Assign the mock implementation

    with patch('optuna.create_study', return_value=mock_study) as mock_create_study:
        run_optimization(n_trials=5, top_n=1) # Run 5 trials

    mock_create_study.assert_called_once_with(
        study_name="gan_hyperopt",
        storage="sqlite:///optuna_study.db",
        direction='minimize',
        load_if_exists=True
    )
    # Check that the *mocked* objective was called n_trials times via our mock_optimize
    assert mock_objective.call_count == 5


def test_run_optimization_handles_optimization_error():
    """测试优化运行处理optimize内部错误"""
    mock_study = MagicMock(spec=optuna.study.Study)
    # Make optimize raise an error when called
    mock_study.optimize.side_effect = Exception("Optimization failed")

    with patch('optuna.create_study', return_value=mock_study), \
         pytest.raises(Exception, match="Optimization failed"):
        run_optimization(n_trials=1)


@patch('src.optimization.hyperparameter_optimizer.objective', return_value=None) # Mock objective
def test_run_optimization_no_valid_trials(mock_objective):
    """测试优化运行在没有有效试验时的情况"""
    mock_trial_fail = MagicMock(spec=optuna.trial.FrozenTrial)
    mock_trial_fail.state = optuna.trial.TrialState.FAIL
    mock_trial_fail.value = None

    mock_study = MagicMock(spec=optuna.study.Study)
    mock_study.trials = [mock_trial_fail]

    # Simulate optimize calling the objective
    def mock_optimize(objective_func, n_trials, **kwargs):
        for i in range(n_trials):
            trial = MockTrial(number=i) # Use our mock trial
            with contextlib.suppress(optuna.exceptions.TrialPruned):
                objective_func(trial)

    mock_study.optimize = mock_optimize

    with patch('optuna.create_study', return_value=mock_study):
        # Should run without error, but log a warning
        run_optimization(n_trials=1)
        # Assert objective was called once via our mock_optimize
        assert mock_objective.call_count == 1


if __name__ == '__main__':
    pytest.main(['-v', __file__])
