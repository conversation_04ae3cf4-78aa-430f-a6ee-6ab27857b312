"""路径配置模块 - 管理系统中的所有路径"""

from __future__ import annotations

from dataclasses import dataclass, field
from pathlib import Path

from src.utils.logger import get_logger


# 移除 BaseConfig 继承
# @dataclass
# class PathsConfig(BaseConfig):
@dataclass
class PathsConfig:
    """路径配置 (不再继承 BaseConfig)

    设计说明：
    1. 支持绝对路径和相对路径
    2. 相对路径基于配置文件所在目录
    3. 自动转换为绝对路径
    4. 统一使用Path对象
    """
    # 移除所有默认值和 default_factory，变为必需字段
    # 将类型提示改回 Path，__post_init__ 会处理从 str 的转换
    data_dir: Path
    checkpoint_dir: Path
    logs_dir: Path
    raw_data: Path
    results_dir: Path
    model_dir: Path
    # log_dir: Path # 移除 log_dir 字段定义
    model_files: dict[str, str]
    # 移除 noise_dim 和 dimensions

    # 将带有默认值的 _base_dir 移到最后
    _base_dir: Path | None = field(default=None, init=True) # 通过构造函数传入

    def __post_init__(self):
    # def __post_init__(self, base_dir_override: Optional[Union[str, Path]] = None): # 移除 base_dir_override 参数
        """初始化路径配置，检查必需字段，解析路径"""
        logger = get_logger(__name__)
        if hasattr(self, '_initialized') and self._initialized:
            logger.debug("PathsConfig 已初始化，跳过 __post_init__")
            return
        self._initialized = True
        logger.debug("开始初始化 PathsConfig...")

        # 检查必需字段
        missing_fields = []
        required_fields = [
            'data_dir', 'checkpoint_dir', 'logs_dir', 'raw_data', # 移除 'log_dir'
            'results_dir', 'model_dir', 'model_files'
        ]
        for field_name in required_fields:
            if not hasattr(self, field_name):
                 missing_fields.append(field_name)

        if missing_fields:
            error_msg = f"PathsConfig 初始化时缺少必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 验证 model_files 类型
        if not isinstance(self.model_files, dict):
             error_msg = f"'model_files' 必须是字典类型，但得到 {type(self.model_files)}"
             logger.error(error_msg)
             raise TypeError(error_msg)

        try:
            # 设置基准目录
            # 检查 _base_dir 是否已通过构造函数传入
            if self._base_dir is None:
                 # 如果没有覆盖且 _base_dir 未设置，则报错，不再依赖 __file__
                 error_msg = "PathsConfig 初始化时必须通过构造函数提供有效的 _base_dir"
                 logger.error(error_msg)
                 raise ValueError(error_msg)
            else:
                 # 如果 _base_dir 已通过其他方式设置 (例如 loader)，则使用它
                 self._base_dir = Path(self._base_dir).resolve()
                 logger.debug(f"使用预设的基准目录: {self._base_dir}")

            # 转换所有路径字段为 Path 对象并解析
            path_fields = ['data_dir', 'checkpoint_dir', 'logs_dir', 'raw_data', # 移除 'log_dir'
                           'results_dir', 'model_dir']
            for key in path_fields:
                 value = getattr(self, key)
                 if not isinstance(value, str | Path):
                      raise TypeError(f"路径字段 '{key}' 必须是字符串或 Path 对象, 得到 {type(value)}")

                 path = Path(str(value)) # 确保是 Path 对象

                 # 如果是相对路径，则基于 base_dir 解析
                 if not path.is_absolute():
                     resolved_path = (self._base_dir / path).resolve()
                     logger.debug(f"解析相对路径 '{key}': '{value}' -> '{resolved_path}' (基准: {self._base_dir})")
                     path = resolved_path
                 else:
                      logger.debug(f"路径 '{key}' 是绝对路径: '{path}'")

                 # 更新字段值为解析后的 Path 对象
                 setattr(self, key, path)

            logger.debug("PathsConfig 初始化完成。")

        except Exception as e:
            logger.error(f"路径配置初始化失败: {e!s}", exc_info=True)
            # 统一使用 ValueError
            raise ValueError(f"路径配置初始化失败: {e!s}") from e

    def setup_directories(self):
        """创建必要的目录结构（简化版）"""
        # 获取所有需要创建的目录
        directories = [
            self.data_dir,
            self.checkpoint_dir,
            self.logs_dir,
            self.results_dir,
            self.model_dir
        ]

        # 创建目录
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

        return len(directories)

    def set_base_dir(self, base_dir: str | Path) -> None:
        """设置基准目录并重新解析所有路径

        Args:
            base_dir: 新的基准目录
        """
        try:
            self._base_dir = Path(base_dir).resolve()
            self.__post_init__()  # 重新解析所有路径
        except Exception as e:
            # 包含原始异常信息，以便更好地定位问题根源 (规则 36)
            raise ValueError(f"设置基准目录失败: {e!s}") from e

    def get_checkpoint_path(self, epoch: int, timestamp: str) -> Path:
        """获取检查点保存路径"""
        return self.checkpoint_dir / f"checkpoint_epoch_{epoch}_{timestamp}.pt"

    def get_model_path(self, model_type: str) -> Path:
        """获取模型文件路径

        Args:
            model_type: 模型类型，如"generator"或"discriminator"

        Returns:
            Path: 模型文件的完整路径
        """
        if model_type not in self.model_files:
            raise ValueError(f"未知的模型类型: {model_type}")
        return self.model_dir / self.model_files[model_type]
