"""注意力组件模块 - 提供各种注意力机制的实现

本模块实现了各种注意力机制，包括：
1. 特征注意力
2. 时间注意力
3. 多头注意力
4. 自注意力
5. 交叉注意力
"""

import math

import torch
import torch.nn.functional as f
from torch import nn

from src.models.base.base_module import BaseModule

# FeatureAttention class removed as it's unused and redundant

# TemporalAttention class (single-head custom implementation) removed as it's unused and redundant
class MultiHeadAttention(BaseModule):
    """多头注意力 - 在不同子空间中关注不同的信息模式"""

    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        dropout: float
    ):
        """初始化多头注意力

        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            dropout: Dropout比率
        """
        super().__init__("MultiHeadAttention")

        # 确保嵌入维度可以被头数整除
        assert embed_dim % num_heads == 0, f"嵌入维度({embed_dim})必须能被头数({num_heads})整除"

        # 设置参数
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        # 查询、键、值投影
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)

        # 输出投影
        self.output_proj = nn.Linear(embed_dim, embed_dim)

        # 缩放因子
        self.scale = 1.0 / math.sqrt(self.head_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        mask: torch.Tensor | None = None
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """前向传播

        Args:
            query: 查询 [batch_size, query_length, embed_dim]
            key: 键 [batch_size, key_length, embed_dim]
            value: 值 [batch_size, key_length, embed_dim]
            mask: 注意力掩码 [batch_size, query_length, key_length]

        Returns:
            Tuple[torch.Tensor, torch.Tensor]:
                输出 [batch_size, query_length, embed_dim]
                注意力权重 [batch_size, num_heads, query_length, key_length]
        """
        batch_size = query.size(0)

        # 投影查询、键、值
        q = self.q_proj(query)
        k = self.k_proj(key)
        v = self.v_proj(value)

        # 重塑为多头形式
        q = q.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)


        # 计算注意力分数
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale

        # 应用掩码
        if mask is not None:
            scores = scores.masked_fill(mask.unsqueeze(1) == 0, -1e9)

        # 计算注意力权重 (添加值裁剪以提高数值稳定性)
        # 提高 Softmax 数值稳定性：减去最大值
        scores_stable = scores - torch.max(scores, dim=-1, keepdim=True)[0]
        # 钳位操作可以在减去最大值后进行，进一步限制范围，但可能不是必须的
        # scores_clamped = torch.clamp(scores_stable, min=-30.0, max=30.0)

        # 计算注意力权重
        attention_weights = f.softmax(scores_stable, dim=-1)


        # 应用Dropout
        attention_weights = self.dropout(attention_weights)

        # 计算加权和
        context = torch.matmul(attention_weights, v)

        # 重塑回原始形状
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.embed_dim)

        # 输出投影
        output = self.output_proj(context)

        return output, attention_weights
