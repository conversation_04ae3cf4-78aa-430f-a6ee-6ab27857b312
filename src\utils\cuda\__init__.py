"""CUDA 管理工具包

此包提供了 CUDA 资源管理的完整工具集，包括：
- 设备管理
- 内存管理
- 流管理
- 事件管理
- 资源监控
"""

from .device import cuda_device_manager
from .manager import cuda_manager
from .memory import cuda_memory_manager
from .stream import cuda_stream_manager
from .types import CUDAInitError, GPUMemoryInfo, StreamEventInfo, StreamInfo

# 导出主要接口
__all__ = [
    # 错误类型
    'CUDAInitError',
    # 数据类型
    'GPUMemoryInfo',
    'StreamEventInfo',
    'StreamInfo',
    # 子管理器
    'cuda_device_manager',
    # 主管理器
    'cuda_manager',
    'cuda_memory_manager',
    'cuda_stream_manager',
]

# 版本信息
__version__ = '1.0.0'
