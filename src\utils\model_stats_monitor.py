"""
模型统计监控工具

提供对模型权重、梯度和激活值的统计监控功能。
"""
import logging
import os
from collections import defaultdict

import torch
from torch import nn

# 获取日志记录器
logger = logging.getLogger("ModelStatsMonitor")

class ModelStatsMonitor:
    """模型统计监控工具，用于记录模型权重、梯度和激活值的统计信息"""

    def __init__(
        self,
        log_frequency: int = 10,
        detailed_log_frequency: int = 100,
        log_path: str | None = None,
        track_weights: bool = True,
        track_gradients: bool = True,
        track_activations: bool = False,
        nan_detection: bool = True
    ):
        """
        初始化模型统计监控工具

        Args:
            log_frequency: 基本日志记录频率（每N个批次记录一次）
            detailed_log_frequency: 详细日志记录频率（每N个批次记录一次详细信息）
            log_path: 日志文件路径（如果为None，则只记录到控制台）
            track_weights: 是否跟踪权重统计信息
            track_gradients: 是否跟踪梯度统计信息
            track_activations: 是否跟踪激活值统计信息（需要注册钩子）
            nan_detection: 是否启用NaN检测
        """
        self.log_frequency = log_frequency
        self.detailed_log_frequency = detailed_log_frequency
        self.log_path = log_path
        self.track_weights = track_weights
        self.track_gradients = track_gradients
        self.track_activations = track_activations
        self.nan_detection = nan_detection

        # 统计信息存储
        self.stats_history = defaultdict(list)
        self.hooks = []
        self.step_counter = 0
        self.activation_values = {}

        # 设置日志文件
        if log_path:
            os.makedirs(os.path.dirname(log_path), exist_ok=True)
            file_handler = logging.FileHandler(log_path, mode='w')  # 使用覆写模式
            file_handler.setFormatter(logging.Formatter('%(asctime)s [%(levelname)s] %(name)s: %(message)s'))
            logger.addHandler(file_handler)

        logger.info(f"模型统计监控初始化完成，日志频率: {log_frequency}，详细日志频率: {detailed_log_frequency}")

    def register_model(self, model: nn.Module) -> None:
        """
        注册模型，用于跟踪统计信息

        Args:
            model: PyTorch模型
        """
        if self.track_activations:
            self._register_activation_hooks(model)

        logger.info(f"已注册模型: {type(model).__name__}")

    def _register_activation_hooks(self, model: nn.Module) -> None:
        """
        注册激活值钩子

        Args:
            model: PyTorch模型
        """
        def hook_fn(name):
            def fn(module, input, output):
                # 使用参数以避免未使用警告
                _ = module
                _ = input
                self.activation_values[name] = output.detach()
            return fn

        # 为所有子模块注册钩子
        for name, module in model.named_modules():
            if isinstance(module, nn.ReLU | nn.LeakyReLU | nn.Sigmoid | nn.Tanh | nn.Linear | nn.Conv1d | nn.Conv2d):
                hook = module.register_forward_hook(hook_fn(name))
                self.hooks.append(hook)

        logger.info(f"已注册 {len(self.hooks)} 个激活值钩子")

    def remove_hooks(self) -> None:
        """移除所有注册的钩子"""
        for hook in self.hooks:
            hook.remove()
        self.hooks = []
        logger.info("已移除所有钩子")

    def check_nan_inf(self, model: nn.Module) -> tuple[bool, list[str]]:
        """
        检查模型参数和梯度中是否存在NaN或Inf值

        Args:
            model: PyTorch模型

        Returns:
            Tuple[bool, List[str]]: (是否有效, 问题参数列表)
        """
        if not self.nan_detection:
            return True, []

        invalid_params = []

        # 检查参数
        for name, param in model.named_parameters():
            if param.data is not None and (torch.isnan(param.data).any() or torch.isinf(param.data).any()):
                invalid_params.append(f"{name} (权重)")

            # 检查梯度
            if param.grad is not None and (torch.isnan(param.grad).any() or torch.isinf(param.grad).any()):
                invalid_params.append(f"{name} (梯度)")

        # 检查激活值
        if self.track_activations:
            for name, activation in self.activation_values.items():
                if torch.isnan(activation).any() or torch.isinf(activation).any():
                    invalid_params.append(f"{name} (激活值)")

        return len(invalid_params) == 0, invalid_params

    def log_step(self, model: nn.Module, losses: dict[str, float], step_type: str = "训练") -> bool:
        """
        记录单步统计信息

        Args:
            model: PyTorch模型
            losses: 损失值字典
            step_type: 步骤类型（训练/验证）

        Returns:
            bool: 是否检测到NaN/Inf（True表示正常，False表示检测到NaN/Inf）
        """
        self.step_counter += 1

        # 检查NaN/Inf
        is_valid, invalid_params = self.check_nan_inf(model)
        if not is_valid:
            logger.error(f"检测到NaN/Inf值: {invalid_params}")
            # 记录详细的模型状态
            self._log_detailed_stats(model, losses, step_type, force=True)
            return False

        # 基本日志记录
        if self.step_counter % self.log_frequency == 0:
            self._log_basic_stats(model, losses, step_type)

        # 详细日志记录
        if self.step_counter % self.detailed_log_frequency == 0:
            self._log_detailed_stats(model, losses, step_type)

        return True

    def _log_basic_stats(self, model: nn.Module, losses: dict[str, float], step_type: str) -> None:
        """
        记录基本统计信息

        Args:
            model: PyTorch模型
            losses: 损失值字典
            step_type: 步骤类型
        """
        # 记录损失值
        loss_str = ", ".join([f"{k}: {v:.4f}" for k, v in losses.items()])
        logger.info(f"步骤 {self.step_counter} ({step_type}) - 损失: {loss_str}")

        # 记录梯度范数
        if self.track_gradients:
            total_norm = 0.0
            param_count = 0
            for param in model.parameters():
                if param.grad is not None:
                    param_norm = param.grad.data.norm(2)
                    total_norm += param_norm.item() ** 2
                    param_count += 1

            if param_count > 0:
                total_norm = total_norm ** 0.5
                logger.info(f"步骤 {self.step_counter} ({step_type}) - 梯度范数: {total_norm:.4f}")

    def _log_detailed_stats(self, model: nn.Module, losses: dict[str, float], step_type: str, force: bool = False) -> None:
        """
        记录详细统计信息

        Args:
            model: PyTorch模型
            losses: 损失值字典
            step_type: 步骤类型
            force: 是否强制记录（即使不到记录频率）
        """
        if not force and self.step_counter % self.detailed_log_frequency != 0:
            return

        logger.info(f"===== 详细模型统计信息 (步骤 {self.step_counter}, {step_type}) =====")

        # 记录损失值
        loss_str = ", ".join([f"{k}: {v:.6f}" for k, v in losses.items()])
        logger.info(f"损失值: {loss_str}")

        # 记录权重统计信息
        if self.track_weights:
            logger.info("----- 权重统计信息 -----")
            for name, param in model.named_parameters():
                if param.requires_grad:
                    data = param.data
                    if data.numel() > 0:
                        logger.info(
                            f"{name}: "
                            f"形状={list(data.shape)}, "
                            f"均值={data.mean().item():.6f}, "
                            f"标准差={data.std().item():.6f}, "
                            f"最小值={data.min().item():.6f}, "
                            f"最大值={data.max().item():.6f}, "
                            f"L2范数={data.norm().item():.6f}"
                        )

        # 记录梯度统计信息
        if self.track_gradients:
            logger.info("----- 梯度统计信息 -----")
            for name, param in model.named_parameters():
                if param.requires_grad and param.grad is not None:
                    grad = param.grad.data
                    if grad.numel() > 0:
                        logger.info(
                            f"{name} (梯度): "
                            f"均值={grad.mean().item():.6f}, "
                            f"标准差={grad.std().item():.6f}, "
                            f"最小值={grad.min().item():.6f}, "
                            f"最大值={grad.max().item():.6f}, "
                            f"L2范数={grad.norm().item():.6f}"
                        )

        # 记录激活值统计信息
        if self.track_activations:
            logger.info("----- 激活值统计信息 -----")
            for name, activation in self.activation_values.items():
                if activation.numel() > 0:
                    logger.info(
                        f"{name} (激活值): "
                        f"形状={list(activation.shape)}, "
                        f"均值={activation.mean().item():.6f}, "
                        f"标准差={activation.std().item():.6f}, "
                        f"最小值={activation.min().item():.6f}, "
                        f"最大值={activation.max().item():.6f}"
                    )

        logger.info("=" * 50)

    def __del__(self):
        """析构函数，确保钩子被移除"""
        self.remove_hooks()
