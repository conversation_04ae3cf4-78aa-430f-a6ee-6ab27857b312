"""注意力模块 - 提供高级注意力机制的实现

本模块实现了高级注意力机制，包括：
1. 多尺度注意力
2. 自适应注意力
3. 混合注意力
4. 条件注意力
"""


import torch
import torch.nn.functional as f
from torch import nn

from src.models.base.base_module import BaseModule

# 已删除 HierarchicalAttention 类，该类已被 MultiLayerAttention 类替代
# 新的 MultiLayerAttention 类位于 src/models/gan/components/adaptive_attention.py


class MultiScaleAttention(BaseModule):
    """多尺度注意力 - 在不同时间尺度上关注信息"""

    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        num_scales: int,
        dilation_rates: list[int],
        dropout: float
    ):
        """初始化多尺度注意力

        Args:
            embed_dim: 嵌入维度
            num_heads: 注意力头数
            num_scales: 尺度数量
            dilation_rates: 扩张率列表
            dropout: Dropout比率
        """
        super().__init__("MultiScaleAttention")

        # 存储参数
        self.num_heads = num_heads
        self.embed_dim = embed_dim

        # 确保扩张率数量与尺度数量一致
        assert len(dilation_rates) == num_scales, f"扩张率数量({len(dilation_rates)})与尺度数量({num_scales})不一致"

        # 确保嵌入维度可以被头数整除
        assert embed_dim % num_heads == 0, f"嵌入维度({embed_dim})必须能被头数({num_heads})整除"

        # 创建多尺度注意力
        self.attention_scales = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(
                    in_channels=embed_dim,
                    out_channels=embed_dim,
                    kernel_size=3,
                    padding=dilation,
                    dilation=dilation
                ),
                nn.LeakyReLU(0.2),
                nn.Dropout(dropout)
            )
            for dilation in dilation_rates
        ])

        # 尺度权重
        self.scale_weights = nn.Parameter(torch.ones(num_scales) / num_scales)

        # 输出投影
        self.output_proj = nn.Linear(embed_dim, embed_dim)

        # 层归一化
        self.layer_norm = nn.LayerNorm(embed_dim)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, embed_dim]

        Returns:
            torch.Tensor: 输出 [batch_size, seq_length, embed_dim]
        """
        # 转换维度顺序 [batch_size, seq_length, embed_dim] -> [batch_size, embed_dim, seq_length]
        x_trans = x.transpose(1, 2)

        # 应用多尺度注意力
        scale_outputs = []
        for _, attention_scale in enumerate(self.attention_scales):
            scale_output = attention_scale(x_trans)
            scale_outputs.append(scale_output)

        # 计算加权和
        scale_weights = f.softmax(self.scale_weights, dim=0)
        weighted_sum = torch.zeros_like(x_trans)
        for i, scale_output in enumerate(scale_outputs):
            weighted_sum += scale_output * scale_weights[i]

        # 转换回原始维度顺序 [batch_size, embed_dim, seq_length] -> [batch_size, seq_length, embed_dim]
        weighted_sum = weighted_sum.transpose(1, 2)

        # 输出投影
        output = self.output_proj(weighted_sum)

        # 残差连接
        output = x + self.dropout(output)

        # 层归一化
        output = self.layer_norm(output)

        return output
