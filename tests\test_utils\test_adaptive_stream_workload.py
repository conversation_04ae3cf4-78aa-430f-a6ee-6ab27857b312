"""
测试自适应流管理器在真实工作负载下的表现

此脚本模拟真实的工作负载，测试自适应流管理器是否能够根据 GPU 利用率动态调整流数量限制。
"""

import os
import sys
import time

import torch
import yaml

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.utils.cuda import cuda_manager


def print_separator():
    print("\n" + "="*50 + "\n")

def simulate_high_workload(duration=10):
    """模拟高负载工作"""
    print(f"模拟高负载工作 ({duration} 秒)...")

    # 创建大矩阵
    size = 8000
    a = torch.randn(size, size, device='cuda')
    b = torch.randn(size, size, device='cuda')

    # 执行计算密集型操作
    start_time = time.time()
    iteration = 0

    while time.time() - start_time < duration:
        # 矩阵乘法
        _ = torch.matmul(a, b)
        # 确保计算完成
        torch.cuda.synchronize()

        # 记录 GPU 利用率和流限制
        iteration += 1
        if iteration % 5 == 0:  # 每5次迭代打印一次
            stats = cuda_manager.get_stream_stats()
            print(f"[高负载] GPU利用率: {stats.get('current_utilization'):.1f}%, 当前流限制: {stats.get('current_limit')}, 调整次数: {stats.get('adjustment_count')}")

        # 短暂休息，避免 CPU 占用过高
        time.sleep(0.1)

    # 释放内存
    del a, b
    torch.cuda.empty_cache()

def simulate_low_workload(duration=10):
    """模拟低负载工作"""
    print(f"模拟低负载工作 ({duration} 秒)...")

    # 创建小矩阵
    size = 1000
    a = torch.randn(size, size, device='cuda')
    b = torch.randn(size, size, device='cuda')

    # 执行轻量级操作
    start_time = time.time()
    iteration = 0

    while time.time() - start_time < duration:
        # 矩阵加法
        _ = a + b
        # 确保计算完成
        torch.cuda.synchronize()

        # 记录 GPU 利用率和流限制
        iteration += 1
        if iteration % 5 == 0:  # 每5次迭代打印一次
            stats = cuda_manager.get_stream_stats()
            print(f"[低负载] GPU利用率: {stats.get('current_utilization'):.1f}%, 当前流限制: {stats.get('current_limit')}, 调整次数: {stats.get('adjustment_count')}")

        # 较长休息，进一步降低负载
        time.sleep(0.5)

    # 释放内存
    del a, b
    torch.cuda.empty_cache()

def test_adaptive_stream_workload():
    """测试自适应流管理器在真实工作负载下的表现"""
    print("开始测试自适应流管理器在真实工作负载下的表现")

    # 确保CUDA可用
    if not torch.cuda.is_available():
        print("CUDA不可用，无法测试自适应流管理器")
        return

    print(f"CUDA可用: {torch.cuda.get_device_name()}")

    # 加载配置
    config_path = os.path.join(project_root, 'config.yaml')
    with open(config_path, encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 配置 cuda_manager
    print("配置 cuda_manager...")
    cuda_manager.configure(config['system']['cuda'])
    print("cuda_manager 配置完成")

    # 获取流管理器统计信息
    stats = cuda_manager.get_stream_stats()
    print(f"初始流统计信息: {stats}")

    # 检查是否使用自适应流管理器
    if stats.get('manager_type') != 'adaptive':
        print("警告: 未使用自适应流管理器，请检查配置")
        return
    else:
        print("确认: 使用自适应流管理器")

    # 获取自适应流管理器
    # 使用流管理器的公共接口获取流统计信息
    stream_stats = cuda_manager.get_stream_stats()
    if stream_stats.get('adaptive_enabled'):
        # 自适应流管理器已启用，使用流统计信息

        # 打印自适应流管理器参数
        print("自适应流管理器参数:")
        print(f"  - 流统计信息: {stream_stats}")
    else:
        print("错误: 自适应流管理器未初始化")
        return

    print_separator()

    # 创建一些流，用于测试
    print("创建测试流...")
    streams = []
    for i in range(10):
        stream_name = f"test_stream_{i}"
        stream = cuda_manager.create_stream(stream_name)
        streams.append((stream_name, stream))

    # 获取流管理器统计信息
    stats = cuda_manager.get_stream_stats()
    print(f"创建流后的统计信息: {stats}")

    print_separator()

    # 模拟工作负载循环
    print("开始模拟工作负载循环...")

    # 先进行一次高负载工作，让 GPU 利用率上升
    simulate_high_workload(duration=15)

    # 然后进行一次低负载工作，让 GPU 利用率下降
    simulate_low_workload(duration=15)

    # 再次进行高负载工作
    simulate_high_workload(duration=15)

    # 最后进行低负载工作
    simulate_low_workload(duration=15)

    print_separator()

    # 释放所有流
    print("释放所有流...")
    for stream_name, _ in streams:
        cuda_manager.release_stream(stream_name)

    # 最终统计
    stats = cuda_manager.get_stream_stats()
    print(f"最终流统计信息: {stats}")

    print("\n测试完成!")

if __name__ == "__main__":
    test_adaptive_stream_workload()
