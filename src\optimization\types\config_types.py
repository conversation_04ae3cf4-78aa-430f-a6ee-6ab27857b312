"""
参数优化模块的配置类型定义。
"""

from dataclasses import asdict, dataclass, field  # Import asdict
from typing import Any

# 导入主要的配置类，避免重复定义
from src.utils.config.training import BalanceConfig, LossConfig, OptimizerConfig
from src.utils.config.training import LrBalancerConfig as MainLrBalancerConfig

# 使用从src.utils.config.training导入的类
# LRBalancerConfig -> MainLrBalancerConfig
# OptimizerConfig -> OptimizerConfig
# BalanceConfig -> BalanceConfig


# LossConfig类已从src.utils.config.training导入，不再在此处定义


@dataclass
class AttentionConfig:
    """注意力机制配置"""
    num_heads: int
    head_dim: int
    dropout: float


@dataclass
class AdaptiveAttentionConfig:
    """自适应注意力配置"""
    dilation_rates: list[int] = field(default_factory=lambda: [1, 2, 4, 8])
    adjustment_threshold: float = 0.3


@dataclass
class ModelConfig:
    """模型配置"""
    hidden_dim: int
    noise_dim: int
    dropout_rate: float
    layers: int # Moved before fields with defaults
    attention: AttentionConfig | None = None # Optional is fine
    adaptive_attention: AdaptiveAttentionConfig | None = None # Optional is fine
    loss: LossConfig | None = None # Optional is fine


@dataclass
class DataConfig:
    """数据配置"""
    window_size: int
    batch_size: int
    stride: int
    sample_size: int | None = None # Optional is fine


@dataclass
class TrainingConfig:
    """训练配置"""
    lr_balancer: MainLrBalancerConfig # Must be provided by config
    optimizer: OptimizerConfig | dict[str, Any] # Must be provided by config
    balance: BalanceConfig # Must be provided by config
    lambda_gp: float
    epochs: int
    validation_freq: int


@dataclass
class LoggingConfig:
    """日志配置"""
    verbose: bool


@dataclass
class ParameterOptimizationConfig:
    """完整的参数优化配置"""
    # Remove default_factory as these configs are now mandatory and have required fields
    training: TrainingConfig
    model: ModelConfig
    data: DataConfig
    logging: LoggingConfig

    @classmethod
    def from_dict(cls, config_dict: dict[str, Any]) -> 'ParameterOptimizationConfig':
        """从字典创建配置对象"""
        training_dict = config_dict.get('training', {})
        model_dict = config_dict.get('model', {})
        data_dict = config_dict.get('data', {})
        logging_dict = config_dict.get('logging', {}) # Keep default here, logging might be optional overall

        # --- Remove defaults from .get() calls for critical parameters ---
        training = TrainingConfig(
            # Nested structures need careful handling - assume they exist if top-level training exists
            lr_balancer=MainLrBalancerConfig(**training_dict['lr_balancer']),
            optimizer=(OptimizerConfig(**training_dict['optimizer'])
                       if isinstance(training_dict.get('optimizer'), dict) # Check type before accessing
                       else training_dict['optimizer']), # Assume optimizer exists if training exists
            balance=BalanceConfig(**training_dict['balance']),
            lambda_gp=training_dict['lambda_gp'], # Direct access, will raise KeyError if missing
            epochs=training_dict['epochs'], # Direct access
            validation_freq=training_dict['validation_freq'] # Direct access
        )

        model = ModelConfig(
            hidden_dim=model_dict['hidden_dim'], # Direct access
            noise_dim=model_dict['noise_dim'], # Direct access
            dropout_rate=model_dict['dropout_rate'], # Direct access
            layers=model_dict['layers'], # Direct access
            # Optional fields remain the same
            attention=(AttentionConfig(**model_dict['attention'])
                      if model_dict.get('attention') else None),
            adaptive_attention=(AdaptiveAttentionConfig(**model_dict['adaptive_attention'])
                              if model_dict.get('adaptive_attention') else None),
            loss=(LossConfig(**model_dict['loss'])
                  if model_dict.get('loss') else None)
        )

        data = DataConfig(
            window_size=data_dict['window_size'], # Direct access
            batch_size=data_dict['batch_size'], # Direct access
            stride=data_dict['stride'], # Direct access
            sample_size=data_dict.get('sample_size', None) # sample_size is Optional
        )

        logging_config = LoggingConfig(
            verbose=logging_dict['verbose'] # Direct access
        )

        return cls(training=training, model=model, data=data, logging=logging_config)

    def to_dict(self) -> dict[str, Any]:
        """将配置对象转换为字典"""
        # Use standard asdict for simplicity
        # If specific handling (like ignoring None) is needed,
        # it should be done where this method is called.
        return asdict(self)
