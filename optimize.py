"""
超参数优化入口脚本

本脚本使用 Optuna 框架运行超参数搜索，支持两阶段优化模式。通过快速筛选阶段，
可以高效地探索超参数空间，并找出最有潜力的参数组合。

功能特点:
----------
1. 支持多种超参数的同时优化
2. 自动保存优化结果到数据库
3. 支持断点续优化（可以中断后继续）
4. 自动清理 GPU 内存
5. 详细的日志记录
6. 支持内存监控
7. 报告多个最佳参数组合
8. 始终使用快速模式进行参数探索
9. 严格的参数一致性检查
10. 分级参数探索策略，处理参数依赖关系

命令行参数:
----------
--n-trials INT       试验次数 (默认: 100)
--study-name STR     Optuna Study 的名称 (默认: gan_hyperopt_study)
--storage STR        Optuna 存储后端 URL (默认: sqlite:///optuna_study.db)
--top-n INT          报告前N个最佳参数组合 (默认: 5)
--base-config STR    基础配置文件的路径 (默认: config.yaml)
--log-level STR      日志级别 [DEBUG|INFO|WARNING|ERROR|CRITICAL] (默认: 使用配置文件中的设置)
--monitor-memory     启用内存监控 (默认: False)

使用示例:
----------
# 基本用法
python optimize.py

# 指定试验次数和存储位置
python optimize.py --n-trials 50 --storage sqlite:///my_study.db

# 完整参数示例
python optimize.py --n-trials 100 --study-name gan_hpo_run_1 --storage sqlite:///hpo_studies.db --top-n 5 --base-config config.yaml --log-level INFO --monitor-memory

输出说明:
----------
1. 控制台输出: 显示优化过程的主要信息和进度
2. 日志文件: 详细记录优化过程，保存在 logs/optimization 目录下
3. 数据库文件: 保存所有试验结果，可用于后续分析

注意事项:
----------
1. 确保 config.yaml 文件存在且配置正确
2. 优化过程可能需要较长时间，建议在稳定环境中运行
3. 使用 --monitor-memory 参数可能会略微影响性能，但可提供有用的内存使用信息
4. 优化完成后，建议对报告的前N个最佳参数进行完整训练评估

相关文件:
----------
- src/optimization/hyperparameter_optimizer.py: 核心优化逻辑
- src/optimization/parameter_exploration.py: 分级参数探索策略
- src/optimization/metric_extraction.py: 指标提取功能
- src/optimization/memory_management.py: 内存管理功能
- src/optimization/memory_monitoring.py: 内存监控管理
- src/optimization/result_analysis.py: 结果分析功能
- src/optimization/logging_config.py: 日志配置
- src/utils/system_info.py: 系统信息收集
- config.yaml: 基础配置文件
"""

import argparse
import logging
import os
import sys
import time
from datetime import datetime

from src.optimization.hyperparameter_optimizer import run_optimization
from src.optimization.logging_config import setup_optimization_logger
from src.optimization.memory_management import MemoryMonitoringManager
from src.utils.system_info import log_system_info


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="使用 Optuna 进行超参数优化")

    parser.add_argument(
        "--n-trials",
        type=int,
        default=100,
        help="Optuna 优化的试验次数 (默认: 100)"
    )
    parser.add_argument(
        "--study-name",
        type=str,
        default="gan_hyperopt_study",
        help="Optuna Study 的名称 (用于持久化和恢复, 默认: gan_hyperopt_study)"
    )
    parser.add_argument(
        "--storage",
        type=str,
        default="sqlite:///optuna_study.db",
        help="Optuna 存储后端 URL (例如 sqlite:///study.db, 默认: sqlite:///optuna_study.db)"
    )
    parser.add_argument(
        "--top-n",
        type=int,
        default=8,  # 从5改为8
        help="报告前N个最佳参数组合 (默认: 8)"
    )
    parser.add_argument(
        "--base-config",
        type=str,
        default="config.yaml",
        help="基础配置文件的路径 (默认: config.yaml)"
    )
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default=None,
        help="日志级别，覆盖配置文件中的设置 (默认: 使用配置文件中的设置)"
    )
    parser.add_argument(
        "--monitor-memory",
        action="store_true",
        help="启用内存监控 (默认: False)"
    )

    return parser.parse_args()


def format_time(seconds):
    """格式化时间"""
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}小时{int(minutes)}分钟{seconds:.2f}秒"


if __name__ == "__main__":
    start_time = time.time()

    # 初始化日志系统
    logger = setup_optimization_logger(
        log_dir="logs/optimization",
        console_level="INFO",
        file_level="DEBUG",
        config_path="config.yaml"
    )

    # 记录启动信息
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    logger.info("=" * 80)
    logger.info(f"启动超参数优化脚本 - {current_time}")
    logger.info("=" * 80)

    # 记录系统信息
    log_system_info(logger)

    # 解析命令行参数
    args = parse_args()

    # 如果指定了日志级别，则覆盖配置
    if args.log_level:
        logger.setLevel(getattr(logging, args.log_level))
        logger.info(f"日志级别已设置为: {args.log_level}")

    # 记录运行参数
    logger.info("运行参数:")
    for key, value in vars(args).items():
        logger.info(f"- {key}: {value}")

    # 检查配置文件是否存在
    if not os.path.exists(args.base_config):
        logger.error(f"配置文件不存在: {args.base_config}")
        print(f"错误: 配置文件不存在: {args.base_config}")
        sys.exit(1)

    # 初始化内存监控管理器
    memory_monitor = MemoryMonitoringManager(logger)
    if args.monitor_memory:
        memory_monitor.start_monitoring()

    try:
        # 执行优化流程
        logger.info("开始超参数优化过程...")
        run_optimization(
            n_trials=args.n_trials,
            study_name=args.study_name,
            storage=args.storage,
            top_n=args.top_n,
            base_config_path=args.base_config
        )

        # 报告内存使用情况
        if args.monitor_memory:
            memory_monitor.report_memory_stats()

        # 计算总运行时间
        elapsed_time = time.time() - start_time
        time_str = format_time(elapsed_time)

        # 记录完成信息
        logger.info("=" * 80)
        logger.info(f"超参数优化过程成功完成。总耗时: {time_str}")
        logger.info("=" * 80)

    except Exception as e:
        # 处理异常
        logger.error(f"超参数优化过程中发生错误: {e}", exc_info=True)
        print("错误: 优化过程失败。详情请查看日志。")

        # 清理资源
        if args.monitor_memory:
            memory_monitor.clear_cache()

        sys.exit(1)  # 返回非零退出码表示失败

    finally:
        # 停止内存监控
        if args.monitor_memory:
            memory_monitor.stop_monitoring()

    sys.exit(0)  # 成功退出
