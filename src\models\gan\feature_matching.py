"""特征匹配模块 - 提供GAN模型的特征匹配损失功能

本模块实现了GAN模型的特征匹配损失功能，包括：
1. 特征匹配损失
2. 感知损失
3. 风格损失
4. 内容损失
5. 多尺度特征匹配
"""


import torch
import torch.nn.functional as f
from torch import nn


class FeatureMatchingLoss(nn.Module):
    """特征匹配损失 - 使生成的特征与真实特征相似"""

    def __init__(self):
        """初始化特征匹配损失"""
        super().__init__()
        # 移除 lambda_feat
        self.l1_loss = nn.L1Loss(reduction='none') # 保留 L1 损失以备将来使用或调试

    def forward(
        self,
        real_features: list[torch.Tensor],
        fake_features: list[torch.Tensor]
    ) -> torch.Tensor:
        """前向传播

        Args:
            real_features: 真实特征列表
            fake_features: 生成特征列表

        Returns:
            torch.Tensor: 特征匹配损失
        """
        # 检查特征列表长度是否一致
        if len(real_features) != len(fake_features):
            raise ValueError(
                f"真实特征列表长度({len(real_features)})与"
                f"生成特征列表长度({len(fake_features)})不一致"
            )

        # 计算特征匹配损失
        loss = torch.tensor(0.0, device=real_features[0].device if real_features else 'cpu')
        for real_feat, fake_feat in zip(real_features, fake_features, strict=False):
            # 展平时序维度 [B, C, T] -> [B, C*T]
            real_feat = real_feat.flatten(start_dim=1)
            fake_feat = fake_feat.flatten(start_dim=1)

            # 计算余弦相似度
            real_feat = f.normalize(real_feat.detach(), p=2, dim=1)
            fake_feat = f.normalize(fake_feat, p=2, dim=1)
            cos_sim = (fake_feat * real_feat).sum(dim=1)

            # 计算损失 (1 - 余弦相似度)
            loss += (1 - cos_sim).mean()

        # 直接返回累加的损失，不再乘以权重
        # 确保返回的是torch.Tensor类型
        return loss


class PerceptualLoss(nn.Module):
    """感知损失 - 使用预训练模型提取特征进行比较"""

    def __init__(
        self,
        feature_extractor: nn.Module,
        lambda_percep: float = 1.0,
        normalize: bool = True
    ):
        """初始化感知损失

        Args:
            feature_extractor: 特征提取器
            lambda_percep: 感知损失权重
            normalize: 是否归一化特征
        """
        super().__init__()
        self.feature_extractor = feature_extractor
        self.lambda_percep = lambda_percep
        self.normalize = normalize
        self.l1_loss = nn.L1Loss()

        # 冻结特征提取器参数
        for param in self.feature_extractor.parameters():
            param.requires_grad = False

    def forward(
        self,
        real_images: torch.Tensor,
        fake_images: torch.Tensor
    ) -> torch.Tensor:
        """前向传播

        Args:
            real_images: 真实图像
            fake_images: 生成图像

        Returns:
            torch.Tensor: 感知损失
        """
        # 提取特征
        real_features = self.feature_extractor(real_images)
        fake_features = self.feature_extractor(fake_images)

        # 归一化特征
        if self.normalize:
            real_features = f.normalize(real_features, p=2, dim=1)
            fake_features = f.normalize(fake_features, p=2, dim=1)

        # 计算感知损失
        loss = self.l1_loss(fake_features, real_features.detach())

        # 应用权重
        loss = loss * self.lambda_percep

        return loss


class StyleLoss(nn.Module):
    """风格损失 - 使生成的风格与真实风格相似"""

    def __init__(self, lambda_style: float = 1.0):
        """初始化风格损失

        Args:
            lambda_style: 风格损失权重
        """
        super().__init__()
        self.lambda_style = lambda_style
        self.mse_loss = nn.MSELoss()

    def gram_matrix(self, features: torch.Tensor) -> torch.Tensor:
        """计算Gram矩阵

        Args:
            features: 特征 [batch_size, channels, height, width]

        Returns:
            torch.Tensor: Gram矩阵 [batch_size, channels, channels]
        """
        batch_size, channels, height, width = features.size()
        features = features.view(batch_size, channels, height * width)
        gram = torch.bmm(features, features.transpose(1, 2))
        return gram / (channels * height * width)

    def forward(
        self,
        real_features: list[torch.Tensor],
        fake_features: list[torch.Tensor]
    ) -> torch.Tensor:
        """前向传播

        Args:
            real_features: 真实特征列表
            fake_features: 生成特征列表

        Returns:
            torch.Tensor: 风格损失
        """
        # 检查特征列表长度是否一致
        if len(real_features) != len(fake_features):
            raise ValueError(
                f"真实特征列表长度({len(real_features)})与"
                f"生成特征列表长度({len(fake_features)})不一致"
            )

        # 计算风格损失
        loss = torch.tensor(0.0, device=real_features[0].device if real_features else 'cpu')
        for real_feat, fake_feat in zip(real_features, fake_features, strict=False):
            real_gram = self.gram_matrix(real_feat)
            fake_gram = self.gram_matrix(fake_feat)
            loss += self.mse_loss(fake_gram, real_gram.detach())

        # 应用权重
        loss = loss * self.lambda_style

        # 确保返回的是torch.Tensor类型
        return loss
