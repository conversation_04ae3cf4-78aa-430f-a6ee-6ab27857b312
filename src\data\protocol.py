"""数据集协议定义模块 - 包含数据集接口协议定义和基础实现

本模块定义数据集相关的协议接口，用于类型检查和接口规范。
同时提供基础时序数据集的实现，作为其他时序数据集的基类。

相关模块：
- src/data/windowed_time_series.py: 窗口数据集实现
- src/utils/logger.py: 日志系统
"""

from typing import Any, Protocol, runtime_checkable

import torch
from torch.utils.data import Dataset as TorchDataset

from src.utils.logger import get_logger


@runtime_checkable
class DatasetProtocol(Protocol):
    """通用数据集协议"""

    def __len__(self) -> int:
        """返回数据集长度"""
        ...

    def __getitem__(self, index: int) -> dict[str, Any]:
        """获取指定索引的数据样本

        Args:
            index: 样本索引

        Returns:
            Dict[str, Any]: 包含数据的字典
        """
        ...


@runtime_checkable
class TimeSeriesDatasetProtocol(Protocol):
    """时间序列数据集协议"""

    def __len__(self) -> int:
        """返回数据集长度"""
        ...

    def __getitem__(self, index: int) -> dict[str, torch.Tensor]:
        """获取指定索引的数据样本

        Args:
            index: 样本索引

        Returns:
            Dict[str, torch.Tensor]: 包含特征和目标的字典
        """
        ...

    def get_all_data(self) -> dict[str, torch.Tensor]:
        """获取全部数据

        Returns:
            Dict[str, torch.Tensor]: 包含所有数据的字典
        """
        ...

@runtime_checkable
class WindowDataset(Protocol):
    """窗口数据集协议"""

    @property
    def feature_dim(self) -> int:
        """获取特征维度"""
        ...

    def __len__(self) -> int:
        """返回数据集长度"""
        ...

    def __getitem__(self, index: int) -> dict[str, torch.Tensor]:
        """获取指定索引的数据样本

        Args:
            index: 样本索引

        Returns:
            Dict[str, torch.Tensor]: 包含特征和目标的字典
        """
        ...

class TimeSeriesDataset(TorchDataset):
    """基础时序数据集 - 提供时间序列数据处理基础功能

    作为其他时序数据集的基类，提供基础功能实现。
    这是一个最小化实现，仅包含必要的方法以支持TimeSeriesWindowDataset。
    """

    def __init__(self):
        """初始化基础时序数据集"""
        self.logger = get_logger(self.__class__.__name__)
        self._features = None
        self._targets = None
        self._feature_components = {}

    def __len__(self) -> int:
        """返回数据集长度"""
        if self._features is not None:
            return len(self._features)
        return 0

    def __getitem__(self, index: int) -> dict[str, torch.Tensor]:
        """获取指定索引的数据样本

        Args:
            index: 样本索引

        Returns:
            Dict[str, torch.Tensor]: 包含特征和目标的字典
        """
        if self._features is None or self._targets is None:
            raise ValueError("特征或目标数据未初始化")

        if index >= len(self._features):
            raise IndexError(f"索引{index}超出范围[0, {len(self._features)-1}]")

        return {
            'features': self._features[index],
            'target': self._targets[index]
        }

    def get_all_data(self) -> dict[str, torch.Tensor]:
        """获取全部数据

        Returns:
            Dict[str, torch.Tensor]: 包含所有数据的字典
        """
        if self._features is None or self._targets is None:
            return {'features': torch.tensor([]), 'targets': torch.tensor([])}

        return {
            'features': self._features,
            'targets': self._targets
        }

    @property
    def features(self) -> torch.Tensor:
        """获取特征数据"""
        if self._features is None:
            raise ValueError("特征数据未初始化")
        return self._features

    @property
    def targets(self) -> torch.Tensor:
        """获取目标数据"""
        if self._targets is None:
            raise ValueError("目标数据未初始化")
        return self._targets

    @property
    def feature_components(self) -> dict[str, Any]:
        """获取特征组件"""
        return self._feature_components

    @property
    def get_target_standardizer(self) -> Any:
        raise NotImplementedError("请在子类中实现get_target_standardizer属性")

    @property
    def target_standardizer(self) -> Any:
        raise NotImplementedError("请在子类中实现target_standardizer属性")

__all__ = ['DatasetProtocol', 'TimeSeriesDataset', 'TimeSeriesDatasetProtocol', 'WindowDataset']
