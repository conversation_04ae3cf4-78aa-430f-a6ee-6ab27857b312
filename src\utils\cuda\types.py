"""CUDA 管理相关的数据类型定义"""

from dataclasses import dataclass
from typing import Any


class CUDAInitError(Exception):
    """CUDA初始化错误"""
    pass


@dataclass
class GPUMemoryInfo:
    """GPU内存信息"""
    used_gb: float  # 已使用内存(GB)
    free_gb: float  # 可用内存(GB)
    cached_gb: float  # 缓存内存(GB)
    total_gb: float  # 总内存(GB)
    utilization: float  # 使用率

    def to_mb(self) -> dict[str, float]:
        """转换为MB单位"""
        return {
            'used_mb': self.used_gb * 1024,
            'free_mb': self.free_gb * 1024,
            'cached_mb': self.cached_gb * 1024,
            'total_mb': self.total_gb * 1024
        }

    def to_dict(self) -> dict[str, float]:
        """转换为字典格式"""
        return {
            'used_gb': self.used_gb,
            'free_gb': self.free_gb,
            'cached_gb': self.cached_gb,
            'total_gb': self.total_gb,
            'utilization': self.utilization,
            **self.to_mb()
        }

    def __str__(self) -> str:
        """格式化输出"""
        return (
            f"GPU内存使用情况:\n"
            f"- 已用: {self.used_gb:.2f}GB ({self.utilization*100:.1f}%)\n"
            f"- 缓存: {self.cached_gb:.2f}GB\n"
            f"- 可用: {self.free_gb:.2f}GB\n"
            f"- 总计: {self.total_gb:.2f}GB"
        )


@dataclass
class StreamInfo:
    """CUDA流信息"""
    stream: Any  # CUDA流对象
    name: str  # 流名称
    created_time: float  # 创建时间
    last_used_time: float  # 最后使用时间
    total_execution_time: float = 0.0  # 总执行时间
    execution_count: int = 0  # 执行次数
    is_busy: bool = False  # 是否正在执行任务


@dataclass
class StreamEventInfo:
    """CUDA流事件信息"""
    event: Any  # CUDA事件对象
    name: str  # 事件名称
    created_time: float  # 创建时间
    recorded_time: float | None = None  # 记录时间
    elapsed_time: float | None = None  # 事件间隔时间
