"""注意力机制工厂

本模块提供了创建不同类型注意力机制的工厂函数。
"""

from typing import Any

import torch

from src.models.base.base_module import BaseModule
from src.models.gan.components.adaptive_attention import (
    AdaptiveDilationAttention,
    MultiLayerAttention,
)
from src.utils.cuda import cuda_manager
from src.utils.logger import get_logger

# 创建日志记录器
logger = get_logger("attention_factory")


def create_adaptive_attention(
    embed_dim: int,
    num_heads: int,
    num_scales: int,
    dropout: float,
    use_cuda_streams: bool,
    cuda_stream_config: dict[str, Any],
    device: torch.device
) -> BaseModule:
    """创建自适应扩张率注意力机制

    Args:
        embed_dim: 嵌入维度
        num_heads: 注意力头数
        num_scales: 尺度数量
        dropout: Dropout比率
        use_cuda_streams: 是否使用CUDA流（如果可用）
        cuda_stream_config: CUDA流配置参数
        device: 设备

    Returns:
        BaseModule: 自适应扩张率注意力机制实例
    """
    # 参数验证
    if embed_dim <= 0:
        error_msg = f"嵌入维度必须为正数，当前值: {embed_dim}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # 参数验证
    if num_heads <= 0:
        error_msg = f"注意力头数必须为正数，当前值: {num_heads}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    if num_scales <= 0:
        error_msg = f"尺度数量必须为正数，当前值: {num_scales}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    if dropout < 0 or dropout >= 1:
        error_msg = f"Dropout比率必须在[0, 1)范围内，当前值: {dropout}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # 默认CUDA流配置参数名称
    required_config_keys = ["max_batch_streams", "max_scale_streams", "verbose_logging"]

    # 检查CUDA流配置已在函数签名中强制要求

    # 检查必要的配置参数
    for key in required_config_keys:
        if key not in cuda_stream_config:
            error_msg = f"缺少必要的CUDA流配置参数: '{key}'"
            logger.error(error_msg)
            raise ValueError(error_msg)

    logger.info(f"CUDA流配置: {cuda_stream_config}")

    # 设备验证已在函数签名中强制要求

    # 创建自适应扩张率注意力机制
    logger.info(f"创建自适应扩张率注意力机制 - embed_dim: {embed_dim}, num_heads: {num_heads}, num_scales: {num_scales}")

    try:
        attention = AdaptiveDilationAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            num_scales=num_scales,
            dropout=dropout,
            use_streams=use_cuda_streams and cuda_manager.is_cuda_available,
            max_batch_streams=cuda_stream_config["max_batch_streams"],
            max_scale_streams=cuda_stream_config["max_scale_streams"],
            verbose_logging=cuda_stream_config["verbose_logging"]
        )
        logger.info("自适应扩张率注意力机制创建成功")
    except Exception as e:
        logger.error(f"创建自适应扩张率注意力机制失败: {e!s}")
        raise

    # 移动到指定设备
    try:
        attention = attention.to(device)
        logger.info(f"注意力机制已移动到设备: {device}")
    except Exception as e:
        logger.error(f"移动注意力机制到设备 {device} 失败: {e!s}")
        raise

    return attention


def create_multi_layer_attention(
    embed_dim: int,
    num_heads: int,
    num_layers: int,
    dropout: float,
    device: torch.device
) -> BaseModule:
    """创建多层注意力机制

    Args:
        embed_dim: 嵌入维度
        num_heads: 注意力头数
        num_layers: 层数
        dropout: Dropout比率
        device: 设备

    Returns:
        BaseModule: 多层注意力机制实例
    """
    # 参数验证
    if embed_dim <= 0:
        error_msg = f"嵌入维度必须为正数，当前值: {embed_dim}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    if num_heads <= 0:
        error_msg = f"注意力头数必须为正数，当前值: {num_heads}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    if num_layers <= 0:
        error_msg = f"层数必须为正数，当前值: {num_layers}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    if dropout < 0 or dropout >= 1:
        error_msg = f"Dropout比率必须在[0, 1)范围内，当前值: {dropout}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # 参数已在函数签名中强制要求，并在上面进行了验证

    # 设备验证已在函数签名中强制要求

    logger.info(f"创建多层注意力机制 - embed_dim: {embed_dim}, num_heads: {num_heads}, num_layers: {num_layers}")

    try:
        attention = MultiLayerAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            num_layers=num_layers,
            dropout=dropout
        )
        logger.info("多层注意力机制创建成功")
    except Exception as e:
        logger.error(f"创建多层注意力机制失败: {e!s}")
        raise

    # 移动到指定设备
    try:
        attention = attention.to(device)
        logger.info(f"注意力机制已移动到设备: {device}")
    except Exception as e:
        logger.error(f"移动注意力机制到设备 {device} 失败: {e!s}")
        raise

    return attention
