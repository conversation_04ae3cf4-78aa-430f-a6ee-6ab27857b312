"""配置转换器 - 负责配置对象与字典之间的转换

注意：由于Python dataclass内部实现细节，此处使用类型忽略注释()
来绕过静态类型检查器的限制。运行时类型安全通过is_dataclass()检查保证。
"""

from dataclasses import asdict, is_dataclass
from pathlib import Path
from typing import Any

# 添加导入
from src.utils.logger import get_logger


class ConfigConverter:
    """配置转换工具类"""

    @staticmethod
    def to_dict(config_obj: Any) -> dict[str, Any]:
        """将配置对象递归转换为字典

        Args:
            config_obj: 要转换的配置对象，必须是数据类实例

        Returns:
            转换后的字典，包含所有配置项

        Raises:
            ValueError: 如果输入不是数据类实例
        """
        # 获取日志记录器
        logger = get_logger(__name__)
        logger.debug(f"开始将 {type(config_obj).__name__} 转换为字典")

        if not is_dataclass(config_obj):
            error_msg = "输入对象必须是数据类实例"
            logger.error(error_msg)
            raise ValueError(error_msg)

        def dict_factory(data):
            result = {}
            for k, v in data:
                if isinstance(v, Path):
                    result[k] = str(v)  # 将Path对象转换为字符串
                elif is_dataclass(v):
                    result[k] = ConfigConverter.to_dict(v)  # 递归处理嵌套数据类
                else:
                    result[k] = v
            return result

        # 使用类型忽略绕过静态类型检查
        # 运行时安全由is_dataclass()保证
        temp_dict = asdict(config_obj, dict_factory=dict_factory)  # type: ignore[arg-type]
        if '_logger' in temp_dict:
            del temp_dict['_logger']

        logger.debug(f"{type(config_obj).__name__} 转换为字典完成")
        return temp_dict
