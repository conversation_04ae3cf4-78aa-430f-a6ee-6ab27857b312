# 层级间空值处理机制

## 概述

层级间空值处理机制是特征工程系统中的一个关键组件，用于防止NaN值在层级化特征生成过程中传播，确保数据质量和系统稳定性。

## 问题背景

在层级化特征生成过程中，各个特征生成器（如差分特征、窗口统计特征等）可能会产生NaN值：

1. **差分特征生成器**：在计算差分时，前几行会被填充为NaN
2. **窗口统计特征生成器**：在窗口大小不足时可能产生NaN
3. **波动性特征生成器**：在计算GARCH模型时可能遇到数值问题
4. **交互特征生成器**：在特征交互计算中可能产生NaN

如果这些NaN值传播到下一层级，会导致：
- 后续特征生成器失败
- 模型训练异常
- 预测结果不可靠

## 解决方案

### 核心设计原则

1. **层级间隔离**：每层完成后立即处理NaN值，防止传播到下一层
2. **基础实现**：使用简单有效的填充策略，不过度复杂化
3. **充分暴露问题**：当NaN值过多时直接抛出异常，而不是掩盖问题
4. **双重保护**：既处理传递给下一层的数据，也处理添加到最终特征集的数据

### 实现机制

#### 1. 层级间数据传递处理

在每个层级处理完成后，传递给下一层级之前：

```python
# 在 enhance_features 方法中
current_layer_input = torch.cat(next_layer_input_components, dim=1)
# 层级间空值处理
current_layer_input = self._handle_interlayer_nan(current_layer_input, level)
```

#### 2. 最终特征集处理

在将衍生特征添加到最终特征集时：

```python
# 合并当前层新生成的特征
level_derived_combined_tensor = torch.cat(current_layer_derived_features, dim=1)
# 对添加到最终特征集的衍生特征也进行空值处理
level_derived_combined_tensor = self._handle_interlayer_nan(level_derived_combined_tensor, level)
final_feature_collection.append(level_derived_combined_tensor)
```

### 空值处理策略

#### 1. NaN比例检查

```python
nan_count = torch.isnan(data).sum().item()
total_elements = data.numel()
nan_ratio = nan_count / total_elements

max_nan_ratio = 0.3  # 30%阈值
if nan_ratio > max_nan_ratio:
    raise ValueError(f"Level {level}: NaN值比例过高 ({nan_ratio:.4f} > {max_nan_ratio})")
```

#### 2. 前向填充策略

对于每一列：
1. 找到第一个非NaN值
2. 用第一个有效值填充前面的NaN
3. 对剩余NaN进行前向填充
4. 如果整列都是NaN，用0填充

```python
# 前向填充
mask = ~np.isnan(col_numpy)
if mask.any():
    first_valid_idx = np.where(mask)[0][0]
    col_numpy[:first_valid_idx] = col_numpy[first_valid_idx]
    
    # 前向填充剩余的NaN
    for i in range(1, len(col_numpy)):
        if np.isnan(col_numpy[i]):
            col_numpy[i] = col_numpy[i-1]
else:
    # 整列都是NaN，用0填充
    col_numpy[:] = 0.0
```

## 配置参数

### 硬编码参数（基础实现）

- **max_nan_ratio**: 0.3 (30%) - NaN值比例阈值
- **填充策略**: 前向填充 + 零填充
- **设备一致性**: 保持与输入张量相同的设备

### 未来扩展

可以考虑将以下参数配置化：
- NaN比例阈值
- 填充策略选择（前向/后向/均值/中位数）
- 异常处理策略

## 测试覆盖

### 单元测试

1. **无NaN值情况**：验证正常数据不受影响
2. **包含NaN值情况**：验证NaN值被正确填充
3. **前向填充逻辑**：验证填充算法正确性
4. **整列NaN情况**：验证零填充策略
5. **高比例NaN情况**：验证异常抛出
6. **设备一致性**：验证CUDA设备支持
7. **边界情况**：空张量、单行数据等

### 集成测试

1. **enhance_features流程**：验证在实际特征工程中的工作
2. **多层级传播**：验证多层级中NaN值的正确处理
3. **阈值强制执行**：验证NaN比例阈值的有效性

## 日志记录

系统会记录详细的空值处理信息：

```
Level 1: 检测到 3 个NaN值 (占比: 0.1250)
Level 1: 成功填充 3 个NaN值，数据传递到下一层级
```

## 性能考虑

1. **最小开销**：只在检测到NaN值时才进行处理
2. **内存效率**：使用就地操作减少内存分配
3. **设备优化**：保持张量在原设备上，避免不必要的数据传输

## 错误处理

当NaN值比例超过阈值时，系统会：
1. 记录详细的错误信息
2. 抛出包含层级信息的ValueError
3. 提供NaN比例的具体数值

这种设计确保问题能够被及时发现和解决，而不是被掩盖。
