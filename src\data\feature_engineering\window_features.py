"""窗口统计特征生成器 - 计算滑动窗口统计特征

此模块负责计算时序数据的滑动窗口统计特征，包括：
- 均值、标准差、最大值、最小值等统计量
- 不同窗口大小的统计特征
"""


import numpy as np
import pandas as pd
import torch

from src.data.feature_engineering.base import BaseFeatureGenerator
from src.utils.config.manager import ConfigManager
from src.utils.logger import get_logger


class WindowFeatureGenerator(BaseFeatureGenerator):
    """窗口统计特征生成器

    计算时序数据的滑动窗口统计特征
    """

    def __init__(self, config: ConfigManager):
        """初始化窗口统计特征生成器

        Args:
            config: 配置管理器实例
        """
        if not isinstance(config, ConfigManager):
            raise TypeError("config参数必须为ConfigManager实例")

        self.config = config
        self.logger = get_logger(__name__)
        self.feature_names: list[str] = []
        self._feature_count = 0

        # 检查配置
        self._enabled = False
        self._window_sizes = []
        self._stats = []
        self._check_config()

    def _check_config(self) -> None:
        """检查全局配置，仅设置启用状态"""
        self._enabled = False # 默认禁用
        try:
            if hasattr(self.config, 'feature_engineering'):
                feature_eng = self.config.feature_engineering
                if hasattr(feature_eng, 'enable'):
                    if feature_eng.enable:
                        # 全局特征工程已启用，此生成器可能被层级配置调用
                        self._enabled = True
                        self.logger.info("窗口统计特征生成器已启用（可能由层级配置驱动）。")
                    else:
                        self.logger.info("特征工程全局开关 (feature_engineering.enable) 为 False，窗口统计特征生成器将禁用。")
                        self._enabled = False # Explicitly disable
                else:
                    # feature_engineering.enable 属性缺失
                    error_msg = "配置中 feature_engineering 下缺少 'enable' 配置项。窗口统计特征生成器将禁用。"
                    self.logger.error(error_msg)
                    self._enabled = False # Explicitly disable
            else:
                # feature_engineering 属性缺失
                error_msg = "配置中缺少 'feature_engineering' 配置项。窗口统计特征生成器将禁用。"
                self.logger.error(error_msg)
                self._enabled = False # Explicitly disable
        except AttributeError as e:
            # AttributeError 可能是由于ConfigManager内部结构问题，视为配置错误
            error_msg = f"访问配置项时发生 AttributeError (可能配置结构不正确): {e}。窗口统计特征生成器将禁用。"
            self.logger.error(error_msg)
            self._enabled = False
        except Exception as e:
            # 其他意外错误，记录并禁用
            error_msg = f"检查全局窗口统计特征配置时发生意外错误: {e}。窗口统计特征生成器将禁用。"
            self.logger.error(error_msg, exc_info=True)
            self._enabled = False

    def generate(self, data: torch.Tensor, **kwargs) -> torch.Tensor:
        """生成窗口统计特征

        Args:
            data: 输入数据张量 [n_samples, n_features]
            **kwargs: 其他参数，必须包含 'window_sizes' (List[int]) 和 'stats' (List[str])

        Returns:
            torch.Tensor: 生成的窗口统计特征张量 [n_samples, n_features * len(window_sizes) * len(stats)]

        Raises:
            ValueError: 如果未提供有效的 'window_sizes' 或 'stats' 参数或计算失败
        """
        # Roo-Fix: 强制要求通过 kwargs 提供 window_sizes 和 stats
        if 'window_sizes' not in kwargs:
            error_msg = "调用 generate 时必须通过 kwargs 提供 'window_sizes' 参数。"
            self.logger.error(error_msg) # 已有error日志
            raise ValueError(error_msg)
        if 'stats' not in kwargs:
             error_msg = "调用 generate 时必须通过 kwargs 提供 'stats' 参数。"
             self.logger.error(error_msg) # 已有error日志
             raise ValueError(error_msg)

        window_sizes_to_use = kwargs['window_sizes']
        stats_to_use = kwargs['stats']
        source = "kwargs" # 明确来源

        # 检查生成器是否启用 (基于全局配置)
        if not self.is_enabled:
            self.logger.info("窗口统计特征生成器(全局)已禁用，跳过。")
            return torch.empty((data.shape[0], 0), dtype=data.dtype, device=data.device)

        # --- 参数验证 ---
        if not isinstance(window_sizes_to_use, list) or not window_sizes_to_use:
            error_msg = f"无效的 window_sizes (来源: {source}): {window_sizes_to_use}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        valid_window_sizes = []
        for w in window_sizes_to_use:
             if not isinstance(w, int) or w <= 0:
                 error_msg = f"无效的窗口大小 (来源: {source}): {w}，必须为正整数"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)
             valid_window_sizes.append(w)
        window_sizes_to_use = valid_window_sizes
        if not window_sizes_to_use:
            error_msg = f"验证后 window_sizes 列表为空 (来源: {source})"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(stats_to_use, list) or not stats_to_use:
            error_msg = f"无效的 stats (来源: {source}): {stats_to_use}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        valid_stats = []
        for s in stats_to_use:
            if not isinstance(s, str) or not s:
                 error_msg = f"无效的统计量名称 (来源: {source}): '{s}'"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)
            valid_stats.append(s)
        stats_to_use = valid_stats
        if not stats_to_use:
             error_msg = f"验证后 stats 列表为空 (来源: {source})"
             self.logger.error(error_msg)
             raise ValueError(error_msg)
        # --- 参数验证结束 ---

        self.logger.info(f"开始生成窗口统计特征 (使用 {source} 的参数)")
        self.logger.info(f"参数详情 - 窗口大小: {window_sizes_to_use}, 统计量: {stats_to_use}")
        self.logger.debug(
            f"WindowFeatureGenerator - 输入数据统计:\n"
            f"- 形状: {data.shape}\n"
            f"- 数据类型: {data.dtype}\n"
            f"- 设备: {data.device}\n"
            f"- 均值(每列): {data.mean(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 标准差(每列): {data.std(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最小值(每列): {data.min(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最大值(每列): {data.max(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 是否包含NaN: {torch.isnan(data).any().item()}\n"
            f"- 是否包含Inf: {torch.isinf(data).any().item()}"
        )

        try:
            if torch.isinf(data).any():
                error_msg = "输入数据包含无穷大值，无法进行窗口统计特征计算。"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            n_samples, n_features = data.shape

            # 转换为 pandas DataFrame 以便使用 rolling 方法
            original_columns = [f"orig_feat_{i}" for i in range(n_features)]
            columns_index = pd.Index(original_columns)
            df = pd.DataFrame(data.cpu().numpy(), columns=columns_index)

            all_window_stats_dfs = []
            current_feature_names = [] # 使用局部变量

            for w in window_sizes_to_use:
                # Roo-Fix: 窗口大小不能大于样本数
                if w > n_samples:
                    error_msg = f"窗口大小 {w} (来源: {source}) 不能大于样本数 {n_samples}"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
                # 不再允许窗口大小等于样本数，因为这通常没有意义，除非特殊情况
                # if w == n_samples:
                #     self.logger.warning(f"窗口大小 {w} 等于样本数 {n_samples}，结果将是整个序列的统计量。")

                for stat_name in stats_to_use:
                    # Roo-Fix: 移除 try-except，让 pandas/numpy 错误直接抛出
                    # 计算滚动统计量，min_periods=1 确保从第一个有效点开始输出
                    # 使用 .agg(stat_name) 而不是直接调用方法，更通用
                    rolling_result_df = df[original_columns].rolling(window=w, min_periods=1).agg(stat_name)

                    # Roo-Fix: 移除空 DataFrame 检查，让后续 concat 处理或报错
                    # if rolling_result_df.empty:
                    #      self.logger.warning(f"计算滚动统计量 '{stat_name}' (窗口={w}) 返回空 DataFrame，跳过。")
                    #      continue

                    # 重命名列以反映窗口大小和统计量类型
                    new_columns = [f"{col}_win{w}_{stat_name}" for col in original_columns]
                    rolling_result_df.columns = new_columns
                    all_window_stats_dfs.append(rolling_result_df)
                    current_feature_names.extend(new_columns)
                    self.logger.debug(f"计算完成: 窗口={w}, 统计量='{stat_name}', 输出形状={rolling_result_df.shape}")


            if not all_window_stats_dfs:
                # Roo-Fix: 无法生成特征时抛出错误，而不是返回空张量
                error_msg = "未能生成任何有效的窗口统计特征。"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
                # self.logger.warning("未能生成任何有效的窗口统计特征。")
                # self._feature_count = 0
                # self.feature_names = []
                # return torch.empty((data.shape[0], 0), dtype=data.dtype, device=data.device)

            # 合并所有计算出的统计量 DataFrame
            combined_stats_df = pd.concat(all_window_stats_dfs, axis=1)

            # 转换回 PyTorch 张量，检查 Inf
            stats_values = combined_stats_df.values

            # Roo-Fix: 检查 Inf 值，如果存在则抛出错误，而不是替换为 NaN
            if np.isinf(stats_values).any():
                 error_msg = "窗口统计特征计算结果包含无穷大值 (Inf)。"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)

            # rolling 操作本身可能产生 NaN (例如在窗口未满时)，这是预期的
            # combined_stats_tensor = torch.from_numpy(stats_values.replace([np.inf, -np.inf], np.nan)).to(data.dtype).to(data.device) # 旧代码
            combined_stats_tensor = torch.from_numpy(stats_values).to(data.dtype).to(data.device)


            self._feature_count = combined_stats_tensor.shape[1]
            self.feature_names = current_feature_names # 更新实例变量

            self.logger.info(f"成功添加窗口统计特征 | 新增维度: {combined_stats_tensor.shape}")
            # 返回可能包含 NaN 的张量
            return combined_stats_tensor

        except Exception as e:
            error_msg = f"生成窗口统计特征失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

    def get_feature_names(self) -> list[str]:
        """获取生成的特征名称列表

        Returns:
            List[str]: 特征名称列表
        """
        return self.feature_names

    @property
    def feature_count(self) -> int:
        """获取生成的特征数量

        Returns:
            int: 特征数量
        """
        return self._feature_count

    @property
    def is_enabled(self) -> bool:
        """检查特征生成器是否启用

        Returns:
            bool: 是否启用
        """
        return self._enabled
