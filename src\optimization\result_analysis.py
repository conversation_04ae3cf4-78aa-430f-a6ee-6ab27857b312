"""
结果分析模块

本模块提供超参数优化结果的分析和报告功能，包括试验结果排序和筛选、
最佳参数组合报告生成等功能。

主要组件:
----------
1. analyze_trials: 分析试验结果，包括统计信息和排序
2. report_best_trials: 报告最佳参数组合
3. format_duration: 格式化持续时间

使用方式:
----------
```python
from src.optimization.result_analysis import analyze_trials, report_best_trials

# 分析试验结果
trial_stats, sorted_trials = analyze_trials(study)

# 报告最佳参数组合
report_best_trials(sorted_trials, top_n=5)
```
"""

import math
import time
from collections.abc import Sequence
from typing import Any

import optuna

from src.utils.logger import LoggerFactory

# 获取日志记录器
logger = LoggerFactory().get_logger("ResultAnalyzer")

# 日志分隔符
LOG_SEPARATOR_SMALL = "-" * 40
LOG_SEPARATOR_MEDIUM = "=" * 60
LOG_SEPARATOR_LARGE = "#" * 80


def analyze_trials(study: optuna.study.Study) -> tuple[dict[str, int], list[optuna.trial.FrozenTrial]]:
    """
    分析试验结果，包括统计信息和排序。

    Args:
        study: Optuna Study对象

    Returns:
        Tuple[Dict[str, int], List[optuna.trial.FrozenTrial]]: (试验统计信息, 排序后的有效试验)
    """
    # 统计试验结果
    all_trials = len(study.trials)
    completed_trials = [
        t for t in study.trials
        if t.state == optuna.trial.TrialState.COMPLETE and t.value is not None and math.isfinite(t.value)
    ]
    pruned_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED]
    failed_trials = [t for t in study.trials if t.state == optuna.trial.TrialState.FAIL]

    # 记录试验统计信息
    logger.info("试验统计:")
    logger.info(f"- 总试验数: {all_trials}")
    logger.info(f"- 完成试验: {len(completed_trials)}")
    logger.info(f"- 剪枝试验: {len(pruned_trials)}")
    logger.info(f"- 失败试验: {len(failed_trials)}")

    # 检查是否有有效的完成试验
    if not completed_trials:
        logger.warning("未找到有效的已完成试验。无法确定最佳参数。")
        return {
            'all': all_trials,
            'completed': 0,
            'pruned': len(pruned_trials),
            'failed': len(failed_trials)
        }, []

    # 按指标值排序试验
    sorted_trials = sorted(
        completed_trials,
        key=lambda t: t.value if (t.value is not None and math.isfinite(t.value)) else float('inf')
    )

    return {
        'all': all_trials,
        'completed': len(completed_trials),
        'pruned': len(pruned_trials),
        'failed': len(failed_trials)
    }, sorted_trials


def report_best_trials(sorted_trials: Sequence[optuna.trial.FrozenTrial], top_n: int = 5) -> None:
    """
    报告最佳参数组合。

    Args:
        sorted_trials: 排序后的有效试验
        top_n: 报告前N个最佳参数组合
    """
    if not sorted_trials:
        logger.warning("没有有效的试验结果可供报告。")
        return

    # 报告最佳参数
    logger.info(LOG_SEPARATOR_MEDIUM)
    logger.info(f"找到 {len(sorted_trials)} 个有效试验")
    logger.info(f"报告前 {min(top_n, len(sorted_trials))} 个最佳参数 (基于快速筛选):")
    logger.info(LOG_SEPARATOR_SMALL)

    # 详细报告每个最佳参数组合
    for i, trial in enumerate(sorted_trials[:top_n], 1):
        logger.info(f"【排名 {i}】")
        logger.info(f"- 试验编号: #{trial.number}")
        value_display = f"{trial.value:.6f}" if trial.value is not None else "N/A"
        logger.info(f"- 指标值 (Fast MAE): {value_display}")
        logger.info("- 参数组合:")
        for key, value in trial.params.items():
            logger.info(f"  • {key}: {value}")
        logger.info(LOG_SEPARATOR_SMALL)


def format_duration(seconds: float) -> str:
    """
    格式化持续时间。

    Args:
        seconds: 持续时间（秒）

    Returns:
        str: 格式化后的持续时间字符串
    """
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)

    if hours > 0:
        return f"{int(hours)}小时{int(minutes)}分钟{seconds:.2f}秒"
    elif minutes > 0:
        return f"{int(minutes)}分钟{seconds:.2f}秒"
    else:
        return f"{seconds:.2f}秒"


def report_optimization_summary(
    study: optuna.study.Study,
    start_time: float,
    phase_duration: float | None = None,
    phase_name: str | None = None
) -> None:
    """
    报告优化过程的总结信息。

    Args:
        study: Optuna Study对象
        start_time: 优化开始时间
        phase_duration: 阶段持续时间（可选）
        phase_name: 阶段名称（可选）
    """
    # 如果提供了阶段信息，报告阶段完成信息
    if phase_duration is not None and phase_name is not None:
        logger.info(LOG_SEPARATOR_MEDIUM)
        logger.info(f"=== {phase_name}完成 (耗时: {format_duration(phase_duration)}) ===")
        logger.info(LOG_SEPARATOR_SMALL)

    # 计算总运行时间
    total_duration = time.time() - start_time
    time_str = format_duration(total_duration)

    # 记录完成信息和建议
    logger.info(LOG_SEPARATOR_MEDIUM)
    logger.info(f"超参数优化完成，总耗时: {time_str}")
    logger.info("建议:")
    logger.info("1. 使用上述最佳参数配置，进行完整的训练/评估")
    logger.info("2. 在config.yaml中设置相应参数，然后运行main.py进行完整训练")
    logger.info("3. 对比不同参数组合的完整训练结果，选择最终方案")
    logger.info(LOG_SEPARATOR_LARGE)


def analyze_parameter_importance(study: optuna.study.Study) -> dict[str, float]:
    """
    分析参数重要性。

    Args:
        study: Optuna Study对象

    Returns:
        Dict[str, float]: 参数重要性字典
    """
    # 首先检查成功完成的试验数量
    completed_trials = [
        t for t in study.trials
        if t.state == optuna.trial.TrialState.COMPLETE and t.value is not None and math.isfinite(t.value)
    ]

    # 如果成功完成的试验数量不足，提供优雅的提示
    if len(completed_trials) < 2:
        logger.info(LOG_SEPARATOR_MEDIUM)
        logger.info("参数重要性分析:")
        logger.info(LOG_SEPARATOR_SMALL)
        logger.info("无法进行参数重要性分析 - 需要至少2个成功完成的试验")
        logger.info(f"当前成功完成的试验数量: {len(completed_trials)}")
        logger.info("建议:")
        logger.info("1. 增加试验次数 (--n-trials 参数)")
        logger.info("2. 检查试验失败或被剪枝的原因")
        logger.info("3. 调整参数空间，减少OOM风险")
        return {}

    try:
        # 尝试计算参数重要性
        importances = optuna.importance.get_param_importances(study)

        # 记录参数重要性
        logger.info(LOG_SEPARATOR_MEDIUM)
        logger.info("参数重要性分析:")
        logger.info(LOG_SEPARATOR_SMALL)

        for param_name, importance in importances.items():
            logger.info(f"- {param_name}: {importance:.4f}")

        return importances
    except Exception as e:
        # 提供更详细的错误信息和建议
        logger.info(LOG_SEPARATOR_MEDIUM)
        logger.info("参数重要性分析:")
        logger.info(LOG_SEPARATOR_SMALL)
        logger.info(f"计算参数重要性时遇到问题: {e}")
        logger.info("可能的原因:")
        logger.info("1. 试验数量不足或参数变化不够")
        logger.info("2. 所有试验使用了相同的参数值")
        logger.info("3. Optuna内部计算限制")
        logger.info("建议:")
        logger.info("1. 增加试验次数并确保参数有足够的变化")
        logger.info("2. 检查参数空间定义是否合理")
        return {}


def get_best_trial_config(trial: optuna.trial.FrozenTrial) -> dict[str, Any]:
    """
    获取最佳试验的配置。

    Args:
        trial: Optuna试验对象

    Returns:
        Dict[str, Any]: 最佳试验的配置
    """
    config = {}

    # 将参数组织成层次结构
    for key, value in trial.params.items():
        parts = key.split('.')
        current = config

        # 创建嵌套字典
        for _i, part in enumerate(parts[:-1]):
            if part not in current:
                current[part] = {}
            current = current[part]

        # 设置最终值
        current[parts[-1]] = value

    return config
