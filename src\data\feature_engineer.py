"""特征工程兼容层 - 提供对新特征工程系统的兼容支持

注意: 此模块已重构，所有功能已迁移到 src.data.feature_engineering 包中。
此文件仅作为兼容层保留，为了确保现有代码不会因为重构而失效。
新代码应直接使用 src.data.feature_engineering 包中的类和函数。

新特征工程系统结构:
1. 特征管理器:
   - src/data/feature_engineering/feature_manager.py: FeatureManager类，协调各个特征生成器

2. 特征生成器:
   - src/data/feature_engineering/base.py: BaseFeatureGenerator接口，所有特征生成器的基类
   - src/data/feature_engineering/time_features.py: TimeFeatureGenerator，生成时间特征
   - src/data/feature_engineering/diff_features.py: DiffFeatureGenerator，生成差分特征
   - src/data/feature_engineering/lag_features.py: LagFeatureGenerator，生成滞后特征
   - src/data/feature_engineering/window_features.py: WindowFeatureGenerator，生成窗口统计特征
   - src/data/feature_engineering/volatility_features.py: VolatilityFeatureGenerator，生成波动特征
   - src/data/feature_engineering/interaction_features.py: InteractionFeatureGenerator，生成特征交互项

新特征工程系统优势:
1. 模块化: 每个特征生成器只负责一种类型的特征生成，职责明确
2. 可扩展性: 添加新的特征生成器只需创建新的类，无需修改现有代码
3. 可测试性: 每个特征生成器可以独立测试，更容易编写单元测试
4. 可维护性: 代码结构清晰，每个文件大小适中，易于理解和维护

使用示例:
```python
from src.data.feature_engineering import FeatureManager
from src.utils.config.manager import ConfigManager

# 加载配置
config = ConfigManager.from_yaml('config.yaml')

# 创建特征管理器
feature_manager = FeatureManager(config)

# 处理数据
features_df = feature_manager.process(input_data)
```
"""


# 移除兼容层，直接抛出异常
def assert_tensor(data):
    """已移除的函数

    Args:
        data: 未使用的参数，仅为保持接口兼容性
    """
    # 使用参数以避免未使用警告
    _ = data
    raise NotImplementedError(
        "assert_tensor 函数已移除。请使用 src.data.feature_engineering 包中的功能。"
    )

# 移除兼容层，直接抛出异常
class FeatureEngineer:
    """已移除的类

    此类已完全移除，所有功能已迁移到 FeatureManager 类中。
    请直接使用 src.data.feature_engineering.FeatureManager。
    """

    def __init__(self, config, logger=None):
        """已移除的初始化方法

        Args:
            config: 未使用的参数，仅为保持接口兼容性
            logger: 未使用的参数，仅为保持接口兼容性
        """
        # 使用参数以避免未使用警告
        _ = config
        _ = logger
        raise NotImplementedError(
            "FeatureEngineer 类已移除。请使用 src.data.feature_engineering.FeatureManager 类。"
        )
