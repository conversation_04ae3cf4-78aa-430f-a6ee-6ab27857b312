"""单特征通道系统 - 每个特征对应一个独立通道

核心功能：
1. 单特征管理
2. 基本特征处理

注意：各通道特征保持独立，不进行合并
"""

from collections.abc import Callable

import torch

from src.utils.logger import get_logger


class FeatureChannel:
    """单特征通道类，管理单个特征的处理"""

    feature: torch.Tensor | None
    processed_feature: torch.Tensor | None

    def __init__(self,
                 channel_id: int,
                 feature_index: int,
                 name: str | None = None):
        """初始化特征通道

        Args:
            channel_id: 通道ID
            feature_index: 特征索引
            name: 可选通道名称
        """
        self.channel_id = channel_id
        self.feature_index = feature_index
        if name is None:
            self.logger = get_logger("FeatureChannel")
            error_msg = "通道名称必须提供，不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.name = name
        self.feature = None
        self.processed_feature = None
        self.logger = get_logger(f"FeatureChannel_{self.name}")

    def load_features(self, features: torch.Tensor):
        """加载特征数据到通道

        Args:
            features: 完整特征张量
        """
        if features.dim() == 2:  # [seq_len, feature_dim]
            self.feature = features[:, [self.feature_index]]
        elif features.dim() == 3:  # [batch_size, seq_len, feature_dim]
            self.feature = features[:, :, [self.feature_index]]
        else:
            raise ValueError(f"不支持的特征维度: {features.dim()}")

        assert isinstance(self.feature, torch.Tensor), "加载后的特征必须是Tensor类型"
        self.logger.debug(f"加载特征到通道 {self.name}")

    def process(self, processor_fn: Callable[[torch.Tensor], torch.Tensor] | None = None) -> "FeatureChannel":
        """处理通道内特征，确保processed_feature是Tensor类型

        Args:
            processor_fn: 可选的特征处理函数，必须返回Tensor类型

        Returns:
            FeatureChannel: 返回自身，其中processed_feature保证是torch.Tensor类型

        Raises:
            ValueError: 未加载特征或处理结果非Tensor类型时抛出

        Note:
            此方法保证返回的实例中processed_feature是torch.Tensor类型
        """
        if self.feature is None:
            raise ValueError(f"通道 {self.name} 未加载特征")

        if processor_fn is None:
            error_msg = "处理函数必须提供，不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        result = processor_fn(self.feature)
        if not isinstance(result, torch.Tensor):
            raise ValueError(f"处理函数必须返回Tensor类型，而不是{type(result)}")
        self.processed_feature = result

        if self.processed_feature is None:
            raise ValueError("处理后的特征为None，无法继续执行")

        self.logger.debug(f"通道 {self.name} 处理完成")
        assert isinstance(self.processed_feature, torch.Tensor), "处理后的特征必须是Tensor类型"
        return self


def create_feature_channels(feature_dim: int) -> list[FeatureChannel]:
    """为每个特征创建一个通道

    Args:
        feature_dim: 特征维度数

    Returns:
        list[FeatureChannel]: 特征通道列表
    """
    return [FeatureChannel(i, i) for i in range(feature_dim)]


def process_channels(channels: list[FeatureChannel],
                    processor_fn: Callable | None = None) -> list[FeatureChannel]:
    """批量处理特征通道

    Args:
        channels: 特征通道列表
        processor_fn: 可选的处理函数

    Returns:
        list[FeatureChannel]: 处理后的通道列表
    """
    logger = get_logger("process_channels")
    if processor_fn is None:
        error_msg = "处理函数必须提供，不能为None"
        logger.error(error_msg)
        raise ValueError(error_msg)
    return [channel.process(processor_fn) for channel in channels]
