"""CUDA 管理器主接口模块"""

from contextlib import contextmanager
from typing import Any

import torch

from src.utils.logger import get_logger

from .device import cuda_device_manager
from .memory import cuda_memory_manager
from .stream import cuda_stream_manager
from .types import GPUMemoryInfo


class CUDAManager:
    """CUDA资源统一管理器，作为其他模块使用CUDA功能的主要入口点"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """初始化CUDA管理器"""
        self._logger = get_logger(__name__)
        self._device_manager = cuda_device_manager
        self._memory_manager = cuda_memory_manager
        self._stream_manager = cuda_stream_manager

    def configure(self, cuda_config: dict[str, Any]) -> None:
        """配置CUDA设置

        Args:
            cuda_config: CUDA配置字典，包含内存和流的配置参数
        """
        if not self.is_cuda_available:
            error_msg = "CUDA不可用，无法进行配置"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

        try:
            # 内存配置
            if "memory_fraction" in cuda_config:
                fraction = float(cuda_config["memory_fraction"])
                if 0 < fraction <= 1:
                    torch.cuda.set_per_process_memory_fraction(fraction)
                    self._logger.info(f"设置GPU内存使用比例: {fraction*100:.0f}%")
                else:
                    raise ValueError(f"内存分配比例无效: {fraction}，应在(0,1]范围内")

            # cuDNN配置
            if "enable_memory_cache" in cuda_config:
                enable_cache = bool(cuda_config["enable_memory_cache"])
                torch.backends.cudnn.benchmark = enable_cache
                self._logger.info(f"{'启用' if enable_cache else '禁用'}cuDNN基准测试")

            if cuda_config.get("deterministic", False):
                torch.backends.cudnn.deterministic = True
                torch.backends.cudnn.benchmark = False
                self._logger.info("启用确定性计算")

            # 流配置
            if "streams" in cuda_config:
                self._stream_manager.configure(cuda_config["streams"])

            self._logger.info(
                f"CUDA配置完成:\n"
                f"- cuDNN版本: {torch.backends.cudnn.version()}\n"
                f"- 是否启用cuDNN: {torch.backends.cudnn.enabled}\n"
                f"- 是否启用基准测试: {torch.backends.cudnn.benchmark}\n"
                f"- 是否确定性计算: {torch.backends.cudnn.deterministic}"
            )

        except Exception as e:
            self._logger.error(f"CUDA配置失败: {e!s}")
            raise RuntimeError(f"CUDA配置失败: {e!s}")

    @property
    def device(self) -> torch.device:
        """获取当前CUDA设备"""
        return self._device_manager.device

    @property
    def is_cuda_available(self) -> bool:
        """检查CUDA是否可用"""
        return self._device_manager.is_cuda_available

    def get_device_info(self) -> dict[str, Any]:
        """获取设备详细信息"""
        return self._device_manager.get_device_info()

    def get_memory_info(self) -> GPUMemoryInfo | None:
        """获取GPU内存信息"""
        return self._memory_manager.get_memory_info()

    def get_memory_stats(self) -> dict[str, float]:
        """获取内存统计数据"""
        return self._memory_manager.get_memory_stats()

    def get_peak_memory_info(self) -> GPUMemoryInfo | None:
        """获取峰值内存信息"""
        return self._memory_manager.get_peak_memory_info()

    def reset_batch_peak_memory(self) -> None:
        """重置批次内峰值内存统计

        在每个批次开始前调用，重置批次内峰值内存记录
        """
        self._memory_manager.reset_batch_peak_memory()
        self._logger.debug("已重置批次内峰值内存统计")

    def get_batch_peak_memory_info(self) -> GPUMemoryInfo | None:
        """获取批次内峰值内存信息

        Returns:
            Optional[GPUMemoryInfo]: 批次内的峰值内存信息，如果未记录则返回None
        """
        return self._memory_manager.get_batch_peak_memory_info()

    def clear_cache(self):
        """清理CUDA缓存"""
        self._memory_manager.clear_cache()

    def reset(self):
        """重置CUDA峰值内存统计信息"""
        if not self.is_cuda_available:
            self._logger.warning("CUDA不可用，无法重置峰值内存统计")
            return

        try:
            torch.cuda.reset_peak_memory_stats()
            self._logger.info("已重置CUDA峰值内存统计信息")
        except Exception as e:
            self._logger.error(f"重置CUDA峰值内存统计信息失败: {e!s}")
            raise RuntimeError(f"重置CUDA峰值内存统计信息失败: {e!s}")

    def create_stream(self, name: str) -> Any:
        """创建CUDA流"""
        return self._stream_manager.create_stream(name)

    def get_current_stream(self) -> Any:
        """获取当前CUDA流"""
        return self._stream_manager.get_current_stream()

    def synchronize_stream(self, stream: Any) -> None:
        """同步指定的CUDA流"""
        self._stream_manager.synchronize_stream(stream)

    def release_stream(self, stream_or_name: str | Any) -> bool:
        """释放指定的流"""
        return self._stream_manager.release_stream(stream_or_name)

    def create_event(self, name: str, enable_timing: bool = False) -> Any:
        """创建CUDA事件"""
        return self._stream_manager.create_event(name, enable_timing)

    def get_stream_stats(self) -> dict[str, Any]:
        """获取流统计信息"""
        return self._stream_manager.get_stream_stats()

    @contextmanager
    def stream_context(self, name: str):
        """创建流上下文"""
        with self._stream_manager.stream_context(name) as stream:
            yield stream

    def move_to_device(self, tensor: torch.Tensor, non_blocking: bool = False, stream: Any | None = None) -> torch.Tensor:
        """移动张量到当前设备"""
        return self._device_manager.move_to_device(tensor, non_blocking, stream)

    def start_monitoring(self, interval: int = 5):
        """启动资源监控"""
        if not self.is_cuda_available:
            self._logger.warning("CUDA不可用，无法启动监控")
            return

        self._memory_manager.start_monitoring(interval)
        self._stream_manager.start_monitoring(interval)
        self._logger.info(f"已启动CUDA资源监控，间隔: {interval}秒")

    def start_peak_memory_monitoring(self, interval: int = 5, window_size: int = 60):
        """启动峰值内存监控"""
        if not self.is_cuda_available:
            self._logger.warning("CUDA不可用，无法启动峰值内存监控")
            return

        self._memory_manager.start_peak_memory_monitoring(interval, window_size)
        self._logger.info(f"已启动峰值内存监控，间隔: {interval}秒，窗口: {window_size}秒")

    def stop_monitoring(self):
        """停止资源监控"""
        self._memory_manager.stop_monitoring()
        self._stream_manager.stop_monitoring()
        self._logger.info("已停止CUDA资源监控")

    def stop_peak_memory_monitoring(self):
        """停止峰值内存监控"""
        self._memory_manager.stop_peak_memory_monitoring()
        self._logger.info("已停止峰值内存监控")

    def get_monitoring_summary(self) -> dict[str, Any]:
        """获取监控数据摘要"""
        memory_stats = self._memory_manager.get_memory_stats()
        stream_stats = self._stream_manager.get_stream_stats()
        device_info = self._device_manager.get_device_info()

        # 添加峰值内存信息（如果可用）
        peak_memory_info = self.get_peak_memory_info()
        peak_memory_dict = peak_memory_info.to_dict() if peak_memory_info else {'available': False}

        return {
            'device': device_info,
            'memory': memory_stats,
            'peak_memory': peak_memory_dict,
            'streams': stream_stats
        }

    def get_monitoring_data(self) -> dict[str, Any]:
        """获取完整监控数据历史记录"""
        return self._memory_manager.get_monitoring_data()


# 全局单例
try:
    cuda_manager = CUDAManager()
except Exception as e:
    logger = get_logger("CUDAManager")
    logger.error(f"CUDA管理器初始化失败: {e!s}")
    raise RuntimeError(f"CUDA管理器初始化失败: {e!s}")
