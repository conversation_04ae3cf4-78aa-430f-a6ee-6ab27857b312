"""数据验证器 - 负责在数据加载后立即进行基本验证

执行时机：
- 在数据加载后立即执行
- 在数据清洗和特征工程之前执行
- 作为数据处理流水线的第二步（加载→验证→清洗→...）

功能：
1. 检查必要字段存在性（日期列、目标列等）
2. 验证数据类型正确性（数值列、日期列等）
3. 确认数据范围合理性（检查无效值、无穷值等）
4. 检查数据完整性（主键唯一性等）
5. 检查数据一致性（时间序列顺序等）
6. 检查无法修正的价格跳变

处理原则：
- 任何一项验证不通过就停止执行，抛出异常
- 不尝试修复问题，而是明确报告问题
- 所有验证参数必须从配置中明确指定，不使用默认值

与其他模块的区别：
- 与DataCleaner的区别：本模块只检测问题，不修复问题
- 与DataQualityChecker的区别：本模块在数据加载后立即执行，而DataQualityChecker在数据标准化后执行

相关模块：
- src/data/data_pipeline.py: 调用本模块进行数据验证
- src/data/preprocessing/data_cleaner.py: 负责数据清洗
- src/data/data_quality_checker.py: 负责标准化后的数据质量检查
"""

import time
from typing import Any

import numpy as np
import pandas as pd
import torch

from src.utils.logger import get_logger


class DataValidationError(Exception):
    """数据验证异常基类"""
    pass

class DataValidator:
    """数据验证器 - 负责无法修正的数据缺陷检测

    职责：
    1. 数据完整性验证（必要字段存在性）
    2. 数据类型验证（类型正确性）
    3. 数据范围验证（无效值、无穷值等）
    4. 数据一致性验证（时间序列顺序等）
    5. 无法修正的价格跳变检测

    处理原则：
    - 任何一项验证不通过就停止执行，抛出异常
    - 不尝试修复问题，而是明确报告问题
    """

    def __init__(self, config: dict[str, Any] | Any):
        """初始化验证器

        Args:
            config: 配置参数，包含验证规则，可以是字典或ConfigManager对象
        """
        self.logger = get_logger(__name__)
        self.config = config
        # 初始化必要字段列表，始终包含日期列
        self.required_fields = ['date']  # 日期列始终是必要字段

        # 尝试从配置中获取目标列
        target_col = None
        try:
            # 如果是对象类型配置（使用hasattr避免类型检查问题）
            if not isinstance(config, dict) and hasattr(config, 'data'):
                config_data = config.data
                if hasattr(config_data, 'target'):
                    target_col = config_data.target
            # 如果是字典类型配置
            elif isinstance(config, dict):
                if 'data' in config and 'target' in config['data']:
                    target_col = config['data']['target']
        except Exception as e:
            self.logger.warning(f"从配置中获取目标列失败: {e!s}")

        # 如果成功获取到目标列，添加到必要字段
        if target_col:
            self.required_fields.append(target_col)
            self.logger.debug(f"已将目标列 '{target_col}' 添加到必要字段列表")
        self.price_jump_config = {}

        # 记录初始化开始
        self.logger.debug(f"初始化数据验证器，配置类型: {type(config)}")

        self._load_price_jump_config()

        try:
            # 设置基本必要字段
            self.required_fields = ['date']  # 始终需要日期列
            self.logger.debug(f"设置基本必要字段: {self.required_fields}")

            # 获取目标列名（如果存在）
            target_col = None
            if isinstance(config, dict):
                if 'data' in config and isinstance(config['data'], dict) and 'target' in config['data']:
                    target_col = config['data']['target']
                    self.logger.debug(f"从字典配置中获取目标列: {target_col}")
                    if target_col and target_col not in self.required_fields:
                        self.required_fields.append(target_col)
            elif hasattr(config, 'data') and hasattr(config.data, 'target'):
                target_col = config.data.target
                self.logger.debug(f"从对象配置中获取目标列: {target_col}")
                if target_col and target_col not in self.required_fields:
                    self.required_fields.append(target_col)

            self.logger.info(f"数据验证器已配置，基本必要字段: {self.required_fields}")
            self.logger.debug("注意：实际验证时将动态检测所有数值列，不仅限于配置中指定的列")

        except Exception as e:
            error_msg = f"配置数据验证器时出现错误: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

    def validate(self, data: pd.DataFrame | tuple[torch.Tensor, torch.Tensor] | None) -> None:
        """执行数据验证，任何验证失败都会抛出异常

        Args:
            data: 待验证的数据，可以是DataFrame或元组(features, targets)，None将被视为无效输入

        Raises:
            DataValidationError: 当任何验证项不通过时抛出
        """
        self.logger.debug(f"开始数据验证，输入数据类型: {type(data)}")

        # 处理元组输入
        if isinstance(data, tuple) and len(data) == 2:
            self.logger.info("检测到元组输入，将使用张量验证方式")
            self.logger.debug(f"张量特征形状: {data[0].shape if hasattr(data[0], 'shape') else '未知'}, 张量目标形状: {data[1].shape if hasattr(data[1], 'shape') else '未知'}")
            self._validate_tensor(data[0], data[1])
            return

        # 输入验证
        if not isinstance(data, pd.DataFrame):
            error_msg = f"输入数据无效: 预期为 pandas DataFrame或元组，但收到 {type(data)}"
            self.logger.error(error_msg)
            raise DataValidationError(error_msg)

        start_time = time.time()

        # 使用已提取的必要字段
        required_fields = self.required_fields
        self.logger.debug(f"必要字段列表: {required_fields}")

        # 获取目标列名
        target_col = None
        if isinstance(self.config, dict):
            if 'data' in self.config and isinstance(self.config['data'], dict) and 'target' in self.config['data']:
                target_col = self.config['data']['target']
                self.logger.debug(f"从字典配置中获取目标列: {target_col}")
        elif hasattr(self.config, 'data') and hasattr(self.config.data, 'target'):
            target_col = self.config.data.target
            self.logger.debug(f"从对象配置中获取目标列: {target_col}")

        # 记录数据基本信息
        self.logger.debug(f"数据列名: {list(data.columns)}")
        self.logger.debug(f"数据类型: {data.dtypes.to_dict()}")
        self.logger.debug(f"数据统计信息:\n{data.describe().to_string()}")

        # 获取所有列名，并分类显示
        all_columns = list(data.columns)
        date_columns = [col for col in all_columns if col == 'date']
        other_columns = [col for col in all_columns if col not in ('date', target_col)]

        # 获取数值列，但确保日期列不被包含在内
        numeric_columns = data.select_dtypes(include=['number']).columns.tolist()
        if 'date' in numeric_columns:
            numeric_columns.remove('date')

        # 确保日期列被归类为非数值列
        non_numeric_columns = [col for col in all_columns if col not in numeric_columns]
        if 'date' in all_columns and 'date' not in non_numeric_columns:
            non_numeric_columns.append('date')

        self.logger.info(
            f"\n=== 数据验证开始 ===\n"
            f"- 数据形状: {data.shape}\n"
            f"- 样本数: {len(data):,}\n"
            f"- 特征数: {len(data.columns)}\n"
            f"- 日期列: {date_columns}\n"
            f"- 目标列: {target_col}\n"
            f"- 其他列: {other_columns}\n"
            f"- 数值列: {numeric_columns}\n"
            f"- 非数值列: {non_numeric_columns}\n"
            f"- 必要字段: {required_fields}"
        )

        try:
            # 1. 检查必要字段
            self.logger.debug("开始检查必要字段")
            missing_fields = [field for field in required_fields if field not in data.columns]
            if missing_fields:
                error_msg = f"缺少必要字段: {missing_fields}"
                self.logger.error(error_msg)
                self.logger.debug(f"现有字段: {list(data.columns)}")
                raise DataValidationError(error_msg)
            self.logger.debug("必要字段检查通过")

            # 1.1 动态检测所有数值列，确保日期列不被包含在内
            date_col = 'date'
            # 先检查日期列是否为日期类型，如果不是，尝试转换
            if date_col in data.columns and not pd.api.types.is_datetime64_any_dtype(data[date_col]):
                try:
                    # 尝试转换为日期类型（仅用于检查，不修改原始数据）
                    pd.to_datetime(data[date_col])
                    self.logger.info(f"列 '{date_col}' 可以转换为日期类型，但当前不是日期类型")
                except Exception as e:
                    self.logger.warning(f"列 '{date_col}' 无法转换为日期类型: {e!s}")

            # 获取所有数值列
            numeric_cols = data.select_dtypes(include=['number']).columns.tolist()

            # 如果日期列被错误地识别为数值列，将其排除
            if date_col in numeric_cols:
                numeric_cols.remove(date_col)
                self.logger.info(f"将日期列 '{date_col}' 从数值列列表中排除，即使它当前是数值类型")

            self.logger.debug(f"动态检测到的数值列: {numeric_cols}")

            # 确保至少有一个数值列
            if not numeric_cols:
                error_msg = "数据中没有数值列，无法进行验证"
                self.logger.error(error_msg)
                raise DataValidationError(error_msg)

            # 将所有数值列添加到必要字段中（如果尚未包含）
            for col in numeric_cols:
                if col not in required_fields:
                    required_fields.append(col)

            self.logger.debug(f"更新后的必要字段（包含所有数值列）: {required_fields}")

            # 2. 检查数据类型
            self.logger.debug("开始检查数据类型")
            type_start = time.time()
            type_checks = self._check_data_types(data)
            if not type_checks['is_valid']:
                error_msg = f"数据类型验证失败: {type_checks['errors']}"
                self.logger.error(
                    f"\n[类型检查失败]\n"
                    f"- 错误数: {len(type_checks['errors'])}\n"
                    f"- 示例错误: {type_checks['errors'][:3]}\n"
                    f"- 耗时: {time.time()-type_start:.2f}s\n"
                    f"- 总耗时: {time.time()-start_time:.2f}s"
                )
                raise DataValidationError(error_msg)

            self.logger.info(
                f"\n[类型检查通过]\n"
                f"- 检查字段数: {len(data.columns)}\n"
                f"- 耗时: {time.time()-type_start:.2f}s"
            )

            # 3. 检查数据范围
            self.logger.debug("开始检查数据范围")
            range_start = time.time()
            range_checks = self._check_data_ranges(data)
            if not range_checks['is_valid']:
                error_msg = f"数据范围验证失败: {range_checks['errors']}"
                self.logger.error(error_msg)
                self.logger.debug(
                    f"\n[范围检查失败]\n"
                    f"- 错误数: {len(range_checks['errors'])}\n"
                    f"- 示例错误: {range_checks['errors'][:3]}\n"
                    f"- 耗时: {time.time()-range_start:.2f}s\n"
                    f"- 总耗时: {time.time()-start_time:.2f}s"
                )
                raise DataValidationError(error_msg)
            self.logger.debug(
                f"\n[范围检查通过]\n"
                f"- 检查字段数: {len(data.select_dtypes(include=['number']).columns)}\n"
                f"- 耗时: {time.time()-range_start:.2f}s"
            )

            # 4. 检查价格跳变
            self.logger.debug("开始检查价格跳变")
            # 确保price_jump_config中的enable字段存在
            if 'enable' not in self.price_jump_config:
                error_msg = "价格跳变检测配置中缺少enable字段，必须明确指定是否启用价格跳变检测"
                self.logger.error(error_msg)
                raise DataValidationError(error_msg)

            if self.price_jump_config['enable']:
                self.logger.debug("价格跳变检测已启用，开始检测")
                jump_start = time.time()
                jump_checks = self._check_price_jumps(data)
                if not jump_checks['is_valid']:
                    # 获取详细的错误信息
                    invalid_columns = [col for col, result in jump_checks['column_results'].items() if not result['is_valid']]
                    error_msg = f"检测到异常价格跳变: {len(invalid_columns)} 个列存在过多跳变点"
                    self.logger.error(error_msg)
                    self.logger.debug(
                        f"\n[价格跳变检测失败]\n"
                        f"- 问题列数量: {len(invalid_columns)}\n"
                        f"- 问题列: {invalid_columns[:3]}{' 等' if len(invalid_columns) > 3 else ''}\n"
                        f"- 错误数: {len(jump_checks['errors'])}\n"
                        f"- 示例错误: {jump_checks['errors'][:3]}\n"
                        f"- 耗时: {time.time()-jump_start:.2f}s\n"
                        f"- 总耗时: {time.time()-start_time:.2f}s"
                    )
                    raise DataValidationError(error_msg)
                self.logger.debug(
                    f"\n[价格跳变检测通过]\n"
                    f"- 检测到的跳变点: {len(jump_checks['jumps'])}\n"
                    f"- 检查列数: {len(jump_checks['column_results'])}\n"
                    f"- 耗时: {time.time()-jump_start:.2f}s"
                )
            else:
                self.logger.debug("价格跳变检测已禁用，跳过检测")

            # 所有验证通过
            validation_time = time.time() - start_time
            self.logger.info(f"数据验证全部通过，总耗时: {validation_time:.2f}s")
            self.logger.debug(f"数据验证详细信息:\n- 数据形状: {data.shape}\n- 数值列数量: {len(data.select_dtypes(include=['number']).columns)}\n- 非数值列数量: {len(data.select_dtypes(exclude=['number']).columns)}")

        except DataValidationError:
            # 直接重新抛出DataValidationError异常
            self.logger.debug("数据验证失败，抛出DataValidationError异常")
            raise
        except Exception as e:
            # 将其他异常包装为DataValidationError
            error_msg = f"数据验证过程中发生异常: {e!s}"
            self.logger.error(error_msg, exc_info=True)
            self.logger.debug(f"数据验证过程中发生未预期的异常: {e!s}", exc_info=True)
            raise DataValidationError(error_msg) from e

    def _check_data_types(self, data: pd.DataFrame) -> dict[str, Any]:
        """检查数据类型

        检查数据框中的列是否符合预期的数据类型

        Args:
            data: 待检查的数据框

        Returns:
            Dict[str, Any]: 检查结果
        """
        self.logger.debug("开始检查数据类型")
        errors = []

        # 检查空数据框
        if data.empty:
            error_msg = "数据框为空"
            self.logger.error(error_msg)
            errors.append(error_msg)
            return {
                'is_valid': False,
                'errors': errors
            }

        # 检查数值列
        numeric_cols = data.select_dtypes(include=['number']).columns.tolist()
        self.logger.debug(f"数值列: {numeric_cols}")

        if len(numeric_cols) == 0:
            error_msg = "数据框中没有数值列"
            self.logger.error(error_msg)
            errors.append(error_msg)

        # 检查非数值列
        non_numeric_cols = [col for col in data.columns if col not in numeric_cols]

        # 确保日期列被归类为非数值列
        date_col = 'date'
        if date_col in data.columns and date_col not in non_numeric_cols:
            non_numeric_cols.append(date_col)
            self.logger.info(f"将日期列 '{date_col}' 强制归类为非数值列，即使它当前是数值类型")

        self.logger.debug(f"非数值列: {non_numeric_cols}")

        # 特别检查日期列
        if date_col in data.columns:
            # 检查日期列是否已经是日期类型
            if pd.api.types.is_datetime64_any_dtype(data[date_col]):
                self.logger.info(f"日期列 '{date_col}' 已经是日期类型")
            else:
                # 验证日期列是否可以转换为日期类型
                try:
                    pd.to_datetime(data[date_col])
                    self.logger.info(f"日期列 '{date_col}' 验证通过，可以正确转换为日期类型")
                except Exception as e:
                    error_msg = f"日期列 '{date_col}' 无法转换为日期类型: {e!s}"
                    self.logger.error(error_msg)
                    errors.append(error_msg)
        else:
            error_msg = f"缺少必要的日期列 '{date_col}'"
            self.logger.error(error_msg)
            errors.append(error_msg)

        # 检查是否有非数值列但可以转换为数值列的情况（除了日期列）
        for col in non_numeric_cols:
            if col != date_col and not pd.api.types.is_datetime64_any_dtype(data[col]):
                try:
                    pd.to_numeric(data[col])
                    error_msg = f"列 {col} 可以转换为数值类型，但当前不是数值类型"
                    self.logger.warning(error_msg)
                    errors.append(error_msg)
                except Exception as e:
                    self.logger.debug(f"列 {col} 不能转换为数值类型，保持原类型: {e}")

        # 检查数值列的混合类型
        for col in numeric_cols:
            # 检查是否有混合类型
            non_numeric_mask = data[col].apply(lambda x: not isinstance(x, int | float | np.integer | np.floating))
            if non_numeric_mask.any():
                non_numeric_count = non_numeric_mask.sum()
                non_numeric_examples = data.loc[non_numeric_mask, col].head(3).tolist()
                error_msg = f"列 {col} 包含 {non_numeric_count} 个混合类型数据，示例: {non_numeric_examples}"
                self.logger.warning(error_msg)
                errors.append(error_msg)
            else:
                self.logger.debug(f"列 {col} 类型检查通过")

        result = {
            'is_valid': len(errors) == 0,
            'errors': errors
        }
        self.logger.debug(f"数据类型检查结果: {'通过' if result['is_valid'] else '失败'}, 错误数: {len(errors)}")
        return result

    def _load_price_jump_config(self) -> None:
        """加载价格跳变检测配置

        注意：当前版本强制禁用价格跳变检测，无论配置如何设置
        """
        self.logger.debug("开始加载价格跳变检测配置")
        try:
            # 强制禁用价格跳变检测，但提供完整的配置结构
            self.price_jump_config = {
                'enable': False,
                'window': {
                    'forward_size': 5,
                    'backward_size': 5
                },
                'thresholds': {
                    'price_change': 0.1,
                    'volatility_ratio': 2.0,
                    'trend_angle': 30.0
                },
                'validation': {
                    'min_percent_change': 0.06
                }
            }
            self.logger.info("价格跳变检测已被强制禁用，验证器将不会检查价格跳变")

            # 以下代码仅用于记录原始配置，但不会使用
            if isinstance(self.config, dict):
                self.logger.debug("从字典类型配置加载价格跳变检测配置（仅记录，不使用）")
                # 尝试多种可能的配置路径
                preprocessing_config = self.config.get('data', {}).get('preprocessing', {})
                self.logger.debug(f"从data.preprocessing路径获取配置: {preprocessing_config}")

                # 尝试多种可能的配置路径

                # 1. 首先尝试直接从preprocessing中获取price_jump_detection
                price_jump_config = preprocessing_config.get('price_jump_detection', {})
                self.logger.debug(f"从data.preprocessing.price_jump_detection路径获取配置: {price_jump_config}")

                # 2. 如果没有找到，尝试从validation中获取
                if not price_jump_config:
                    price_jump_config = preprocessing_config.get('validation', {}).get('price_jump_detection', {})
                    self.logger.debug(f"从data.preprocessing.validation.price_jump_detection路径获取配置: {price_jump_config}")

                # 3. 如果还没有找到，尝试直接从preprocessing中获取
                if not price_jump_config:
                    self.logger.debug("尝试从preprocessing.validation.price_jump_detection路径获取配置")
                    price_jump_config = self.config.get('preprocessing', {}).get('validation', {}).get('price_jump_detection', {})
                    self.logger.debug(f"从preprocessing.validation.price_jump_detection路径获取配置: {price_jump_config}")

                # 4. 最后尝试从preprocessing.price_jump_detection获取
                if not price_jump_config:
                    self.logger.debug("尝试从preprocessing.price_jump_detection路径获取配置")
                    price_jump_config = self.config.get('preprocessing', {}).get('price_jump_detection', {})
                    self.logger.debug(f"从preprocessing.price_jump_detection路径获取配置: {price_jump_config}")

                if price_jump_config and price_jump_config.get('enable', False):
                    self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")
            else:
                self.logger.debug("从对象类型配置加载价格跳变检测配置（仅记录，不使用）")
                # 对象类型配置 - 尝试多种可能的路径

                # 1. 首先尝试从data.preprocessing.price_jump_detection获取
                if hasattr(self.config, 'data'):
                    self.logger.debug("尝试从config.data路径获取配置")

                    # 检查preprocessing是对象还是字典
                    if hasattr(self.config.data, 'preprocessing'):
                        preprocessing = self.config.data.preprocessing
                        self.logger.debug(f"preprocessing类型: {type(preprocessing)}")

                        # 如果preprocessing是字典
                        if isinstance(preprocessing, dict):
                            # 1.1 直接从preprocessing字典获取price_jump_detection
                            if 'price_jump_detection' in preprocessing:
                                self.logger.debug("从config.data.preprocessing['price_jump_detection']路径获取配置")
                                original_config = preprocessing['price_jump_detection']
                                if isinstance(original_config, dict) and original_config.get('enable', False):
                                    self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")
                            # 1.2 从validation中获取price_jump_detection
                            elif 'validation' in preprocessing and isinstance(preprocessing['validation'], dict) and 'price_jump_detection' in preprocessing['validation']:
                                self.logger.debug("从config.data.preprocessing['validation']['price_jump_detection']路径获取配置")
                                original_config = preprocessing['validation']['price_jump_detection']
                                if isinstance(original_config, dict) and original_config.get('enable', False):
                                    self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")

                        # 如果preprocessing是对象
                        # 1.3 直接从preprocessing对象获取price_jump_detection
                        elif hasattr(preprocessing, 'price_jump_detection'):
                            self.logger.debug("从config.data.preprocessing.price_jump_detection路径获取配置")
                            pjd = preprocessing.price_jump_detection
                            if hasattr(pjd, 'enable') and pjd.enable:
                                self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")
                        # 1.4 从validation中获取price_jump_detection
                        elif hasattr(preprocessing, 'validation'):
                            validation = preprocessing.validation
                            if hasattr(validation, 'price_jump_detection'):
                                self.logger.debug("从config.data.preprocessing.validation.price_jump_detection路径获取配置")
                                pjd = validation.price_jump_detection
                                if hasattr(pjd, 'enable') and pjd.enable:
                                    self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")

                # 2. 尝试直接从preprocessing属性获取
                if hasattr(self.config, 'preprocessing'):
                    self.logger.debug("尝试从config.preprocessing路径获取配置")
                    preprocessing = self.config.preprocessing

                    # 如果preprocessing是字典
                    if isinstance(preprocessing, dict):
                        # 2.1 直接从preprocessing字典获取price_jump_detection
                        if 'price_jump_detection' in preprocessing:
                            self.logger.debug("从config.preprocessing['price_jump_detection']路径获取配置")
                            original_config = preprocessing['price_jump_detection']
                            if isinstance(original_config, dict) and original_config.get('enable', False):
                                self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")
                        # 2.2 从validation中获取price_jump_detection
                        elif 'validation' in preprocessing and isinstance(preprocessing['validation'], dict) and 'price_jump_detection' in preprocessing['validation']:
                            self.logger.debug("从config.preprocessing['validation']['price_jump_detection']路径获取配置")
                            original_config = preprocessing['validation']['price_jump_detection']
                            if isinstance(original_config, dict) and original_config.get('enable', False):
                                self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")

                    # 如果preprocessing是对象
                    # 2.3 直接从preprocessing对象获取price_jump_detection
                    elif hasattr(preprocessing, 'price_jump_detection'):
                        self.logger.debug("从config.preprocessing.price_jump_detection路径获取配置")
                        pjd = preprocessing.price_jump_detection
                        if hasattr(pjd, 'enable') and pjd.enable:
                            self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")
                    # 2.4 从validation中获取price_jump_detection
                    elif hasattr(preprocessing, 'validation'):
                        validation = preprocessing.validation
                        if hasattr(validation, 'price_jump_detection'):
                            self.logger.debug("从config.preprocessing.validation.price_jump_detection路径获取配置")
                            pjd = validation.price_jump_detection
                            if hasattr(pjd, 'enable') and pjd.enable:
                                self.logger.info("注意：配置文件中启用了价格跳变检测，但验证器已被修改为忽略此设置")

            # 由于我们已经提供了完整的配置结构，只需记录配置信息
            self.logger.debug(f"价格跳变检测配置内容: {self.price_jump_config}")

            # 记录配置详情
            self.logger.info("价格跳变检测配置已加载并强制禁用")
            self.logger.debug(f"价格跳变检测配置详情:\n- enable: {self.price_jump_config.get('enable')}\n- window: {self.price_jump_config.get('window')}\n- thresholds: {self.price_jump_config.get('thresholds')}\n- validation: {self.price_jump_config.get('validation')}")

        except Exception as e:
            # 由于我们已经提供了默认配置，这里不应该出现异常
            # 但如果出现了，记录日志并继续使用默认配置
            self.logger.error(f"加载价格跳变检测配置时出现异常: {e!s}")
            self.logger.debug("异常详情:", exc_info=True)
            self.logger.warning("使用默认配置并继续执行")

    def _check_data_ranges(self, data: pd.DataFrame) -> dict[str, Any]:
        """检查数据范围

        检查数据框中的值是否在合理范围内

        Args:
            data: 待检查的数据框

        Returns:
            Dict[str, Any]: 检查结果
        """
        self.logger.debug("开始检查数据范围")
        errors = []

        # 检查空数据框
        if data.empty:
            error_msg = "数据框为空"
            self.logger.error(error_msg)
            errors.append(error_msg)
            return {
                'is_valid': False,
                'errors': errors
            }

        # 只检查数值列，明确排除日期列
        date_col = 'date'
        numeric_cols = data.select_dtypes(include=['number']).columns.tolist()
        # 如果日期列被错误地识别为数值列，将其排除
        if date_col in numeric_cols:
            numeric_cols.remove(date_col)
            self.logger.info(f"将日期列 '{date_col}' 从数值范围检查中排除")
        self.logger.debug(f"将检查 {len(numeric_cols)} 个数值列的数据范围")

        # 如果没有数值列，返回错误
        if not numeric_cols:
            error_msg = "数据框中没有数值列"
            self.logger.error(error_msg)
            errors.append(error_msg)
            return {
                'is_valid': False,
                'errors': errors
            }

        # 记录数值列的基本统计信息
        stats_summary = {}
        for col in numeric_cols:
            stats = {
                'min': data[col].min(),
                'max': data[col].max(),
                'mean': data[col].mean(),
                'median': data[col].median(),
                'std': data[col].std(),
                'na_count': data[col].isna().sum()
            }
            stats_summary[col] = stats
            self.logger.debug(f"列 {col} 统计信息: {stats}")

        for col in numeric_cols:
            # 检查是否有空值
            na_mask = data[col].isna()
            na_count = na_mask.sum()
            if na_count > 0:
                error_msg = f"列 {col} 包含 {na_count} 个空值"
                self.logger.warning(error_msg)
                errors.append(error_msg)
                if na_count < 10:  # 只显示少量空值的索引
                    na_indices = data[na_mask].index.tolist()
                    self.logger.debug(f"列 {col} 空值索引: {na_indices}")

            # 检查是否有无穷值
            inf_mask = data[col].isin([float('inf'), float('-inf')])
            inf_count = inf_mask.sum()
            if inf_count > 0:
                error_msg = f"列 {col} 包含 {inf_count} 个无穷值"
                self.logger.warning(error_msg)
                errors.append(error_msg)
                if inf_count < 10:  # 只显示少量无穷值的索引
                    inf_indices = data[inf_mask].index.tolist()
                    self.logger.debug(f"列 {col} 无穷值索引: {inf_indices}")

            # 检查是否有过大的值
            mean = data[col].mean()
            std = data[col].std()
            if std > 0:  # 避免除零
                extreme_values = (data[col] > mean + 10 * std) | (data[col] < mean - 10 * std)
                extreme_count = extreme_values.sum()
                if extreme_count > 0:
                    error_msg = f"列 {col} 包含 {extreme_count} 个可能的极端值（超过10倍标准差）"
                    self.logger.warning(error_msg)
                    errors.append(error_msg)

                    # 记录极端值的详细信息
                    if extreme_count < 10:  # 只显示少量极端值的详细信息
                        extreme_indices = data[extreme_values].index.tolist()
                        extreme_values_list = data.loc[extreme_values, col].tolist()
                        self.logger.debug(f"列 {col} 极端值: {list(zip(extreme_indices, extreme_values_list, strict=False))}")

                    # 计算极端值的统计信息
                    extreme_min = data.loc[extreme_values, col].min()
                    extreme_max = data.loc[extreme_values, col].max()
                    extreme_mean = data.loc[extreme_values, col].mean()
                    self.logger.debug(f"列 {col} 极端值统计: 最小值={extreme_min}, 最大值={extreme_max}, 平均值={extreme_mean}")
            else:
                self.logger.debug(f"列 {col} 标准差为0，跳过极端值检查")

        result = {
            'is_valid': len(errors) == 0,
            'errors': errors
        }
        self.logger.debug(f"数据范围检查结果: {'通过' if result['is_valid'] else '失败'}, 错误数: {len(errors)}")
        return result

    def _check_price_jumps(self, data: pd.DataFrame) -> dict[str, Any]:
        """检测价格跳变点

        使用DataCleaner的跳变检测逻辑，但不修改数据，只返回检测结果。
        对每列单独进行判断，使用基于序列长度的百分比阈值。

        Args:
            data: 待检查的数据框

        Returns:
            Dict[str, Any]: 检测结果，包含:
                - is_valid: 是否通过检测
                - jumps: 检测到的跳变点信息
                - column_results: 每列的检测结果
                - errors: 检测过程中的错误信息
        """
        # 确保price_jump_config中的enable字段存在
        if 'enable' not in self.price_jump_config:
            error_msg = "价格跳变检测配置中缺少enable字段，必须明确指定是否启用价格跳变检测"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not self.price_jump_config['enable']:
            return {'is_valid': True, 'jumps': [], 'column_results': {}, 'errors': []}

        errors = []
        jumps = []
        column_results = {}

        try:
            # 导入DataCleaner
            from src.data.preprocessing.data_cleaner import DataCleaner

            # 准备配置
            # 确保price_jump_config是字典类型
            config_dict = self.price_jump_config if isinstance(self.price_jump_config, dict) else {}
            window_dict = config_dict.get('window', {}) if isinstance(config_dict, dict) else {}
            thresholds_dict = config_dict.get('thresholds', {}) if isinstance(config_dict, dict) else {}
            validation_dict = config_dict.get('validation', {}) if isinstance(config_dict, dict) else {}

            # 验证必要的配置字段
            if not isinstance(window_dict, dict) or not isinstance(thresholds_dict, dict):
                raise ValueError("价格跳变检测配置格式不正确，window和thresholds必须是字典类型")

            # 获取配置值，不使用默认值
            forward_size = window_dict.get('forward_size')
            backward_size = window_dict.get('backward_size')
            price_change = thresholds_dict.get('price_change')
            volatility_ratio = thresholds_dict.get('volatility_ratio')
            trend_angle = thresholds_dict.get('trend_angle')

            # 验证所有必要的配置值都存在
            missing_values = []
            if forward_size is None:
                missing_values.append('window.forward_size')
            if backward_size is None:
                missing_values.append('window.backward_size')
            if price_change is None:
                missing_values.append('thresholds.price_change')
            if volatility_ratio is None:
                missing_values.append('thresholds.volatility_ratio')
            if trend_angle is None:
                missing_values.append('thresholds.trend_angle')

            if missing_values:
                raise ValueError(f"价格跳变检测配置缺少必要的值: {', '.join(missing_values)}")

            # 创建DataCleaner配置
            cleaner_config = {
                'preprocessing': {
                    'cleaning': {
                        'pre_window_size': forward_size,
                        'post_window_size': backward_size,
                        'level_threshold': price_change,
                        'volatility_threshold': volatility_ratio,
                        'trend_angle_threshold': trend_angle,
                        'outlier_threshold': 3.0,
                        'missing_value_strategy': 'median'
                    }
                }
            }

            self.logger.info(f"使用以下配置进行价格跳变检测: {cleaner_config['preprocessing']['cleaning']}")

            # 获取所有数值列作为价格列，排除日期列
            numeric_cols = data.select_dtypes(include=['number']).columns
            date_col = 'date'
            # 如果日期列被错误地识别为数值列，将其排除
            if date_col in numeric_cols:
                numeric_cols = numeric_cols.drop(date_col)
                self.logger.info(f"将日期列 '{date_col}' 从价格跳变检测中排除")
            self.logger.info(f"将对所有 {len(numeric_cols)} 个数值列进行价格跳变检测")

            # 从配置获取验证参数
            # 使用与DataCleaner一致的百分比变化阈值
            min_percent_change = validation_dict.get('min_percent_change', 0.06)  # 默认6%，与DataCleaner一致

            # 记录使用的百分比变化阈值
            self.logger.info(f"使用百分比变化阈值: {min_percent_change*100:.2f}% 来判断跳变点是否有效")

            # 标记是否所有列都通过验证
            all_columns_valid = True
            invalid_columns = []

            # 使用DataCleaner检测每个数值列的跳变
            for col_index, col in enumerate(numeric_cols):
                try:
                    # 记录当前处理的列名和进度
                    self.logger.info(f"正在处理第 {col_index+1}/{len(numeric_cols)} 个数值列: '{col}'")

                    # 创建DataCleaner实例
                    cleaner = DataCleaner(config=cleaner_config)

                    # 检测跳变
                    jumps_mask = cleaner._detect_price_jumps(data, col)

                    # 获取该列的跳变点数量
                    jump_count = jumps_mask.sum()

                    # 获取窗口大小（确保是整数）
                    pre_size = int(cleaner_config['preprocessing']['cleaning']['pre_window_size'])
                    post_size = int(cleaner_config['preprocessing']['cleaning']['post_window_size'])

                    # 计算该列跳变点的平均百分比变化
                    avg_percent_change = 0.0
                    if jump_count > 0:
                        jump_indices = jumps_mask[jumps_mask].index.tolist()
                        total_percent_change = 0.0
                        valid_jumps = 0

                        for idx in jump_indices:
                            i = data.index.get_loc(idx) if isinstance(idx, pd.Timestamp | str) else idx

                            # 确保索引在有效范围内
                            if isinstance(i, int | np.integer) and i >= pre_size and i < len(data) - post_size:
                                series = data[col]
                                pre_window = series.iloc[i-pre_size:i]
                                post_window = series.iloc[i+1:i+1+post_size]

                                pre_median = pre_window.median()
                                post_median = post_window.median()

                                # 计算百分比变化
                                if abs(pre_median) < 1e-9:  # 避免除零
                                    percent_change = 1.0 if abs(post_median) > 1e-9 else 0.0
                                else:
                                    percent_change = abs((post_median - pre_median) / pre_median)

                                total_percent_change += percent_change
                                valid_jumps += 1

                        # 计算平均百分比变化
                        avg_percent_change = total_percent_change / valid_jumps if valid_jumps > 0 else 0.0

                    # 判断该列是否有效（基于平均百分比变化）
                    is_valid = avg_percent_change < min_percent_change if jump_count > 0 else True

                    # 记录该列的检测结果
                    column_results[col] = {
                        'jump_count': int(jump_count),
                        'avg_percent_change': float(avg_percent_change),
                        'min_percent_change': float(min_percent_change),
                        'is_valid': is_valid,
                        'jumps': []
                    }

                    # 如果检测到跳变，添加到结果中
                    if jump_count > 0:
                        jump_indices = jumps_mask[jumps_mask].index.tolist()

                        for idx in jump_indices:
                            i = data.index.get_loc(idx) if isinstance(idx, pd.Timestamp | str) else idx

                            # 获取窗口大小（确保是整数）
                            pre_size = int(cleaner_config['preprocessing']['cleaning']['pre_window_size'])
                            post_size = int(cleaner_config['preprocessing']['cleaning']['post_window_size'])

                            # 计算跳变指标
                            if isinstance(i, int | np.integer) and i >= pre_size and i < len(data) - post_size:
                                series = data[col]
                                pre_window = series.iloc[i-pre_size:i]
                                post_window = series.iloc[i+1:i+1+post_size]

                                pre_median = pre_window.median()
                                post_median = post_window.median()

                                # 使用与DataCleaner一致的相对变化计算方式
                                if abs(pre_median) < 1e-9:  # 避免除零
                                    price_change = 1.0 if abs(post_median) > 1e-9 else 0.0
                                else:
                                    price_change = abs((post_median - pre_median) / pre_median)

                                pre_std = pre_window.std()
                                post_std = post_window.std()
                                volatility_ratio = post_std / pre_std if pre_std > 0 else float('inf')

                                # 安全地获取时间戳
                                timestamp = None
                                try:
                                    if isinstance(data.index, pd.DatetimeIndex) and isinstance(i, int | np.integer):
                                        # 将numpy整数转换为Python整数
                                        idx = int(i)
                                        timestamp = data.index[idx]
                                    else:
                                        timestamp = i
                                except Exception:
                                    timestamp = i

                                jump_info = {
                                    'index': int(i) if isinstance(i, int | np.integer) else i,
                                    'timestamp': timestamp,
                                    'metrics': {
                                        'price_change': float(price_change),
                                        'volatility_ratio': float(volatility_ratio)
                                    }
                                }

                                column_results[col]['jumps'].append(jump_info)
                                jumps.append({**jump_info, 'column': col})

                        # 判断该列是否有显著跳变（基于平均百分比变化）
                        if avg_percent_change >= min_percent_change:
                            all_columns_valid = False
                            invalid_columns.append(col)
                            error_msg = f"列 '{col}' 检测到显著跳变: {jump_count} 个，平均变化率 {avg_percent_change*100:.2f}%，超过阈值 {min_percent_change*100:.2f}%"
                            errors.append(error_msg)
                            self.logger.error(error_msg)
                        else:
                            self.logger.info(f"列 '{col}' 检测到 {jump_count} 个跳变点，但平均变化率 {avg_percent_change*100:.2f}% < {min_percent_change*100:.2f}%，在允许范围内")
                    else:
                        self.logger.info(f"列 '{col}' 未检测到跳变点")

                except Exception as e:
                    self.logger.error(f"检测列 {col} 的价格跳变时出错: {e!s}")
                    errors.append(f"列 {col} 跳变检测失败: {e!s}")
                    all_columns_valid = False
                    invalid_columns.append(col)
                    column_results[col] = {
                        'jump_count': 0,
                        'avg_percent_change': 0.0,
                        'min_percent_change': float(min_percent_change),
                        'is_valid': False,
                        'error': str(e),
                        'jumps': []
                    }

            # 汇总结果
            if invalid_columns:
                self.logger.error(f"检测到 {len(invalid_columns)} 个列存在显著跳变: {', '.join(invalid_columns[:3])}{' 等' if len(invalid_columns) > 3 else ''}")

            return {
                'is_valid': all_columns_valid,
                'jumps': jumps,
                'column_results': column_results,
                'errors': errors
            }

        except Exception as e:
            error_msg = f"价格跳变检测失败: {e!s}"
            self.logger.error(error_msg)
            return {
                'is_valid': False,
                'jumps': [],
                'column_results': {},
                'errors': [error_msg]
            }

    def _validate_tensor(self, features: torch.Tensor, targets: torch.Tensor) -> None:
        """验证张量数据，任何验证失败都会抛出异常

        Args:
            features: 特征张量
            targets: 目标张量

        Raises:
            DataValidationError: 当任何验证项不通过时抛出
        """
        start_time = time.time()

        # 基本验证
        if features is None or targets is None:
            error_msg = '特征或目标数据为空'
            self.logger.error(error_msg)
            raise DataValidationError(error_msg)

        # 维度验证
        if features.dim() < 2:
            error_msg = f'特征维度不足: {features.dim()}, 需要至少2维'
            self.logger.error(error_msg)
            raise DataValidationError(error_msg)

        # 样本数量验证
        if len(features) != len(targets):
            error_msg = f'特征和目标样本数不匹配: 特征={len(features)}, 目标={len(targets)}'
            self.logger.error(error_msg)
            raise DataValidationError(error_msg)

        # 空值检查
        if torch.isnan(features).any() or torch.isnan(targets).any():
            error_msg = '数据包含空值(NaN)'
            self.logger.error(error_msg)
            raise DataValidationError(error_msg)

        # 无穷值检查
        if torch.isinf(features).any() or torch.isinf(targets).any():
            error_msg = '数据包含无穷值(Inf)'
            self.logger.error(error_msg)
            raise DataValidationError(error_msg)

        # 记录验证信息
        self.logger.info(
            f"\n=== 张量数据验证完成 ===\n"
            f"- 特征形状: {features.shape}\n"
            f"- 目标形状: {targets.shape}\n"
            f"- 验证耗时: {time.time() - start_time:.3f}s"
        )
