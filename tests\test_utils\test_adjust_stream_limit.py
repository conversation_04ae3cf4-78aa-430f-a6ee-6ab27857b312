"""
测试自适应流管理器的流限制调整功能

此脚本直接测试自适应流管理器的 _adjust_stream_limit 方法，验证它是否能够正常工作。
"""

import os
import sys

import torch
import yaml

# 添加项目根目录到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from src.utils.cuda import cuda_manager


def test_adjust_stream_limit():
    """测试自适应流管理器的流限制调整功能"""
    print("开始测试自适应流管理器的流限制调整功能")

    # 确保CUDA可用
    if not torch.cuda.is_available():
        print("CUDA不可用，无法测试自适应流管理器")
        return

    print(f"CUDA可用: {torch.cuda.get_device_name()}")

    # 加载配置
    config_path = os.path.join(project_root, 'config.yaml')
    with open(config_path, encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 配置 cuda_manager
    print("配置 cuda_manager...")
    cuda_manager.configure(config['system']['cuda'])
    print("cuda_manager 配置完成")

    # 获取流管理器统计信息
    stats = cuda_manager.get_stream_stats()
    print(f"初始流统计信息: {stats}")

    # 检查是否使用自适应流管理器
    if stats.get('manager_type') != 'adaptive':
        print("警告: 未使用自适应流管理器，请检查配置")
        return
    else:
        print("确认: 使用自适应流管理器")

    # 获取自适应流管理器
    # 使用流管理器的公共接口获取流统计信息
    stream_stats = cuda_manager.get_stream_stats()
    if stream_stats.get('adaptive_enabled'):
        # 打印自适应流管理器参数
        print("自适应流管理器参数:")
        print(f"  - 流统计信息: {stream_stats}")

        # 创建一些流来测试
        print("\n创建一些流来测试...")
        streams = []
        for i in range(5):
            stream_name = f"test_stream_{i}"
            stream = cuda_manager.create_stream(stream_name)
            streams.append((stream_name, stream))
            print(f"创建流 {stream_name}")

        # 获取更新后的流统计信息
        updated_stats = cuda_manager.get_stream_stats()
        print(f"\n更新后的流统计信息: {updated_stats}")

        # 释放流
        print("\n释放流...")
        for stream_name, _ in streams:
            cuda_manager.release_stream(stream_name)
            print(f"释放流 {stream_name}")

        # 获取最终流统计信息
        final_stats = cuda_manager.get_stream_stats()
        print(f"\n最终流统计信息: {final_stats}")
    else:
        print("错误: 自适应流管理器未初始化")
        return

    print("\n测试完成!")

if __name__ == "__main__":
    test_adjust_stream_limit()
