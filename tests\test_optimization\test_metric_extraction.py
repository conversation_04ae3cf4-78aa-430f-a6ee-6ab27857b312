#!/usr/bin/env python
"""
测试指标提取功能

本模块测试超参数优化中的指标提取功能，特别是对异常情况的处理，
如空历史记录、NaN值等。

测试内容:
1. 空历史记录处理
2. NaN值处理
3. 类型错误处理
4. 负值处理
5. 正常值处理
"""

import logging
import os
import sys
import unittest

import numpy as np
import torch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# 导入被测试的函数
from src.optimization.metric_extraction import extract_metric

# 配置日志
logging.basicConfig(level=logging.DEBUG)


class TestMetricExtraction(unittest.TestCase):
    """测试指标提取功能"""

    def setUp(self):
        """测试前的准备工作"""
        # 禁用日志输出，使测试输出更清晰
        logging.disable(logging.CRITICAL)

    def tearDown(self):
        """测试后的清理工作"""
        # 恢复日志输出
        logging.disable(logging.NOTSET)

    def test_empty_history(self):
        """测试空历史记录处理"""
        # 测试空字典
        result = extract_metric({})
        self.assertEqual(result, float('inf'), "空字典应返回inf")

        # 测试空列表
        result = extract_metric([])
        self.assertEqual(result, float('inf'), "空列表应返回inf")

    def test_nan_values(self):
        """测试NaN值处理"""
        # 测试字典中的NaN值
        result = extract_metric({'val_mae': float('nan')})
        self.assertEqual(result, float('inf'), "NaN值应返回inf")

        # 测试列表中的NaN值
        result = extract_metric([{'val_mae': float('nan')}])
        self.assertEqual(result, float('inf'), "NaN值应返回inf")

        # 测试numpy NaN值
        result = extract_metric({'val_mae': np.nan})
        self.assertEqual(result, float('inf'), "NumPy NaN值应返回inf")

        # 测试torch NaN值
        if torch.cuda.is_available():
            result = extract_metric({'val_mae': torch.tensor(float('nan')).cuda()})
        else:
            result = extract_metric({'val_mae': torch.tensor(float('nan'))})
        self.assertEqual(result, float('inf'), "Torch NaN值应返回inf")

    def test_type_errors(self):
        """测试类型错误处理"""
        # 测试字符串值
        result = extract_metric({'val_mae': 'not_a_number'})
        self.assertEqual(result, float('inf'), "字符串值应返回inf")

        # 测试None值
        result = extract_metric({'val_mae': None})
        self.assertEqual(result, float('inf'), "None值应返回inf")

        # 测试复杂对象
        result = extract_metric({'val_mae': object()})
        self.assertEqual(result, float('inf'), "复杂对象应返回inf")

    def test_negative_values(self):
        """测试负值处理"""
        # 测试负值
        result = extract_metric({'val_mae': -1.0})
        self.assertEqual(result, float('inf'), "负值应返回inf")

        # 测试极小负值
        result = extract_metric({'val_mae': -1e-10})
        self.assertEqual(result, float('inf'), "极小负值应返回inf")

    def test_valid_values(self):
        """测试有效值处理"""
        # 测试正常浮点数
        value = 123.45
        result = extract_metric({'val_mae': value})
        self.assertEqual(result, value, f"有效值{value}应原样返回")

        # 测试零值
        result = extract_metric({'val_mae': 0.0})
        self.assertEqual(result, 0.0, "零值应原样返回")

        # 测试极小正值
        value = 1e-10
        result = extract_metric({'val_mae': value})
        self.assertEqual(result, value, f"极小正值{value}应原样返回")

        # 测试整数值
        value = 42
        result = extract_metric({'val_mae': value})
        self.assertEqual(result, float(value), f"整数值{value}应转换为浮点数返回")

        # 测试torch张量
        tensor_value = torch.tensor(123.45).cuda() if torch.cuda.is_available() else torch.tensor(123.45)
        result = extract_metric({'val_mae': tensor_value})
        self.assertEqual(result, tensor_value.item(), "Torch张量应转换为Python浮点数返回")

    def test_nested_structures(self):
        """测试嵌套结构处理"""
        # 测试嵌套字典 - 当前实现会从metrics中提取mae或val_mae
        result = extract_metric({'metrics': {'val_mae': 123.45}})
        self.assertEqual(result, 123.45, "应从metrics字典中提取val_mae")

        # 测试metrics中只有mae
        result = extract_metric({'metrics': {'mae': 123.45}})
        self.assertEqual(result, 123.45, "应从metrics字典中提取mae")

        # 测试metrics中没有mae或val_mae
        result = extract_metric({'metrics': {'loss': 123.45}})
        self.assertEqual(result, float('inf'), "metrics中没有mae或val_mae时应返回inf")

        # 测试嵌套列表
        result = extract_metric([{'epoch': 1, 'val_mae': 200.0}, {'epoch': 2, 'val_mae': 123.45}])
        self.assertEqual(result, 123.45, "应提取列表中最后一个元素的val_mae")

        # 测试val_mae作为列表
        result = extract_metric({'val_mae': [200.0, 150.0, 123.45]})
        self.assertEqual(result, 123.45, "应提取val_mae列表中的最后一个元素")


if __name__ == '__main__':
    unittest.main()
