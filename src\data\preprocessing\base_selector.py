"""特征选择器基类 - 定义特征选择器的基本接口

此模块定义了特征选择器的基本接口，所有特征选择器实现都应该继承这个基类。

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理，特征选择参数控制
   - src/utils/logger.py: 日志系统，选择过程记录

2. 共用模块:
   - src/data/data_loader.py: 数据加载，特征读取预处理
   - src/data/preprocessing/feature_selector.py: 特征选择器实现

3. 配置文件:
   - config.yaml:
     └── feature_selection: 特征选择配置

4. 父类模块:
   - 无 (这是基类)

5. 同阶段数据处理模块:
   - src/data/preprocessing/data_validator.py: 数据验证
   - src/data/preprocessing/data_cleaner.py: 数据清洗
"""

from abc import ABC, abstractmethod
from typing import Any

import pandas as pd

from src.utils.logger import LoggerFactory


class BaseFeatureSelector(ABC):
    """特征选择基类

    所有特征选择器实现都应该继承这个基类，以确保一致的API和行为。
    """
    def __init__(self, config: dict[str, Any] | None = None):
        """初始化特征选择器基类

        Args:
            config: 配置字典，可选
        """
        self.config = config or {}
        self.logger = LoggerFactory().get_logger(self.__class__.__name__)

    @abstractmethod
    def fit(self, data: pd.DataFrame) -> None:
        """训练特征选择器

        Args:
            data: 训练数据
        """
        pass

    @abstractmethod
    def transform(self, data: pd.DataFrame) -> pd.DataFrame:
        """执行特征选择

        Args:
            data: 输入数据

        Returns:
            选择特征后的数据
        """
        pass

    @abstractmethod
    def get_selected_features(self) -> list:
        """获取选择的特征列表

        Returns:
            选择的特征名称列表
        """
        pass
