"""位置编码模块 - 提供序列模型的位置信息编码

本模块实现了序列模型的位置信息编码，包括：
1. 正弦位置编码
2. 学习式位置编码
3. 相对位置编码
4. 时间位置编码
"""

import math

import torch
from torch import nn

from src.models.base.base_module import BaseModule


class PositionalEncoding(BaseModule):
    """位置编码 - 为序列提供位置信息"""

    def __init__(
        self,
        d_model: int,
        max_len: int = 5000,
        dropout: float = 0.1,
        encoding_type: str = "sinusoidal"
    ):
        """初始化位置编码

        Args:
            d_model: 模型维度
            max_len: 最大序列长度
            dropout: Dropout比率
            encoding_type: 编码类型，可选值：sinusoidal, learned, relative
        """
        super().__init__("PositionalEncoding")

        self.dropout = nn.Dropout(p=dropout)
        self.encoding_type = encoding_type

        if encoding_type == "sinusoidal":
            # 正弦位置编码
            pe = torch.zeros(max_len, d_model)
            position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(1000.0) / d_model))  # 简化频率计算
            pe[:, 0::2] = torch.sin(position * div_term)
            pe[:, 1::2] = torch.cos(position * div_term)
            pe = pe.unsqueeze(0)
            self.register_buffer('pe', pe)
        elif encoding_type == "learned":
            # 学习式位置编码
            self.pe = nn.Parameter(torch.randn(1, max_len, d_model))
        elif encoding_type == "relative":
            # 相对位置编码
            self.pe = nn.Parameter(torch.randn(max_len * 2 - 1, d_model))
            self.max_len = max_len
        else:
            raise ValueError(f"不支持的编码类型: {encoding_type}")

        self.logger.info(
            f"位置编码初始化完成:\n"
            f"- 模型维度: {d_model}\n"
            f"- 最大长度: {max_len}\n"
            f"- 编码类型: {encoding_type}"
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, d_model]

        Returns:
            torch.Tensor: 添加位置编码后的序列
        """
        if self.encoding_type == "sinusoidal":
            # 正弦位置编码
            x = x + self.pe[:, :x.size(1)]
        elif self.encoding_type == "learned":
            # 学习式位置编码
            x = x + self.pe[:, :x.size(1)]
        elif self.encoding_type == "relative":
            # 相对位置编码 - 在注意力层中使用，这里不做处理
            pass

        return self.dropout(x)

    def get_relative_positions(self, length: int) -> torch.Tensor:
        """获取相对位置编码

        Args:
            length: 序列长度

        Returns:
            torch.Tensor: 相对位置编码
        """
        if self.encoding_type != "relative":
            raise ValueError("只有相对位置编码才能使用get_relative_positions方法")

        # 计算相对位置索引
        range_vec = torch.arange(length)
        range_mat = range_vec.unsqueeze(0).repeat(length, 1)
        distance_mat = range_mat - range_mat.transpose(0, 1)
        distance_mat_clipped = torch.clamp(distance_mat + self.max_len - 1, 0, 2 * self.max_len - 2)

        # 获取相对位置编码
        relative_positions = self.pe[distance_mat_clipped.long()]

        return relative_positions


class TimePositionalEncoding(BaseModule):
    """时间位置编码 - 为时间序列提供时间信息"""

    def __init__(
        self,
        d_model: int,
        max_len: int = 1000,  # 减少默认最大序列长度以降低内存使用
        dropout: float = 0.1,
        base_freq: float = 1.0
    ):
        """初始化时间位置编码

        Args:
            d_model: 模型维度
            max_len: 最大序列长度
            dropout: Dropout比率
            base_freq: 基础频率
        """
        super().__init__("TimePositionalEncoding")

        self.dropout = nn.Dropout(p=dropout)
        self.base_freq = base_freq

        # 创建频率列表
        freqs = torch.ones(d_model // 2)
        for i in range(d_model // 2):
            freqs[i] = 1.0 / (base_freq * 10000 ** (i / d_model))

        # 注册频率参数
        self.register_buffer('freqs', freqs)

        self.logger.info(
            f"时间位置编码初始化完成:\n"
            f"- 模型维度: {d_model}\n"
            f"- 最大长度: {max_len}\n"
            f"- 基础频率: {base_freq}"
        )

    def forward(
        self,
        x: torch.Tensor,
        times: torch.Tensor | None = None
    ) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, d_model]
            times: 时间戳 [batch_size, seq_length]，如果为None则使用等间隔时间

        Returns:
            torch.Tensor: 添加时间位置编码后的序列
        """
        batch_size, seq_length, d_model = x.shape

        # 如果没有提供时间戳，使用等间隔时间
        if times is None:
            times = torch.arange(seq_length, dtype=torch.float, device=x.device)
            times = times.unsqueeze(0).repeat(batch_size, 1)

        # 创建时间位置编码
        pe = torch.zeros(batch_size, seq_length, d_model, device=x.device)

        # 计算正弦和余弦编码
        for i in range(d_model // 2):
            pe[:, :, 2*i] = torch.sin(times * self.freqs[i])
            pe[:, :, 2*i+1] = torch.cos(times * self.freqs[i])

        # 添加位置编码
        x = x + pe

        return self.dropout(x)
