#!/usr/bin/env python
"""
测试内存管理功能

本模块测试超参数优化中的内存管理功能，包括CUDA内存监控与清理功能。

测试内容:
1. 内存清理
2. 内存监控
3. 内存统计
4. 内存优化
"""

import logging
import os
import sys
import unittest
from unittest.mock import MagicMock, patch

import optuna

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# 导入被测试的函数
from src.optimization.memory_management import (
    cleanup_memory,
    get_memory_stats,
    monitor_memory,
    optimize_memory_usage,
)

# 配置日志
logging.basicConfig(level=logging.DEBUG)


class TestMemoryManagement(unittest.TestCase):
    """测试内存管理功能"""

    def setUp(self):
        """测试前的准备工作"""
        # 禁用日志输出，使测试输出更清晰
        logging.disable(logging.CRITICAL)

        # 创建模拟的Optuna试验
        self.trial = MagicMock(spec=optuna.trial.Trial)
        self.trial.number = 1
        self.trial.params = {
            'model.use_self_attention': False,
            'data.window_size': 36,
            'model.n_heads': 4,
            'model.hidden_dim': 64,
            'model.dimensions.base_dim': 64,
            'model.generator.num_layers': 2
        }

    def tearDown(self):
        """测试后的清理工作"""
        # 恢复日志输出
        logging.disable(logging.NOTSET)

    # OOM风险检查已移至parameter_exploration.py中，通过参数约束处理实现

    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.memory_allocated', return_value=1024*1024*100)  # 100MB
    @patch('torch.cuda.memory_reserved', return_value=1024*1024*200)   # 200MB
    @patch('torch.cuda.max_memory_allocated', return_value=1024*1024*150)  # 150MB
    @patch('torch.cuda.max_memory_reserved', return_value=1024*1024*250)   # 250MB
    @patch('torch.cuda.empty_cache')
    @patch('torch.cuda.reset_peak_memory_stats')
    def test_cleanup_memory(self, mock_reset_peak, mock_empty_cache,
                           mock_max_reserved, mock_max_allocated,
                           mock_reserved, mock_allocated, mock_is_available):
        """测试内存清理功能"""
        # 调用内存清理函数
        memory_stats = cleanup_memory(self.trial)

        # 验证结果
        self.assertEqual(memory_stats['before_clear'], 100.0)
        self.assertEqual(memory_stats['before_reserved'], 200.0)
        self.assertEqual(memory_stats['after_clear'], 100.0)
        self.assertEqual(memory_stats['after_reserved'], 200.0)
        self.assertEqual(memory_stats['peak_memory'], 150.0)
        self.assertEqual(memory_stats['peak_reserved'], 250.0)
        self.assertTrue(memory_stats['reset_peak'])

        # 验证调用
        mock_empty_cache.assert_called_once()
        mock_reset_peak.assert_called_once()

    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.memory_allocated', return_value=1024*1024*100)  # 100MB
    @patch('torch.cuda.max_memory_allocated', return_value=1024*1024*150)  # 150MB
    def test_monitor_memory(self, mock_max_allocated, mock_allocated, mock_is_available):
        """测试内存监控功能"""
        # 调用内存监控函数
        current_memory, peak_memory = monitor_memory()

        # 验证结果
        self.assertEqual(current_memory, 100.0)
        self.assertEqual(peak_memory, 150.0)

    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.memory_allocated', return_value=1024*1024*100)  # 100MB
    @patch('torch.cuda.memory_reserved', return_value=1024*1024*200)   # 200MB
    @patch('torch.cuda.max_memory_allocated', return_value=1024*1024*150)  # 150MB
    @patch('torch.cuda.max_memory_reserved', return_value=1024*1024*250)   # 250MB
    @patch('torch.cuda.get_device_properties')
    def test_get_memory_stats(self, mock_get_properties, mock_max_reserved,
                             mock_max_allocated, mock_reserved, mock_allocated,
                             mock_is_available):
        """测试获取内存统计信息"""
        # 模拟设备属性
        mock_properties = MagicMock()
        mock_properties.total_memory = 1024*1024*1024*3  # 3GB
        mock_get_properties.return_value = mock_properties

        # 调用获取内存统计函数
        stats = get_memory_stats()

        # 验证结果
        self.assertEqual(stats['allocated'], 100.0)
        self.assertEqual(stats['reserved'], 200.0)
        self.assertEqual(stats['max_allocated'], 150.0)
        self.assertEqual(stats['max_reserved'], 250.0)
        self.assertEqual(stats['total'], 3072.0)
        self.assertEqual(stats['available'], 3072.0 - 200.0)
        self.assertAlmostEqual(stats['utilization'], (200.0 / 3072.0) * 100)

    def test_optimize_memory_usage(self):
        """测试内存优化功能"""
        # 使用with语句进行patch，避免装饰器问题
        with patch('torch.cuda.is_available', return_value=True), \
             patch('torch.cuda.set_per_process_memory_fraction') as mock_set_fraction, \
             patch('torch.cuda.empty_cache') as mock_empty_cache:
                    # 保存原始值
                    from torch.backends import cudnn
                    original_benchmark = getattr(cudnn, 'benchmark', None)

                    try:
                        # 调用内存优化函数
                        optimize_memory_usage()

                        # 验证调用
                        mock_set_fraction.assert_called_once_with(0.9)  # 函数内部使用固定值0.9
                        mock_empty_cache.assert_called_once()
                    finally:
                        # 恢复原始值
                        if original_benchmark is not None:
                            cudnn.benchmark = original_benchmark


if __name__ == '__main__':
    unittest.main()
