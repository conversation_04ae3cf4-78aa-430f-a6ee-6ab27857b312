"""
GAN 损失比率学习率平衡器

根据生成器和判别器的损失比率动态调整它们的学习率，以维持训练平衡。
"""


from torch import optim

# 导入日志记录器
from src.utils.logger import get_logger


class GanLossRatioLrBalancer:
    """
    根据 D/G 损失比率调整学习率以平衡 GAN 训练。

    该平衡器在每个 epoch 结束时调用，根据平均损失计算调整比例，
    并反向调整生成器和判别器的学习率。

    比率计算方式：D/G（判别器损失/生成器损失）
    - 比率 > 上限阈值：判别器强/生成器弱 -> 提高G学习率，降低D学习率
    - 比率 < 下限阈值：判别器弱/生成器强 -> 降低G学习率，提高D学习率
    """
    def __init__(
        self,
        optimizer_g: optim.Optimizer,
        optimizer_d: optim.Optimizer,
        target_ratio: float = 1.0,
        sensitivity: float = 0.1,
        min_lr: float = 1e-6,
        max_lr: float = 1e-2,
        epsilon: float = 1e-8,
        logger_name: str | None = None
    ):
        """
        初始化平衡器。

        Args:
            optimizer_g: 生成器的优化器。
            optimizer_d: 判别器的优化器。
            target_ratio: 目标 D/G 损失比率 (d_loss / g_loss)。
            sensitivity: 调整敏感度 (k 值)，控制学习率对偏差的反应强度。
            min_lr: 允许的最小学习率。
            max_lr: 允许的最大学习率。
            epsilon: 防止除以零的小值。
            logger_name: 日志记录器的名称。
        """
        if not isinstance(optimizer_g, optim.Optimizer):
            raise TypeError(f"optimizer_g 必须是 torch.optim.Optimizer 类型, 实际为 {type(optimizer_g)}")
        if not isinstance(optimizer_d, optim.Optimizer):
            raise TypeError(f"optimizer_d 必须是 torch.optim.Optimizer 类型, 实际为 {type(optimizer_d)}")
        if not sensitivity > 0:
            raise ValueError(f"sensitivity (k 值) 必须大于 0, 实际为 {sensitivity}")
        if not 0 <= min_lr < max_lr:
            raise ValueError(f"学习率范围无效: min_lr={min_lr}, max_lr={max_lr}")
        if not epsilon > 0:
             raise ValueError(f"epsilon 必须大于 0, 实际为 {epsilon}")

        self.optimizer_g = optimizer_g
        self.optimizer_d = optimizer_d
        self.target_ratio = target_ratio
        self.sensitivity = sensitivity
        self.min_lr = min_lr
        self.max_lr = max_lr
        self.epsilon = epsilon

        # 获取日志记录器
        log_name = logger_name or self.__class__.__name__
        self.logger = get_logger(log_name)

        self.logger.info(
            f"GAN 损失比率学习率平衡器已初始化:\n"
            f"- 目标 D/G 损失比率: {self.target_ratio:.4f}\n"
            f"- 调整敏感度 (k): {self.sensitivity:.4f}\n"
            f"- 学习率范围: [{self.min_lr:.6f}, {self.max_lr:.6f}]\n"
            f"- Epsilon: {self.epsilon}"
        )

    def _get_lr(self, optimizer: optim.Optimizer) -> float:
        """获取优化器的当前学习率"""
        # 假设只有一个参数组
        if not optimizer.param_groups:
             self.logger.error(f"优化器 {type(optimizer).__name__} 没有参数组，无法获取学习率。")
             return 0.0 # 或者抛出错误？
        return optimizer.param_groups[0]['lr']

    def _set_lr(self, optimizer: optim.Optimizer, lr: float):
        """设置优化器的学习率"""
        # 假设只有一个参数组
        if not optimizer.param_groups:
             self.logger.error(f"优化器 {type(optimizer).__name__} 没有参数组，无法设置学习率。")
             return
        # 限制学习率在指定范围内
        clamped_lr = max(self.min_lr, min(self.max_lr, lr))
        optimizer.param_groups[0]['lr'] = clamped_lr


    def step(self, g_loss: float, d_loss: float):
        """
        根据当前 epoch 的平均损失调整学习率。

        Args:
            g_loss: 当前 epoch 的平均生成器损失。
            d_loss: 当前 epoch 的平均判别器损失。
        """
        # 1. 计算实际比率 (D/G)
        # 注意：在WGAN-GP中，生成器损失为负值是完全正常的现象，不需要特殊处理

        # 对于极小的生成器损失（接近零），使用epsilon防止除零
        # 注意：负损失在WGAN-GP中是正常的，不需要特殊处理
        if abs(g_loss) < self.epsilon:
            self.logger.warning(f"生成器损失绝对值 {abs(g_loss):.4f} 非常小，使用epsilon ({self.epsilon}) 防止除零错误。")
            g_loss = self.epsilon if g_loss >= 0 else -self.epsilon

        # 计算损失比率 (D/G)
        # 使用绝对值计算比率，因为我们关心的是损失的相对大小，而不是符号
        actual_ratio = abs(d_loss) / abs(g_loss)

        # 只应用最小比率限制，移除了ratio_max_cap = 10.0的限制
        ratio_min_cap = 0.1
        if actual_ratio < ratio_min_cap:
            self.logger.debug(f"原始 D/G 损失比率 {actual_ratio:.4f} 低于下限 {ratio_min_cap}, 已调整为 {ratio_min_cap}")
            actual_ratio = ratio_min_cap

        # 2. 计算偏差
        deviation = actual_ratio - self.target_ratio

        # 3. 计算调整比例因子，添加上限防止过度调整
        # 使用 abs(deviation) 确保 adjustment_scale >= 1.0
        max_adjustment_scale = 1.5  # 限制最大调整幅度
        raw_adjustment_scale = 1.0 + self.sensitivity * abs(deviation)
        adjustment_scale = min(raw_adjustment_scale, max_adjustment_scale)

        if raw_adjustment_scale > max_adjustment_scale:
            self.logger.debug(f"调整比例因子 {raw_adjustment_scale:.4f} 超过上限 {max_adjustment_scale}，已限制")

        # 4. 获取当前学习率
        current_g_lr = self._get_lr(self.optimizer_g)
        current_d_lr = self._get_lr(self.optimizer_d)

        # 5. 计算新学习率
        # 根据D/G比率调整学习率
        if deviation > 0:  # D 损失相对较高 (D 强 / G 弱) -> 提高 G, 降低 D
            new_g_lr = current_g_lr * adjustment_scale
            new_d_lr = current_d_lr / adjustment_scale
            adjustment_direction = "提高 G / 降低 D"
        elif deviation < 0: # G 损失相对较高 (G 强 / D 弱) -> 降低 G, 提高 D
            new_g_lr = current_g_lr / adjustment_scale
            new_d_lr = current_d_lr * adjustment_scale
            adjustment_direction = "降低 G / 提高 D"
        else: # deviation == 0, 完美平衡
            self.logger.debug(f"D/G 损失比率 ({actual_ratio:.4f}) 已达目标 ({self.target_ratio:.4f})，无需调整学习率。")
            return # 无需调整

        # 6. 设置新学习率 (内部已包含范围限制)
        self._set_lr(self.optimizer_g, new_g_lr)
        self._set_lr(self.optimizer_d, new_d_lr)

        # 7. 记录日志
        final_g_lr = self._get_lr(self.optimizer_g) # 获取最终设置的 LR
        final_d_lr = self._get_lr(self.optimizer_d)

        self.logger.info(
            f"学习率调整: D/G 损失比率={actual_ratio:.4f} (目标={self.target_ratio:.4f}), "
            f"偏差={deviation:.4f}, 调整方向='{adjustment_direction}'.\n"
            f"  G LR: {current_g_lr:.6f} -> {final_g_lr:.6f}\n"
            f"  D LR: {current_d_lr:.6f} -> {final_d_lr:.6f}"
        )
