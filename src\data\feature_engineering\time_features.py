"""时间特征生成器 - 从日期序列生成时间特征

此模块负责从日期序列中提取各种时间特征，包括：
- 基本时间特征（年、月、日、星期几等）
- 周期性标记（月初、月末、季初、季末等）
- 周期性编码（月份正弦/余弦编码、星期几正弦/余弦编码等）
"""


import numpy as np
import pandas as pd
import torch

from src.utils.config.manager import ConfigManager

# 移除 BaseFeatureGenerator 继承
# from src.data.feature_engineering.base import BaseFeatureGenerator
from src.utils.logger import get_logger


# 不再继承 BaseFeatureGenerator
class TimeFeatureGenerator:
    """时间特征预处理器

    从日期序列中提取各种数值型时间特征，作为特征工程的预处理步骤。
    """

    def __init__(self, config: ConfigManager):
        """初始化时间特征预处理器

        Args:
            config: 配置管理器实例
        """
        if not isinstance(config, ConfigManager):
            raise TypeError("config参数必须为ConfigManager实例")

        self.config = config
        self.logger = get_logger(__name__)

        # 检查配置，确定是否启用以及要提取的特征
        self._enabled: bool = False
        self.features_to_extract_config: list[str] = []
        self._check_config()

    def _check_config(self) -> None:
        """检查配置是否有效，并存储要提取的特征列表。
        假设 ConfigLoader 已确保基本结构存在。
        如果配置无效或不合理，则抛出 ValueError。
        """
        # 假设 feature_engineering 和 time_preprocessing 存在
        feature_eng = self.config.feature_engineering
        # 使用 getattr 并断言，明确表示我们期望 time_preprocessing 存在
        time_preprocessing_cfg = getattr(feature_eng, 'time_preprocessing', None)
        assert time_preprocessing_cfg is not None, "ConfigLoader 应确保 feature_engineering.time_preprocessing 存在"

        # 获取要提取的特征列表
        features_list = getattr(time_preprocessing_cfg, 'features_to_extract', None)
        if features_list is None or not isinstance(features_list, list) or not features_list:
            error_msg = "配置 feature_engineering.time_preprocessing.features_to_extract 必须是一个非空列表。"
            self.logger.error(f"_check_config 失败: {error_msg} 当前获取到的值: {features_list}")
            raise ValueError(error_msg)

        # 验证列表内容
        available_keys = self._get_available_feature_keys()
        valid_features = []
        invalid_features = []
        for feature in features_list:
            if feature in available_keys:
                valid_features.append(feature)
            else:
                invalid_features.append(feature)

        if invalid_features:
            error_msg = f"配置的时间特征列表中包含无效项: {invalid_features}。可用项: {available_keys}"
            self.logger.error(f"_check_config 失败: {error_msg}")
            raise ValueError(error_msg)

        if not valid_features:
            # 这个理论上不应该发生，因为上面已经检查了 invalid_features
            # 但为了健壮性保留
            error_msg = "配置的时间特征列表经验证后为空。"
            self.logger.error(f"_check_config 失败: {error_msg}")
            raise ValueError(error_msg)

        self.features_to_extract_config = valid_features
        self._enabled = True # 如果配置有效，则启用
        self.logger.info(f"时间特征预处理已启用，将提取: {self.features_to_extract_config}")

    def _get_available_feature_keys(self) -> list[str]:
        """返回所有可用的时间特征名称"""
        # 这个列表应该与 generate 方法中的 available_features_calc 字典的键保持一致
        return [
            'year', 'month', 'day', 'dayofweek', 'dayofyear', 'weekofyear', 'quarter',
            'is_month_start', 'is_month_end', 'is_quarter_start', 'is_quarter_end',
            'is_year_start', 'is_year_end', 'is_weekend',
            'month_sin', 'month_cos', 'dayofweek_sin', 'dayofweek_cos'
        ]

    # 修改 generate 方法签名，移除 data 和 kwargs 参数，返回 Tuple
    def generate(self, date_series: pd.Series) -> tuple[torch.Tensor, list[str]]:
        """从日期序列生成数值型时间特征

        Args:
            date_series: 日期序列 (必须提供)

        Returns:
            Tuple[torch.Tensor, List[str]]: 生成的时间特征张量和对应的特征名称列表
                                            如果禁用或出错，返回 (空张量, 空列表)
        """
        # 检查是否启用
        if not self.is_enabled:
            self.logger.info("时间特征预处理器已禁用，跳过。")
            n_samples = len(date_series) if date_series is not None else 0
            return torch.empty((n_samples, 0), dtype=torch.float32), []

        # 检查日期序列
        if date_series is None:
            self.logger.error("未提供日期序列 (date_series)，无法生成时间特征。")
            return torch.empty((0, 0), dtype=torch.float32), []

        # 使用配置中存储的要提取的特征列表
        features_to_extract = self.features_to_extract_config
        if not features_to_extract: # 双重检查
             self.logger.error("内部错误：features_to_extract_config 为空，无法生成时间特征。")
             return torch.empty((len(date_series), 0), dtype=torch.float32), []

        self.logger.info(f"开始生成时间特征，提取: {features_to_extract}")

        # 定义所有可能的时间特征及其计算函数
        available_features_calc = {
            'year': lambda ds: ds.dt.year,
            'month': lambda ds: ds.dt.month,
            'day': lambda ds: ds.dt.day,
            'dayofweek': lambda ds: ds.dt.dayofweek,
            'dayofyear': lambda ds: ds.dt.dayofyear,
            'weekofyear': lambda ds: ds.dt.isocalendar().week.astype(int),
            'quarter': lambda ds: ds.dt.quarter,
            'is_month_start': lambda ds: ds.dt.is_month_start.astype(int),
            'is_month_end': lambda ds: ds.dt.is_month_end.astype(int),
            'is_quarter_start': lambda ds: ds.dt.is_quarter_start.astype(int),
            'is_quarter_end': lambda ds: ds.dt.is_quarter_end.astype(int),
            'is_year_start': lambda ds: ds.dt.is_year_start.astype(int),
            'is_year_end': lambda ds: ds.dt.is_year_end.astype(int),
            'is_weekend': lambda ds: (ds.dt.dayofweek >= 5).astype(int),
            'month_sin': lambda ds: np.sin(2 * np.pi * ds.dt.month / 12),
            'month_cos': lambda ds: np.cos(2 * np.pi * ds.dt.month / 12),
            'dayofweek_sin': lambda ds: np.sin(2 * np.pi * ds.dt.dayofweek / 7),
            'dayofweek_cos': lambda ds: np.cos(2 * np.pi * ds.dt.dayofweek / 7),
        }

        # 1. 数据预处理 (确保 date_series 是 datetime 类型)
        # 如果转换失败，pd.to_datetime(..., errors='raise') 会直接抛出异常
        if not pd.api.types.is_datetime64_any_dtype(date_series):
            date_series = pd.to_datetime(date_series, errors='raise')
            self.logger.debug("成功将日期列转换为datetime类型")

        # 2. 特征提取 (只提取指定的)
        df_time = pd.DataFrame(index=date_series.index) # 保持索引一致
        extracted_feature_names = []

        for feature_name in features_to_extract:
            # We already validated the names in _check_config
            # 如果计算失败，让异常直接抛出
            df_time[feature_name] = available_features_calc[feature_name](date_series)
            extracted_feature_names.append(feature_name)

        if df_time.empty or not extracted_feature_names:
             # 这通常不应该发生，除非 features_to_extract 为空（已在 _check_config 阻止）
             self.logger.warning("未能提取任何有效的时间特征。")
             return torch.empty((len(date_series), 0), dtype=torch.float32), []


        # 3. 特征验证和转换
        # 检查是否有无效值 (NaN) - 这表示计算错误或上游数据问题
        if df_time.isna().values.any():
            problematic_cols = df_time.columns[df_time.isna().any()].tolist()
            error_msg = f"生成的时间特征包含无效值 (NaN)，问题列: {problematic_cols}。这通常表示计算错误或上游数据问题。"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 将特征转换为张量
        # 确定目标设备 - 默认CPU，因为使用了pandas
        target_device = 'cpu'
        time_features_tensor = torch.tensor(df_time.values, dtype=torch.float32).to(target_device)

        # 记录特征统计信息
        feature_stats = {
            'shape': time_features_tensor.shape,
            'features': extracted_feature_names,
            'non_zero': (time_features_tensor != 0).float().mean().item() if extracted_feature_names else 0.0
        }

        self.logger.info(
            f"时间特征生成完成:\n"
            f"- 特征维度: {feature_stats['shape']}\n"
            f"- 特征列表: {', '.join(feature_stats['features'])}\n"
            f"- 非零比例: {feature_stats['non_zero']:.2%}"
        )

        # 返回生成的张量和特征名称列表
        return time_features_tensor, extracted_feature_names

    # 移除顶层异常捕获，让具体错误（如日期转换失败、特征计算失败、NaN值错误）直接抛出
    # except Exception as e:
        #     error_msg = f"生成时间特征失败: {str(e)}"
        #     self.logger.error(f"{error_msg}\n调用栈信息:\n", exc_info=True)
        #     # 在出错时返回空结果
        #     return torch.empty((len(date_series), 0), dtype=torch.float32), []

    # 移除 get_feature_names 和 feature_count (不再需要符合 BaseFeatureGenerator 接口)
    # def get_feature_names(self) -> List[str]:
    #     """获取生成的特征名称列表
    #
    #     Returns:
    #         List[str]: 特征名称列表
    #     """
    #     return self.feature_names
    #
    # @property
    # def feature_count(self) -> int:
    #     """获取生成的特征数量
    #
    #     Returns:
    #         int: 特征数量
    #     """
    #     return self._feature_count

    @property
    def is_enabled(self) -> bool:
        """检查时间特征预处理器是否启用

        Returns:
            bool: 是否启用
        """
        return self._enabled
