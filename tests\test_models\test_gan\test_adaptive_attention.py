"""测试自适应扩张率注意力机制

此脚本测试新的自适应扩张率注意力机制，验证CUDA流功能是否正常工作。
"""

import time

import torch

from src.models.gan.components.adaptive_attention import AdaptiveDilationAttention
from src.models.gan.components.attention_factory import create_adaptive_attention
from src.utils.cuda import cuda_manager

# 创建测试数据
batch_size = 8
seq_length = 64
embed_dim = 128

# 检查CUDA是否可用
if cuda_manager.is_cuda_available:
    device = cuda_manager.device
    print(f"使用设备: {device}")
else:
    device = torch.device("cpu")
    print("CUDA不可用，使用CPU")

x = torch.randn(batch_size, seq_length, embed_dim, device=device)

# 测试不同配置
configs = [
    {"name": "使用CUDA流", "use_streams": True},
    {"name": "不使用CUDA流", "use_streams": False}
]

for config in configs:
    print(f"\n测试配置: {config['name']}")

    # 创建自适应扩张率注意力实例
    attention = AdaptiveDilationAttention(
        embed_dim=embed_dim,
        num_heads=4,
        num_scales=3,
        dropout=0.1,
        use_streams=config["use_streams"],
        max_batch_streams=8,
        max_scale_streams=4,
        verbose_logging=False
    ).to(device)

    # 预热
    print("预热中...")
    with torch.no_grad():
        for _ in range(3):
            # 捕获预热阶段的输出，确保变量被定义
            warmup_output, _ = attention(x)

    # 测试性能
    print("测试性能...")
    torch.cuda.synchronize() if cuda_manager.is_cuda_available else None
    start_time = time.time()

    # 初始化输出变量
    output = None

    with torch.no_grad():
        for _i in range(10):
            # 每次迭代都保存输出，确保在循环结束后变量被定义
            output, _ = attention(x)

    torch.cuda.synchronize() if cuda_manager.is_cuda_available else None
    elapsed_time = (time.time() - start_time) * 1000 / 10
    print(f"平均执行时间: {elapsed_time:.2f}毫秒")

    # 检查输出是否存在
    if output is not None:
        print(f"输出形状: {output.shape}")
    else:
        print("输出为None，请检查模型")

# 测试工厂函数
print("\n测试工厂函数")

# 使用工厂函数创建自适应扩张率注意力实例
attention = create_adaptive_attention(
    embed_dim=embed_dim,
    num_heads=4,
    num_scales=3,
    dropout=0.1,
    use_cuda_streams=True,
    cuda_stream_config={
        "max_batch_streams": 8,
        "max_scale_streams": 4,
        "verbose_logging": True
    },
    device=device
)

# 测试性能
print("测试性能...")

# 初始化输出变量
output = None

try:
    with torch.no_grad():
        output, _ = attention(x)

    # 检查输出是否存在
    if output is not None:
        print(f"输出形状: {output.shape}")
    else:
        print("输出为None，请检查模型")

    print("测试完成 ✓")
except Exception as e:
    print(f"测试失败: {e!s}")
