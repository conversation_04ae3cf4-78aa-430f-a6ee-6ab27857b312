"""数据处理模块 - 提供数据加载、处理和管理功能

本模块包含以下主要组件：
1. TimeSeriesDataset: 基础时序数据集
2. TimeSeriesWindowDataset: 时序窗口数据集
3. Standardizer: 标准化处理器
4. DataValidator: 数据验证器
"""

from src.data.data_loader import (
    TimeSeriesDataLoader,
    TimeSeriesDataLoaderProtocol,
    create_dataloaders,
    execute_data_loading,
)
from src.data.preprocessing.data_validator import DataValidator
from src.data.protocol import (
    DatasetProtocol,
    TimeSeriesDataset,
    TimeSeriesDatasetProtocol,
)
from src.data.standardization import StandardizationProtocol, Standardizer
from src.data.windowed_time_series import TimeSeriesWindowDataset, WindowDataset

__all__ = [
    'DataValidator',
    # 协议接口
    'DatasetProtocol',
    'StandardizationProtocol',
    # 处理器和工具
    'Standardizer',
    'TimeSeriesDataLoader',
    'TimeSeriesDataLoaderProtocol',
    # 数据集类
    'TimeSeriesDataset',
    'TimeSeriesDatasetProtocol',
    'TimeSeriesWindowDataset',
    'WindowDataset',
    'create_dataloaders',
    # 数据加载函数
    'execute_data_loading'
]
