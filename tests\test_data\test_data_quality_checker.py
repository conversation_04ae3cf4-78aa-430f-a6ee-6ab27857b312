"""
单元测试：数据质量检查器 (DataQualityChecker)
"""

import pytest
import torch

from src.data.data_quality_checker import DataQuality<PERSON>he<PERSON>, DataQualityError

# --- 测试 check_nan_inf ---

def test_check_nan_inf_valid():
    """测试 check_nan_inf 对有效数据的处理"""
    valid_tensor = torch.tensor([[1.0, 2.0], [3.0, 4.0]])
    try:
        DataQualityChecker.check_nan_inf(valid_tensor, "ValidTensor")
    except DataQualityError:
        pytest.fail("check_nan_inf 对有效数据不应抛出异常")

def test_check_nan_inf_with_nan():
    """测试 check_nan_inf 对包含 NaN 的数据的处理"""
    nan_tensor = torch.tensor([[1.0, float('nan')], [3.0, 4.0]])
    with pytest.raises(DataQualityError, match="包含 NaN 值"):
        DataQualityChecker.check_nan_inf(nan_tensor, "NanTensor")

def test_check_nan_inf_with_inf():
    """测试 check_nan_inf 对包含 Inf 的数据的处理"""
    inf_tensor = torch.tensor([[1.0, 2.0], [float('inf'), 4.0]])
    with pytest.raises(DataQualityError, match="包含 Inf 值"):
        DataQualityChecker.check_nan_inf(inf_tensor, "InfTensor")

def test_check_nan_inf_with_neg_inf():
    """测试 check_nan_inf 对包含 -Inf 的数据的处理"""
    neg_inf_tensor = torch.tensor([[1.0, 2.0], [3.0, float('-inf')]])
    with pytest.raises(DataQualityError, match="包含 Inf 值"): # isinf 也会捕获 -inf
        DataQualityChecker.check_nan_inf(neg_inf_tensor, "NegInfTensor")

# --- 测试 check_range ---

MAX_ABS_VALUE = 3.0

def test_check_range_valid():
    """测试 check_range 对有效数据的处理"""
    valid_tensor = torch.tensor([[-MAX_ABS_VALUE, 0.0], [1.5, MAX_ABS_VALUE]])
    try:
        DataQualityChecker.check_range(valid_tensor, MAX_ABS_VALUE, "ValidRangeTensor")
    except DataQualityError:
        pytest.fail("check_range 对有效数据不应抛出异常")

def test_check_range_positive_exceed():
    """测试 check_range 对超出正范围的数据的处理"""
    pos_exceed_tensor = torch.tensor([[1.0, 2.0], [MAX_ABS_VALUE + 0.1, 0.0]])
    with pytest.raises(DataQualityError, match=f"绝对值 > {MAX_ABS_VALUE}"):
        DataQualityChecker.check_range(pos_exceed_tensor, MAX_ABS_VALUE, "PosExceedTensor")

def test_check_range_negative_exceed():
    """测试 check_range 对超出负范围的数据的处理"""
    neg_exceed_tensor = torch.tensor([[-MAX_ABS_VALUE - 0.1, 2.0], [1.0, 0.0]])
    with pytest.raises(DataQualityError, match=f"绝对值 > {MAX_ABS_VALUE}"):
        DataQualityChecker.check_range(neg_exceed_tensor, MAX_ABS_VALUE, "NegExceedTensor")

# --- 测试 check_variance ---

MIN_VARIANCE = 1e-6
WINDOW_DIM = 1 # 假设数据形状为 [batch, window, features]，检查窗口内方差

def test_check_variance_valid():
    """测试 check_variance 对有效数据的处理"""
    # 构造一个在窗口维度上有足够方差的数据
    valid_tensor = torch.tensor([
        [[1.0, 2.0], [1.1, 2.1], [0.9, 1.9]], # batch 0
        [[3.0, 4.0], [3.1, 4.1], [2.9, 3.9]]  # batch 1
    ]) # shape: [2, 3, 2] -> [batch, window, features]
    # 检查窗口维度 (dim=1) 的方差
    try:
        DataQualityChecker.check_variance(valid_tensor, MIN_VARIANCE, "ValidVarTensor", check_dim=WINDOW_DIM)
    except DataQualityError:
        pytest.fail("check_variance 对有效数据不应抛出异常")

def test_check_variance_low():
    """测试 check_variance 对低方差数据的处理"""
    # 构造一个在窗口维度上方差过低的数据 (第二个特征方差为0)
    low_var_tensor = torch.tensor([
        [[1.0, 2.0], [1.1, 2.0], [0.9, 2.0]], # batch 0, feature 1 has var=0
        [[3.0, 4.0], [3.1, 4.1], [2.9, 3.9]]  # batch 1
    ]) # shape: [2, 3, 2]
    with pytest.raises(DataQualityError, match=f"< {MIN_VARIANCE}"):
        DataQualityChecker.check_variance(low_var_tensor, MIN_VARIANCE, "LowVarTensor", check_dim=WINDOW_DIM)

def test_check_variance_at_threshold():
    """测试 check_variance 对刚好在阈值上的方差的处理"""
    # 构造方差刚好等于阈值的数据 (需要仔细构造)
    # 假设需要方差为 1e-6，均值为 m
    # var = E[X^2] - (E[X])^2 = 1e-6
    # 简单起见，构造一个只有两个点的窗口，均值为0
    # (x1^2 + x2^2)/2 - 0 = 1e-6 => x1^2 + x2^2 = 2e-6
    # 让 x1 = sqrt(1e-6), x2 = -sqrt(1e-6)
    val = torch.sqrt(torch.tensor(MIN_VARIANCE))
    threshold_tensor = torch.tensor([
        [[0.0, val], [0.0, -val]], # batch 0, feature 1 has var = MIN_VARIANCE
        [[3.0, 4.0], [3.1, 4.1]]   # batch 1
    ]) # shape: [2, 2, 2]
    try:
        # 方差等于阈值，不应报错
        DataQualityChecker.check_variance(threshold_tensor, MIN_VARIANCE, "ThresholdVarTensor", check_dim=WINDOW_DIM)
    except DataQualityError:
        pytest.fail("check_variance 对刚好在阈值上的方差不应抛出异常")

def test_check_variance_empty_tensor():
    """测试 check_variance 对空张量的处理"""
    empty_tensor = torch.empty((0, 5, 2))
    try:
        # 不应报错，应该记录警告
        DataQualityChecker.check_variance(empty_tensor, MIN_VARIANCE, "EmptyVarTensor", check_dim=WINDOW_DIM)
    except DataQualityError:
        pytest.fail("check_variance 对空张量不应抛出异常")

def test_check_variance_dim_size_one():
    """测试 check_variance 对检查维度大小为1的处理"""
    single_window_tensor = torch.tensor([[[1.0, 2.0]], [[3.0, 4.0]]]) # shape: [2, 1, 2]
    try:
        # 不应报错，应该记录警告
        DataQualityChecker.check_variance(single_window_tensor, MIN_VARIANCE, "SingleWindowVarTensor", check_dim=WINDOW_DIM)
    except DataQualityError:
        pytest.fail("check_variance 对检查维度大小为1的张量不应抛出异常")

# --- 测试 check_data (集成测试) ---

def test_check_data_valid():
    """测试 check_data 对完全有效的数据的处理"""
    features = torch.randn(10, 5, 4) * 2 # 制造一些在 [-3, 3] 附近的值
    targets = torch.randn(10, 5, 1) * 1.5
    features = torch.clamp(features, -MAX_ABS_VALUE, MAX_ABS_VALUE) # 确保在范围内
    targets = torch.clamp(targets, -MAX_ABS_VALUE, MAX_ABS_VALUE)
    try:
        DataQualityChecker.check_data(features, targets, max_abs_value=MAX_ABS_VALUE, min_variance=MIN_VARIANCE)
    except DataQualityError:
        pytest.fail("check_data 对有效数据不应抛出异常")

def test_check_data_invalid_nan_features():
    """测试 check_data 对特征包含 NaN 的处理"""
    features = torch.randn(10, 5, 4)
    targets = torch.randn(10, 5, 1)
    features[2, 3, 1] = float('nan')
    with pytest.raises(DataQualityError, match="特征.*包含 NaN 值"):
        DataQualityChecker.check_data(features, targets, max_abs_value=MAX_ABS_VALUE, min_variance=MIN_VARIANCE)

def test_check_data_invalid_inf_targets():
    """测试 check_data 对目标包含 Inf 的处理"""
    features = torch.randn(10, 5, 4)
    targets = torch.randn(10, 5, 1)
    targets[5, 1, 0] = float('inf')
    with pytest.raises(DataQualityError, match="目标.*包含 Inf 值"):
        DataQualityChecker.check_data(features, targets, max_abs_value=MAX_ABS_VALUE, min_variance=MIN_VARIANCE)

def test_check_data_invalid_range_features():
    """测试 check_data 对特征超出范围的处理"""
    features = torch.randn(10, 5, 4)
    targets = torch.randn(10, 5, 1)
    features[1, 2, 3] = MAX_ABS_VALUE + 1.0
    with pytest.raises(DataQualityError, match=f"特征.*绝对值 > {MAX_ABS_VALUE}"):
        DataQualityChecker.check_data(features, targets, max_abs_value=MAX_ABS_VALUE, min_variance=MIN_VARIANCE)

def test_check_data_invalid_variance_targets():
    """测试 check_data 对目标方差过低的处理"""
    features = torch.randn(10, 5, 4)
    targets = torch.ones(10, 5, 1) * 2.0 # 方差为 0
    with pytest.raises(DataQualityError, match=f"目标.*方差过低.*< {MIN_VARIANCE}"):
        DataQualityChecker.check_data(features, targets, max_abs_value=MAX_ABS_VALUE, min_variance=MIN_VARIANCE)

def test_check_data_invalid_variance_features_dim1():
    """测试 check_data 对特征在窗口维度方差过低的处理"""
    features = torch.randn(10, 5, 4)
    targets = torch.randn(10, 5, 1)
    features[3, :, 2] = 1.5 # 第3个batch的第2个特征在窗口维度上方差为0
    with pytest.raises(DataQualityError, match=f"特征.*方差过低.*< {MIN_VARIANCE}"):
        DataQualityChecker.check_data(features, targets, max_abs_value=MAX_ABS_VALUE, min_variance=MIN_VARIANCE)
