"""配置管理模块 - 提供统一的配置控制与验证框架

此模块按功能领域组织配置类，包括：
- 基础配置 (base.py)
- 路径配置 (paths.py)
- 系统配置 (system.py)
- 数据配置 (data.py)
- 模型配置 (model.py)
- 训练配置 (training.py)
- 配置管理器 (manager.py)
"""

# 导入基础配置类
from src.utils.config.base import BaseConfig

# 导入数据配置类
from src.utils.config.data import (
    DataConfig,
    FeatureEngineeringConfig,
    FeaturesConfig,
    FeatureValidationConfig,
)

# 导入配置管理器
from src.utils.config.manager import ConfigManager, get_config

# 导入模型配置类
from src.utils.config.model import (
    BaseModelConfig,
    DiscriminatorConfig,
    GANModelConfig,
    # 移除DimensionAdapterConfig，因为维度适配功能已经集成到生成器和判别器中
    GeneratorConfig,
    SequenceStrategyConfig,
)

# 导入路径配置类
from src.utils.config.paths import PathsConfig

# 导入预测配置类
from src.utils.config.prediction import PredictionConfig

# 导入系统配置类
from src.utils.config.system import LoggingConfig, SystemConfig

# 导入训练配置类
from src.utils.config.training import (
    BalanceConfig,
    BatchSizeOptimizerConfig,
    CheckpointConfig,
    EarlyStoppingConfig,
    EvaluationConfig,
    LossConfig,
    LrBalancerConfig,
    MixedPrecisionConfig,
    OptimizerConfig,
    SchedulerConfig,
    TrainingConfig,
)

# 导出所有公共接口
__all__ = [
    'BalanceConfig',
    # 基础配置
    'BaseConfig',
    # 模型配置
    'BaseModelConfig',
    'BatchSizeOptimizerConfig',
    'CheckpointConfig',
    # 配置管理器
    'ConfigManager',
    # 数据配置
    'DataConfig',
    'DiscriminatorConfig',
    'EarlyStoppingConfig',
    'EvaluationConfig',
    'FeatureEngineeringConfig',
    'FeatureValidationConfig',
    'FeaturesConfig',
    'GANModelConfig',
    # 移除DimensionAdapterConfig，因为维度适配功能已经集成到生成器和判别器中
    'GeneratorConfig',
    'LoggingConfig',
    'LossConfig',
    'LrBalancerConfig',
    'MixedPrecisionConfig',
    'OptimizerConfig',
    # 路径配置
    'PathsConfig',
    # 预测配置
    'PredictionConfig',
    'SchedulerConfig',
    'SequenceStrategyConfig',
    # 系统配置
    'SystemConfig',
    # 训练配置
    'TrainingConfig',
    'get_config'
]
