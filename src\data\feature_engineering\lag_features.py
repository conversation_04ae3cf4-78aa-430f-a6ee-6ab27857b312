"""滞后特征生成器 - 计算时序滞后特征

此模块负责计算时序数据的滞后特征，即将数据向后移动k个时间步，用于捕捉时间序列的历史模式。
"""


import torch

from src.data.feature_engineering.base import BaseFeatureGenerator
from src.utils.config.manager import ConfigManager
from src.utils.logger import get_logger


class LagFeatureGenerator(BaseFeatureGenerator):
    """滞后特征生成器

    计算时序数据的滞后特征
    """

    def __init__(self, config: ConfigManager):
        """初始化滞后特征生成器

        Args:
            config: 配置管理器实例
        """
        if not isinstance(config, ConfigManager):
            raise TypeError("config参数必须为ConfigManager实例")

        self.config = config
        self.logger = get_logger(__name__)
        self.feature_names: list[str] = []
        self._feature_count = 0

        # 添加类型注解的类属性
        self._enabled: bool = False
        self._max_lag: int

        # 初始化配置
        self._check_config()

    def _check_config(self) -> None:
        """检查全局配置，仅设置启用状态"""
        self._enabled = False # 默认禁用
        try:
            if hasattr(self.config, 'feature_engineering'):
                feature_eng = self.config.feature_engineering
                if hasattr(feature_eng, 'enable') and feature_eng.enable:
                    # 全局特征工程已启用，此生成器可能被层级配置调用
                    self._enabled = True
                    self.logger.info("滞后特征生成器已启用（可能由层级配置驱动）。")
                else:
                    self.logger.info("特征工程全局开关已关闭，滞后特征生成器将禁用。")
            else:
                self.logger.warning("配置中缺少 feature_engineering 配置项，滞后特征生成器将禁用。")
        except AttributeError as e:
             # 配置项缺失是预期的，因为ConfigLoader会处理，这里只记录信息
             self.logger.info(f"访问配置项时出错 (可能缺失): {e}。滞后特征生成器默认禁用，除非被层级配置启用。")
             self._enabled = False # 明确禁用，除非层级配置覆盖
        except Exception as e:
             # 其他意外错误应明确抛出
             error_msg = f"检查全局滞后特征配置时发生意外错误: {e}"
             self.logger.error(error_msg)
             raise ValueError(error_msg) from e

    def generate(self, data: torch.Tensor, **kwargs) -> torch.Tensor:
        """生成滞后特征

        Args:
            data: 输入数据张量 [n_samples, n_features]
            **kwargs: 其他参数，必须包含 'max_lag' (int)

        Returns:
            torch.Tensor: 生成的滞后特征张量 [n_samples, n_features * max_lag]

        Raises:
            ValueError: 如果未提供有效的 'max_lag' 参数或计算失败
        """
        # Roo-Fix: 强制要求通过 kwargs 提供 max_lag
        if 'max_lag' not in kwargs:
            error_msg = "调用 generate 时必须通过 kwargs 提供 'max_lag' 参数。"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        max_lag_to_use = kwargs['max_lag']
        source = "kwargs" # 明确来源

        # 检查生成器是否启用 (基于全局配置)
        if not self.is_enabled:
            self.logger.info("滞后特征生成器(全局)已禁用，跳过。")
            return torch.empty((data.shape[0], 0), dtype=data.dtype, device=data.device)

        # 验证要使用的 max_lag
        if not isinstance(max_lag_to_use, int) or max_lag_to_use <= 0:
            error_msg = f"无效的最大滞后阶数 (来源: {source}): {max_lag_to_use}，必须为正整数"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.logger.info(f"开始生成滞后特征 (使用 {source} 的最大滞后阶数: {max_lag_to_use})")
        self.logger.debug(
            f"LagFeatureGenerator - 输入数据统计:\n"
            f"- 形状: {data.shape}\n"
            f"- 数据类型: {data.dtype}\n"
            f"- 设备: {data.device}\n"
            f"- 均值(每列): {data.mean(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 标准差(每列): {data.std(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最小值(每列): {data.min(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最大值(每列): {data.max(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 是否包含NaN: {torch.isnan(data).any().item()}\n"
            f"- 是否包含Inf: {torch.isinf(data).any().item()}"
        )

        try:
            n_samples, n_features = data.shape
            if torch.isinf(data).any():
                error_msg = "输入数据包含无穷大值，无法进行滞后特征计算。"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            lagged_features_list = []
            current_feature_names = [] # 使用局部变量存储本次生成的名称

            for k in range(1, max_lag_to_use + 1):
                # 创建滞后数据：向下移动 k 步，顶部用NaN填充
                self.logger.debug(f"Generating lag {k} features...")
                lagged_data = torch.full_like(data, float('nan'))
                if k < n_samples:
                    lagged_data[k:] = data[:-k]
                    self.logger.debug(f"Lag {k}: data shifted. Shape: {lagged_data.shape}")
                else:
                    # 如果滞后阶数大于或等于样本数，这是一个错误条件
                    error_msg = f"滞后阶数 ({k}) 不能大于或等于样本数 ({n_samples})。"
                    self.logger.error(error_msg) # 已有error日志
                    raise ValueError(error_msg)

                lagged_features_list.append(lagged_data)

                # 更新特征名称
                for i in range(n_features):
                    current_feature_names.append(f"lag{k}_feature{i}")

            if not lagged_features_list:
                 # 如果 max_lag_to_use > 0 但列表为空，说明循环未执行或出现意外情况
                 error_msg = f"未能成功生成任何滞后特征 (请求的最大滞后阶数: {max_lag_to_use})。"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)


            # 沿特征维度合并所有滞后特征
            combined_lags = torch.cat(lagged_features_list, dim=1)
            self._feature_count = combined_lags.shape[1]
            self.feature_names = current_feature_names # 更新实例变量

            self.logger.info(f"成功添加滞后特征 (1 到 {max_lag_to_use} 阶) | 新增维度: {combined_lags.shape}")
            # 注意：滞后特征会包含 NaN，这是预期的，后续步骤（如插值或模型处理）应处理它们
            return combined_lags

        except Exception as e:
            error_msg = f"生成滞后特征失败: {e!s}"
            self.logger.error(error_msg)
            raise ValueError(error_msg) from e

    def get_feature_names(self) -> list[str]:
        """获取生成的特征名称列表

        Returns:
            List[str]: 特征名称列表
        """
        return self.feature_names

    @property
    def feature_count(self) -> int:
        """获取生成的特征数量

        Returns:
            int: 特征数量
        """
        return self._feature_count

    @property
    def is_enabled(self) -> bool:
        """检查特征生成器是否启用

        Returns:
            bool: 是否启用
        """
        return self._enabled
