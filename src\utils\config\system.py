"""系统配置模块 - 管理系统资源和运行环境配置"""

from __future__ import annotations

import logging
from dataclasses import dataclass
from typing import Any


# 移除 BaseConfig 继承
# @dataclass
# class SystemConfig(BaseConfig):
@dataclass
class SystemConfig:
    """系统资源配置 (不再继承 BaseConfig)"""
    # 移除 Optional 和 = None，变为必需字段
    device: str
    cuda: dict[str, Any]
    memory: dict[str, Any]
    cache: dict[str, Any]
    # 移除 noise_dim 和 dimensions

    def __post_init__(self):
        """初始化后验证必需字段"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'device'): missing_fields.append('device')

        # 检查 cuda 结构
        if not hasattr(self, 'cuda') or not isinstance(self.cuda, dict):
            missing_fields.append('cuda (必须是字典)')
        elif 'enabled' not in self.cuda or 'device_id' not in self.cuda:
            missing_fields.append('cuda 必须包含 enabled 和 device_id 键')

        # 检查 memory 结构
        if not hasattr(self, 'memory') or not isinstance(self.memory, dict):
            missing_fields.append('memory (必须是字典)')
        # TODO: 添加 memory 内部结构的具体检查 (例如 'limit', 'strategy')

        # 检查 cache 结构
        if not hasattr(self, 'cache') or not isinstance(self.cache, dict):
            missing_fields.append('cache (必须是字典)')
        # TODO: 添加 cache 内部结构的具体检查 (例如 'enabled', 'size')

        if missing_fields:
            error_msg = f"系统配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

# 移除 BaseConfig 继承
# @dataclass
# class LoggingConfig(BaseConfig):
@dataclass
class LoggingConfig:
    """日志配置 (不再继承 BaseConfig)"""
    # 移除 Optional 和 = None，变为必需字段
    # default_level 和 level 合并为一个必需的 level 字段
    level: str
    format: str
    date_format: str
    save_history: bool
    metrics: list[str]
    module_levels: dict[str, str]
    performance_monitoring: dict[str, bool]
    handlers: dict[str, dict[str, Any]]

    # 移除向后兼容性的字段 file 和 console
    # file: Optional[Dict[str, Any]] = None
    # console: Optional[Dict[str, Any]] = None
    # 移除 noise_dim 和 dimensions

    def __post_init__(self):
        """初始化后验证必需字段"""
        from src.utils.logger import get_logger
        logger = get_logger(__name__)

        missing_fields = []

        # 检查必需字段是否存在 (移除 is None)
        if not hasattr(self, 'level'): missing_fields.append('level')
        if not hasattr(self, 'format'): missing_fields.append('format')
        if not hasattr(self, 'date_format'): missing_fields.append('date_format')
        if not hasattr(self, 'save_history'): missing_fields.append('save_history')

        # 检查 metrics 结构
        if not hasattr(self, 'metrics') or not isinstance(self.metrics, list):
            missing_fields.append('metrics (必须是列表)')

        # 检查 module_levels 结构
        if not hasattr(self, 'module_levels') or not isinstance(self.module_levels, dict):
            missing_fields.append('module_levels (必须是字典)')

        # 检查 performance_monitoring 结构，移除hasattr检查
        try:
            if not isinstance(self.performance_monitoring, dict):
                missing_fields.append('performance_monitoring (必须是字典)')
        except AttributeError:
            missing_fields.append('performance_monitoring (缺少属性)')

        # 检查 handlers 结构，移除hasattr检查
        try:
            if not isinstance(self.handlers, dict):
                missing_fields.append('handlers (必须是字典)')
        except AttributeError:
            missing_fields.append('handlers (缺少属性)')

        # 移除对 file 和 console 的检查
        # if self.file is None: ...
        # if self.console is None: ...

        if missing_fields:
            error_msg = f"日志配置缺失必需字段: {', '.join(missing_fields)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    def get_level(self) -> int:
        """获取日志级别的整数值"""
        levels = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL
        }
        # 直接使用必需的 level 字段
        level_str = self.level.upper()

        # 移除回退逻辑，如果级别无效则抛出错误
        if level_str not in levels:
             # 在需要时导入 get_logger
             from src.utils.logger import get_logger
             logger = get_logger(__name__)
             error_msg = f"无效的日志级别配置: '{self.level}'。有效选项: {list(levels.keys())}"
             logger.error(error_msg)
             raise ValueError(error_msg)

        return levels[level_str]

    # 移除 get_file_level 和 get_console_level 方法
    # 日志级别应通过 handlers 配置获取
    # def get_file_level(self) -> int: ...
    # def get_console_level(self) -> int: ...
