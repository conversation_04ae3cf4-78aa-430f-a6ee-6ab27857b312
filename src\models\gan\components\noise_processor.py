"""噪声处理模块 - 负责噪声编码和变换
模块路径: src/models/gan/components/noise_processor.py

继承自:
- src/models/base/base_module.py: BaseModule

协作模块:
- src/models/gan/components/noise_encoder.py
- src/models/gan/components/adaptive_noise.py

优化特性:
- 自适应噪声注入：条件噪声生成、分层噪声注入
- 预测不确定性建模：将噪声与预测不确定性关联
"""
from torch import Tensor

from src.models.base.base_module import BaseModule

from .adaptive_noise import AdaptiveNoiseModule
from .noise_encoder import NoiseEncoder


class NoiseProcessor(BaseModule):
    def __init__(self, noise_dim: int, output_dim: int):
        """初始化噪声处理器

        Args:
            noise_dim: 输入噪声维度
            output_dim: 输出维度

        Raises:
            ValueError: 如果任何维度参数小于等于0
        """
        super().__init__("NoiseProcessor")

        # 验证参数
        if noise_dim <= 0:
            error_msg = f"noise_dim必须为正数，当前值: {noise_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if output_dim <= 0:
            error_msg = f"output_dim必须为正数，当前值: {output_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.logger.info(f"初始化噪声处理器: noise_dim={noise_dim}, output_dim={output_dim}")

        self.encoder = NoiseEncoder(
            noise_dim=noise_dim,
            output_dim=output_dim,
            hidden_dim=output_dim  # 使用output_dim作为hidden_dim
        )

        # === 自适应噪声注入组件 ===
        # 自适应噪声模块 - 条件噪声生成和分层噪声注入
        self.adaptive_noise = AdaptiveNoiseModule(
            feature_dim=output_dim,  # 使用output_dim作为特征维度
            noise_dim=noise_dim,
            noise_levels=['feature', 'temporal', 'output']
        )

        # 应用权重初始化
        self.apply(self._init_weights)
        self.logger.info(f"噪声处理器初始化完成，参数数量: {self.count_parameters():,}")

    def forward(self, noise: Tensor) -> Tensor:
        """处理噪声输入

        Args:
            noise: [batch, noise_dim] 或 [batch, seq_len, noise_dim]

        Returns:
            编码后的噪声 [batch, seq_len, output_dim]
        """
        import time
        from datetime import datetime

        import torch

        start_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 噪声处理器开始处理 - 输入形状: {noise.shape}")

        # 验证输入张量
        validation_start = time.time()
        if noise.dim() not in [2, 3]:
            error_msg = f"噪声输入维度应为2或3，实际为{noise.dim()}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 输入验证完成 - 耗时: {time.time() - validation_start:.2f}秒")

        # 处理噪声
        encoding_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始噪声编码")
        encoded_noise = self.encoder(noise)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 噪声编码完成 - 形状: {encoded_noise.shape}, 耗时: {time.time() - encoding_start:.2f}秒")

        # === 自适应噪声增强处理 ===
        # 应用自适应噪声模块 - 生成条件噪声和不确定性信息
        adaptive_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始自适应噪声处理")
        enhanced_noise, noise_info = self.adaptive_noise(encoded_noise, noise)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 自适应噪声处理完成 - 形状: {enhanced_noise.shape}, 耗时: {time.time() - adaptive_start:.2f}秒")

        # 记录不确定性信息
        if 'uncertainty_scores' in noise_info:
            uncertainty_mean = noise_info['uncertainty_scores'].mean().item()
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 平均不确定性分数: {uncertainty_mean:.4f}")

        # 检查数值稳定性
        stability_start = time.time()
        self._check_numerical_stability(encoded_noise, "编码后噪声")
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 数值稳定性检查完成 - 耗时: {time.time() - stability_start:.2f}秒")

        # 检查内存使用情况
        if torch.cuda.is_available():
            mem_allocated = torch.cuda.memory_allocated() / (1024 ** 2)
            mem_reserved = torch.cuda.memory_reserved() / (1024 ** 2)
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] CUDA内存使用情况 - 已分配: {mem_allocated:.2f}MB, 已保留: {mem_reserved:.2f}MB")

        end_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 噪声处理器输出: shape={enhanced_noise.shape}, 总耗时: {end_time - start_time:.2f}秒")

        return enhanced_noise
