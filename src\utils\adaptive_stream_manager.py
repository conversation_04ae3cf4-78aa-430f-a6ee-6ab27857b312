"""自适应CUDA流管理器 - 根据GPU利用率动态调整流数量

本模块提供了一个自适应CUDA流管理器，可以根据GPU利用率动态调整CUDA流的数量，
以优化GPU资源利用效率。

主要功能：
1. 监控GPU利用率
2. 根据利用率动态调整流数量上限
3. 智能管理流的创建和释放
4. 提供详细的流使用统计和日志
"""

import threading
import time
from collections import deque
from dataclasses import dataclass
from typing import Any

import torch

from src.utils.logger import get_logger


@dataclass
class StreamInfo:
    """CUDA流信息"""
    stream: Any  # CUDA流对象
    name: str  # 流名称
    created_time: float  # 创建时间
    last_used_time: float  # 最后使用时间
    total_execution_time: float = 0.0  # 总执行时间
    execution_count: int = 0  # 执行次数
    is_busy: bool = False  # 是否正在执行任务


class AdaptiveStreamManager:
    """自适应CUDA流管理器 - 根据GPU利用率动态调整流数量"""

    def __init__(self, config: dict[str, Any]):
        """初始化自适应流管理器

        Args:
            config: 配置字典，包含CUDA和流相关配置
        """
        # 基本配置
        self.config = config
        self.logger = get_logger(__name__)

        # 流配置 - 直接使用传入的配置对象中的参数，不使用默认值
        if isinstance(config, dict) and 'streams' in config:
            # 如果是字典类型的配置
            if 'adaptive' not in config['streams']:
                raise ValueError("缺少必要的自适应流配置: 'adaptive'")

            adaptive_config = config['streams']['adaptive']

            if 'max_streams' not in config['streams']:
                raise ValueError("缺少必要的流配置参数: 'max_streams'")

            global_max_streams = config['streams']['max_streams']
            # 优先使用全局最大流数量
            adaptive_max_streams = global_max_streams

            if 'min_streams' not in adaptive_config:
                raise ValueError("缺少必要的自适应流配置参数: 'min_streams'")

            self.min_streams = adaptive_config['min_streams']
        else:
            # 不再支持旧的配置方式，强制要求使用字典类型的配置
            raise ValueError("配置必须是字典类型，并且包含'streams'键")

        self.max_streams = adaptive_max_streams
        self.current_limit = self.max_streams

        # 记录日志
        self.logger.debug(f"流数量限制设置: 全局最大流数量={global_max_streams}, 自适应流最大数量={adaptive_max_streams}, 当前限制={self.current_limit}")

        # 性能监控参数
        self.utilization_history = []  # 存储最近的GPU利用率

        # 直接使用传入的配置对象中的监控参数，不使用默认值
        if isinstance(config, dict) and 'streams' in config and 'adaptive' in config['streams']:
            # 如果是字典类型的配置
            adaptive_config = config['streams']['adaptive']

            # 检查必要的配置参数
            required_params = ['history_size', 'low_threshold', 'high_threshold', 'adjustment_step', 'monitoring_interval']
            for param in required_params:
                if param not in adaptive_config:
                    raise ValueError(f"缺少必要的自适应流配置参数: '{param}'")

            self.history_size = adaptive_config['history_size']
            self.low_threshold = adaptive_config['low_threshold']
            self.high_threshold = adaptive_config['high_threshold']
            self.adjustment_step = adaptive_config['adjustment_step']
            self.monitoring_interval = adaptive_config['monitoring_interval']
        else:
            # 不再支持旧的配置方式
            raise ValueError("配置必须是字典类型，并且包含'streams.adaptive'键")

        # 流跟踪
        self.streams: dict[str, StreamInfo] = {}  # {name: StreamInfo}
        self.stream_pool: deque[Any] = deque()  # 流池，用于复用
        self.stream_lock = threading.Lock()  # 流操作线程安全锁

        # 统计信息
        self.creation_count = 0  # 创建的流总数
        self.reuse_count = 0  # 复用的流总数
        self.adjustment_count = 0  # 调整次数

        # 启动监控线程
        self.monitoring_thread = None
        self.monitoring_active = False
        self._start_monitoring()

        self.logger.info(
            f"自适应CUDA流管理器初始化完成:\n"
            f"- 最小流数量: {self.min_streams}\n"
            f"- 最大流数量: {self.max_streams}\n"
            f"- 当前限制: {self.current_limit}\n"
            f"- 低利用率阈值: {self.low_threshold}%\n"
            f"- 高利用率阈值: {self.high_threshold}%\n"
            f"- 调整步长: {self.adjustment_step}\n"
            f"- 监控间隔: {self.monitoring_interval}秒"
        )

    def _start_monitoring(self):
        """启动GPU监控线程"""
        if self.monitoring_thread is not None and self.monitoring_thread.is_alive():
            self.logger.info("监控线程已在运行")
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True,
            name="AdaptiveStreamMonitor"
        )
        self.monitoring_thread.start()
        self.logger.info("自适应流监控线程已启动")

    def _stop_monitoring(self):
        """停止GPU监控线程"""
        self.monitoring_active = False
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=2.0)
            self.logger.info("自适应流监控线程已停止")

    def _monitoring_loop(self):
        """监控GPU利用率并调整流限制"""
        self.logger.debug("自适应流监控循环开始")
        while self.monitoring_active:
            try:
                # 获取当前GPU利用率
                utilization = self._get_gpu_utilization()

                # 更新历史记录
                self.utilization_history.append(utilization)
                if len(self.utilization_history) > self.history_size:
                    self.utilization_history.pop(0)

                # 只有在有足够历史数据时才调整
                if len(self.utilization_history) >= self.history_size:
                    avg_utilization = sum(self.utilization_history) / len(self.utilization_history)
                    self._adjust_stream_limit(avg_utilization)

                # 等待下一个监控周期
                time.sleep(self.monitoring_interval)

            except Exception as e:
                self.logger.error(f"流监控异常: {e!s}")
                time.sleep(self.monitoring_interval * 2)  # 出错后等待更长时间

    def _get_gpu_utilization(self) -> float:
        """获取当前GPU利用率

        Returns:
            float: GPU利用率百分比 (0-100)
        """
        try:
            if torch.cuda.is_available():
                # 尝试使用 torch.cuda.utilization()
                try:
                    return torch.cuda.utilization()  # 返回0-100的利用率
                except AttributeError:
                    # 如果 torch.cuda.utilization() 不存在，使用 nvidia-smi
                    import subprocess

                    self.logger.debug("torch.cuda.utilization() 不可用，尝试使用 nvidia-smi")

                    try:
                        result = subprocess.check_output(
                            ['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'],
                            universal_newlines=True
                        )
                        utilization = float(result.strip())
                        return utilization
                    except (subprocess.SubprocessError, ValueError) as e:
                        self.logger.warning(f"通过 nvidia-smi 获取GPU利用率失败: {e!s}")
                        # 使用内存利用率作为替代
                        memory_stats = torch.cuda.memory_stats()
                        if memory_stats:
                            allocated = memory_stats.get('allocated_bytes.all.current', 0)
                            total = torch.cuda.get_device_properties(0).total_memory
                            # 使用内存利用率作为GPU利用率的近似值
                            return (allocated / total) * 100
                        return 50  # 默认返回中等利用率
            return 0
        except Exception as e:
            self.logger.warning(f"获取GPU利用率失败: {e!s}")
            return 50  # 默认返回中等利用率

    def _adjust_stream_limit(self, utilization: float):
        """根据GPU利用率调整流限制

        Args:
            utilization: GPU利用率百分比 (0-100)
        """
        old_limit = self.current_limit

        if utilization > self.high_threshold:
            # 高利用率 - 减少流数量
            self.current_limit = max(self.min_streams,
                                    self.current_limit - self.adjustment_step)
        elif utilization < self.low_threshold:
            # 低利用率 - 增加流数量
            self.current_limit = min(self.max_streams,
                                    self.current_limit + self.adjustment_step)

        # 只在限制变化时记录日志
        if old_limit != self.current_limit:
            self.adjustment_count += 1
            self.logger.info(
                f"调整CUDA流限制: {old_limit} -> {self.current_limit} "
                f"(GPU利用率: {utilization:.1f}%, 调整次数: {self.adjustment_count})"
            )

    def create_stream(self, name: str) -> Any:
        """创建新的CUDA流，受当前限制约束

        Args:
            name: 流的名称

        Returns:
            torch.cuda.Stream: 创建或复用的CUDA流

        Raises:
            RuntimeError: 当无法创建流时抛出
        """
        with self.stream_lock:
            # 检查是否已存在同名流
            if name in self.streams:
                # 更新最后使用时间
                self.streams[name].last_used_time = time.time()
                self.logger.debug(f"复用已存在的流 '{name}'")
                return self.streams[name].stream

            # 检查是否达到当前限制
            if len(self.streams) >= self.current_limit:
                self.logger.warning(
                    f"已达到当前流限制({self.current_limit})，"
                    f"尝试复用现有流"
                )

                # 尝试从流池中获取
                if self.stream_pool:
                    stream = self.stream_pool.popleft()
                    self.reuse_count += 1
                    self.logger.info(f"从流池中获取流 '{name}' (复用计数: {self.reuse_count})")
                else:
                    # 查找最旧的非忙碌流
                    oldest_stream_name = None
                    oldest_time = float('inf')

                    for stream_name, stream_info in self.streams.items():
                        if not stream_info.is_busy and stream_info.last_used_time < oldest_time:
                            oldest_time = stream_info.last_used_time
                            oldest_stream_name = stream_name

                    if oldest_stream_name:
                        # 同步并复用最旧的流
                        try:
                            self.streams[oldest_stream_name].stream.synchronize()
                        except Exception as e:
                            self.logger.warning(f"同步流 '{oldest_stream_name}' 失败: {e!s}")

                        stream = self.streams[oldest_stream_name].stream
                        del self.streams[oldest_stream_name]
                        self.reuse_count += 1
                        self.logger.info(
                            f"复用最旧的流 '{oldest_stream_name}' -> '{name}' "
                            f"(复用计数: {self.reuse_count})"
                        )
                    else:
                        # 如果没有可复用的流，使用默认流
                        self.logger.warning("无可用流，使用默认流")
                        return torch.cuda.current_stream()
            else:
                # 创建新流
                try:
                    stream = torch.cuda.Stream()
                    self.creation_count += 1
                    self.logger.info(
                        f"创建新CUDA流 '{name}' "
                        f"(创建计数: {self.creation_count})"
                    )
                except Exception as e:
                    error_msg = f"创建新CUDA流失败: {e!s}"
                    self.logger.error(error_msg)
                    raise RuntimeError(error_msg)

            # 记录流信息
            now = time.time()
            self.streams[name] = StreamInfo(
                stream=stream,
                name=name,
                created_time=now,
                last_used_time=now
            )

            self.logger.debug(
                f"流 '{name}' 已注册 "
                f"(当前: {len(self.streams)}/{self.current_limit})"
            )
            return stream

    def release_stream(self, name: str) -> bool:
        """释放指定名称的流

        Args:
            name: 流的名称

        Returns:
            bool: 是否成功释放
        """
        with self.stream_lock:
            if name in self.streams:
                # 确保流上的操作完成
                try:
                    self.streams[name].stream.synchronize()
                except Exception as e:
                    self.logger.warning(f"同步流 '{name}' 失败: {e!s}")

                # 将流放入池中以便复用
                self.stream_pool.append(self.streams[name].stream)

                # 从字典中移除
                del self.streams[name]

                self.logger.debug(
                    f"释放CUDA流 '{name}' 到流池 "
                    f"(当前: {len(self.streams)}/{self.current_limit}, "
                    f"池大小: {len(self.stream_pool)})"
                )
                return True

            self.logger.warning(f"尝试释放不存在的流 '{name}'")
            return False

    def get_stream(self, name: str) -> Any | None:
        """获取指定名称的流

        Args:
            name: 流的名称

        Returns:
            Optional[torch.cuda.Stream]: 流对象，如果不存在则返回None
        """
        with self.stream_lock:
            if name in self.streams:
                # 更新最后使用时间
                self.streams[name].last_used_time = time.time()
                return self.streams[name].stream
            return None

    def get_stats(self) -> dict[str, Any]:
        """获取流管理器统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        with self.stream_lock:
            return {
                'current_limit': self.current_limit,
                'active_streams': len(self.streams),
                'pool_size': len(self.stream_pool),
                'creation_count': self.creation_count,
                'reuse_count': self.reuse_count,
                'adjustment_count': self.adjustment_count,
                'current_utilization': self._get_gpu_utilization(),
                'avg_utilization': sum(self.utilization_history) / max(1, len(self.utilization_history)),
                'stream_names': list(self.streams.keys())
            }

    def cleanup(self):
        """清理资源，停止监控线程"""
        self._stop_monitoring()
        with self.stream_lock:
            # 同步所有流
            for name, info in self.streams.items():
                try:
                    info.stream.synchronize()
                except Exception as e:
                    self.logger.warning(f"同步流 '{name}' 失败: {e!s}")

            # 清空流字典和池
            self.streams.clear()
            self.stream_pool.clear()

        self.logger.info("自适应流管理器已清理")
