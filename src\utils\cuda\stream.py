"""CUDA 流管理模块"""

import threading
import time
from collections import deque
from contextlib import contextmanager
from typing import Any

import torch

from src.utils.adaptive_stream_manager import AdaptiveStreamManager
from src.utils.logger import get_logger

from .device import cuda_device_manager
from .types import StreamEventInfo, StreamInfo


class CUDAStreamManager:
    """CUDA流管理器，负责流的创建、监控和管理"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """初始化流管理器"""
        self._logger = get_logger(__name__)

        # 流管理相关属性
        self._streams = {}  # 流字典 {name: StreamInfo}
        self._stream_pool = deque()  # 流池，用于复用
        self._stream_lock = threading.Lock()  # 流操作线程安全锁
        self._default_stream = None  # 默认流

        # 事件管理相关属性
        self._events = {}  # 事件字典 {name: StreamEventInfo}
        self._event_lock = threading.Lock()  # 事件操作线程安全锁

        # 监控相关属性
        self._stream_monitoring_enabled = False
        self._stream_monitoring_thread = None
        self._stream_monitoring_interval = 5  # 默认5秒
        self._stream_performance_data = {}  # 流性能数据
        self._max_streams = 16  # 默认最大流数量

        # 自适应流管理器
        self._adaptive_stream_manager = None

    def configure(self, stream_config: dict[str, Any]) -> None:
        """配置流管理器参数

        Args:
            stream_config: 流配置字典，必须包含以下字段：
                - max_streams: 最大流数量
                - enable_monitoring: 是否启用监控
                - monitoring_interval: 监控间隔（秒）
                - adaptive: 自适应流管理配置（可选）
        """
        if not cuda_device_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法配置流管理器"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

        try:
            # 检查必要的配置参数
            required_keys = ["max_streams", "enable_monitoring", "monitoring_interval"]
            for key in required_keys:
                if key not in stream_config:
                    error_msg = f"缺少必要的流配置参数: '{key}'"
                    self._logger.error(error_msg)
                    raise ValueError(error_msg)

            self._max_streams = int(stream_config["max_streams"])
            monitoring_enabled = bool(stream_config["enable_monitoring"])
            monitoring_interval = float(stream_config["monitoring_interval"])

            # 初始化自适应流管理器（如果配置了）
            if "adaptive" in stream_config and stream_config["adaptive"].get("enabled", False):
                # 创建包含完整配置的字典
                full_config = {"streams": stream_config}
                self._adaptive_stream_manager = AdaptiveStreamManager(full_config)
                self._logger.info(
                    f"已初始化自适应流管理器:\n"
                    f"- 最小流数量: {self._adaptive_stream_manager.min_streams}\n"
                    f"- 最大流数量: {self._adaptive_stream_manager.max_streams}"
                )
            else:
                self._adaptive_stream_manager = None

            self._logger.info(
                f"流管理器配置完成:\n"
                f"- 最大流数量: {self._max_streams}\n"
                f"- 流监控: {'启用' if monitoring_enabled else '禁用'}\n"
                f"- 监控间隔: {monitoring_interval}秒\n"
                f"- 自适应流管理: {'启用' if self._adaptive_stream_manager else '禁用'}"
            )

            # 如果启用了监控，启动监控线程
            if monitoring_enabled:
                self.start_monitoring(monitoring_interval)

        except Exception as e:
            error_msg = f"配置流管理器失败: {e!s}"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

    def create_stream(self, name: str) -> Any:
        """创建并返回一个新的CUDA流"""
        if not cuda_device_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法创建流"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

        with self._stream_lock:
            if name in self._streams:
                self._logger.debug(f"流 '{name}' 已存在，返回现有流")
                self._streams[name].last_used_time = time.time()
                return self._streams[name].stream

            # 使用自适应管理器或从池中获取流
            if self._adaptive_stream_manager:
                stream = self._adaptive_stream_manager.create_stream(name)
                self._logger.debug(f"使用自适应流管理器创建流 '{name}'")
            else:
                if len(self._streams) >= self._max_streams and not self._stream_pool:
                    self._cleanup_old_streams()

                try:
                    stream = (self._stream_pool.popleft()
                             if self._stream_pool
                             else torch.cuda.Stream())
                    self._logger.debug(f"创建新流 '{name}' (当前: {len(self._streams)}/{self._max_streams})")
                except Exception as e:
                    error_msg = f"创建CUDA流失败: {e!s}"
                    self._logger.error(error_msg)
                    raise RuntimeError(error_msg)

            # 记录流信息
            now = time.time()
            self._streams[name] = StreamInfo(
                stream=stream,
                name=name,
                created_time=now,
                last_used_time=now
            )
            return stream

    def _cleanup_old_streams(self):
        """清理旧的未使用流"""
        oldest_stream_name = None
        oldest_time = float('inf')

        for name, info in self._streams.items():
            if not info.is_busy and info.last_used_time < oldest_time:
                oldest_time = info.last_used_time
                oldest_stream_name = name

        if oldest_stream_name:
            self._logger.info(f"清理未使用的流: {oldest_stream_name}")
            stream = self._streams[oldest_stream_name].stream
            stream.synchronize()
            del self._streams[oldest_stream_name]
        else:
            error_msg = f"无法创建新流：所有 {self._max_streams} 个流都在使用中"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

    @contextmanager
    def stream_context(self, name: str):
        """创建流上下文"""
        start_time = time.time()
        stream = self.create_stream(name)

        try:
            with self._stream_lock:
                for info in self._streams.values():
                    if info.stream == stream:
                        info.is_busy = True
                        break

            with torch.cuda.stream(stream):
                yield stream

        finally:
            try:
                stream.synchronize()
                with self._stream_lock:
                    for info in self._streams.values():
                        if info.stream == stream:
                            info.is_busy = False
                            info.last_used_time = time.time()
                            info.execution_count += 1
                            info.total_execution_time += time.time() - start_time
                            break
            except Exception as e:
                self._logger.error(f"流上下文清理失败: {e!s}")

    def create_event(self, name: str, enable_timing: bool = False) -> Any:
        """创建CUDA事件"""
        if not cuda_device_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法创建事件"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

        with self._event_lock:
            if name in self._events:
                return self._events[name].event

            event = torch.cuda.Event(enable_timing=enable_timing)
            self._events[name] = StreamEventInfo(
                event=event,
                name=name,
                created_time=time.time()
            )
            return event

    def get_current_stream(self) -> Any:
        """获取当前CUDA流"""
        if not cuda_device_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法获取当前流"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)
        return torch.cuda.current_stream()

    def synchronize_stream(self, stream: Any) -> None:
        """同步指定的CUDA流

        Args:
            stream: 要同步的CUDA流
        """
        if not cuda_device_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法同步流"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

        try:
            stream.synchronize()
            self._logger.debug("流已同步")
        except Exception as e:
            error_msg = f"同步流失败: {e!s}"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

    def record_event(self, event: Any, stream: Any | None = None):
        """记录事件"""
        if not cuda_device_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法记录事件"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

        try:
            if stream is None:
                stream = torch.cuda.current_stream()
            event.record(stream)
            for event_info in self._events.values():
                if event_info.event == event:
                    event_info.recorded_time = time.time()
                    break
        except Exception as e:
            self._logger.error(f"记录事件失败: {e!s}")
            raise RuntimeError(f"记录事件失败: {e!s}")

    def get_stream_stats(self) -> dict[str, Any]:
        """获取流统计信息"""
        # 如果使用自适应流管理器，返回其统计信息
        if self._adaptive_stream_manager:
            return self._adaptive_stream_manager.get_stats()

        # 否则返回基本流统计信息
        with self._stream_lock:
            return {
                'total_streams': len(self._streams),
                'pool_size': len(self._stream_pool),
                'max_streams': self._max_streams,
                'stream_names': list(self._streams.keys()),
                'busy_streams': sum(1 for info in self._streams.values() if info.is_busy),
                'idle_streams': sum(1 for info in self._streams.values() if not info.is_busy),
                'total_events': len(self._events)
            }

    def get_stream_performance_data(self, name: str | None = None) -> dict[str, list[dict[str, Any]]]:
        """获取流性能数据"""
        if name:
            return {name: self._stream_performance_data.get(name, [])}
        return self._stream_performance_data

    def release_stream(self, stream_or_name: str | Any) -> bool:
        """释放指定的流

        Args:
            stream_or_name: 流的名称或流对象

        Returns:
            bool: 是否成功释放
        """
        with self._stream_lock:
            # 如果是自适应流管理器且传入的是字符串名称
            if self._adaptive_stream_manager and isinstance(stream_or_name, str):
                return self._adaptive_stream_manager.release_stream(stream_or_name)

            # 处理字符串名称
            if isinstance(stream_or_name, str):
                name = stream_or_name
                if name in self._streams:
                    # 确保流上的操作完成
                    try:
                        self._streams[name].stream.synchronize()
                    except Exception as e:
                        self._logger.warning(f"同步流 '{name}' 失败: {e!s}")

                    # 将流放入池中以便复用
                    self._stream_pool.append(self._streams[name].stream)

                    # 从字典中移除
                    del self._streams[name]

                    self._logger.debug(
                        f"释放CUDA流 '{name}' 到流池 "
                        f"(当前: {len(self._streams)}/{self._max_streams}, "
                        f"池大小: {len(self._stream_pool)})"
                    )
                    return True

                self._logger.warning(f"尝试释放不存在的流 '{name}'")
                return False

            # 处理流对象
            else:
                stream = stream_or_name
                for name, info in list(self._streams.items()):
                    if info.stream == stream:
                        try:
                            stream.synchronize()
                        except Exception as e:
                            self._logger.warning(f"同步流 '{name}' 失败: {e!s}")

                        self._stream_pool.append(stream)
                        del self._streams[name]

                        self._logger.debug(
                            f"释放CUDA流对象 (名称: '{name}') 到流池 "
                            f"(当前: {len(self._streams)}/{self._max_streams}, "
                            f"池大小: {len(self._stream_pool)})"
                        )
                        return True

                self._logger.warning("尝试释放未注册的流对象")
                return False

    def start_monitoring(self, interval: float = 5.0):
        """启动流监控"""
        if not cuda_device_manager.is_cuda_available:
            self._logger.warning("CUDA不可用，无法启动流监控")
            return

        if self._stream_monitoring_thread and self._stream_monitoring_thread.is_alive():
            self._logger.info("流监控已在运行")
            return

        self._stream_monitoring_interval = interval
        self._stream_monitoring_enabled = True
        self._stream_monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self._stream_monitoring_thread.start()
        self._logger.info(f"启动流监控，间隔: {interval}秒")

    def stop_monitoring(self):
        """停止流监控"""
        self._stream_monitoring_enabled = False
        if self._stream_monitoring_thread:
            self._stream_monitoring_thread.join(timeout=2)
        self._logger.info("流监控已停止")

        # 如果有自适应流管理器，也停止其监控
        if self._adaptive_stream_manager:
            try:
                self._adaptive_stream_manager.cleanup()
                self._logger.info("自适应流管理器已清理")
            except Exception as e:
                self._logger.warning(f"清理自适应流管理器时出错: {e!s}")

    def _monitoring_loop(self):
        """监控循环"""
        while self._stream_monitoring_enabled:
            try:
                with self._stream_lock:
                    for name, info in self._streams.items():
                        self._stream_performance_data.setdefault(name, []).append({
                            'timestamp': time.time(),
                            'execution_count': info.execution_count,
                            'total_execution_time': info.total_execution_time,
                            'is_busy': info.is_busy
                        })

                        # 限制历史数据量
                        if len(self._stream_performance_data[name]) > 100:
                            self._stream_performance_data[name] = (
                                self._stream_performance_data[name][-100:]
                            )

                time.sleep(self._stream_monitoring_interval)
            except Exception as e:
                self._logger.error(f"流监控循环出错: {e!s}")
                time.sleep(1.0)


# 全局单例
try:
    cuda_stream_manager = CUDAStreamManager()
except Exception as e:
    logger = get_logger("CUDAStreamManager")
    logger.error(f"CUDA流管理器初始化失败: {e!s}")
    raise RuntimeError(f"CUDA流管理器初始化失败: {e!s}")
