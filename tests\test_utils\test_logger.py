"""
测试基础设施：日志模块使用规范验证
模块路径：src/utils/logger.py
测试重点：
1. 验证LoggerFactory正确初始化
2. 验证日志级别使用规范
3. 验证异常日志记录规范
4. 验证敏感信息过滤
"""

from unittest.mock import MagicMock, patch

from src.utils.logger import LoggerFactory


class TestLoggerUsage:
    @patch('builtins.open')
    def test_logger_initialization(self, mock_open):
        """测试日志模块初始化规范"""
        mock_file = MagicMock()
        mock_open.return_value = mock_file

        logger = LoggerFactory().get_logger('test_module') # Call on instance
        assert logger is not None
        mock_open.assert_called_once()

    def test_log_level_usage(self):
        """测试日志级别使用规范"""
        logger = LoggerFactory().get_logger('test_module') # Call on instance
        with patch.object(logger, 'log') as mock_log:
            logger.debug("debug message")
            logger.info("info message")
            logger.warning("warning message")

            mock_log.assert_any_call('DEBUG', 'debug message')
            mock_log.assert_any_call('INFO', 'info message')
            mock_log.assert_any_call('WARNING', 'warning message')

    def test_exception_logging(self):
        """测试异常日志规范"""
        logger = LoggerFactory().get_logger('test_module') # Call on instance
        try:
            raise ZeroDivisionError("Simulating division by zero for test") # Explicitly raise error
        except Exception as e:
            with patch.object(logger, 'error') as mock_error:
                logger.exception("operation failed", exc_info=e)
                mock_error.assert_called_once()
                assert "ZeroDivisionError" in str(mock_error.call_args[0][1])
