"""测试层级间空值处理机制

测试FeatureManager中的_handle_interlayer_nan方法是否能正确处理层级间的NaN值传播问题。
"""

import unittest
from unittest.mock import Mock, patch

import torch

from src.data.feature_engineering.feature_manager import FeatureManager
from src.utils.config.manager import ConfigManager
from src.utils.logger import get_logger


class TestInterlayerNaNHandling(unittest.TestCase):
    """测试层级间空值处理机制"""

    def setUp(self):
        """设置测试环境"""
        self.logger = get_logger(__name__)

        # 创建模拟配置
        self.mock_config = Mock(spec=ConfigManager)
        self.mock_config.feature_engineering = Mock()
        self.mock_config.feature_engineering.enable = True
        self.mock_config.feature_engineering.columns = Mock()
        self.mock_config.feature_engineering.columns.time_features = ['date']
        self.mock_config.feature_engineering.columns.categorical = []

        # 添加时间预处理配置
        self.mock_config.feature_engineering.time_preprocessing = Mock()
        self.mock_config.feature_engineering.time_preprocessing.features_to_extract = [
            'year', 'month', 'day', 'dayofweek'
        ]

        self.mock_config.data = Mock()
        self.mock_config.data.feature_dim = None
        self.mock_config.data.target = 'target'

        # 创建FeatureManager实例，只patch初始化方法
        with patch.object(FeatureManager, '_initialize_generators'):
            with patch.object(FeatureManager, '_configure_encoder'):
                # 还需要patch TimeFeatureGenerator的初始化
                with patch('src.data.feature_engineering.feature_manager.TimeFeatureGenerator') as mock_time_gen:
                    mock_time_gen.return_value.is_enabled = False  # 简化测试
                    self.feature_manager = FeatureManager(self.mock_config)

    def test_handle_interlayer_nan_no_nan(self):
        """测试无NaN值的情况"""
        # 创建无NaN的测试数据
        data = torch.tensor([[1.0, 2.0, 3.0],
                            [4.0, 5.0, 6.0],
                            [7.0, 8.0, 9.0]], dtype=torch.float32)

        result = self.feature_manager._handle_interlayer_nan(data, level=1)

        # 验证结果
        self.assertTrue(torch.equal(result, data))
        self.assertFalse(torch.isnan(result).any())

    def test_handle_interlayer_nan_with_nan(self):
        """测试包含NaN值的情况"""
        # 创建包含NaN的测试数据
        data = torch.tensor([[1.0, float('nan'), 3.0],
                            [4.0, 5.0, float('nan')],
                            [7.0, 8.0, 9.0]], dtype=torch.float32)

        result = self.feature_manager._handle_interlayer_nan(data, level=1)

        # 验证结果：应该没有NaN值
        self.assertFalse(torch.isnan(result).any())
        # 验证形状保持不变
        self.assertEqual(result.shape, data.shape)

    def test_handle_interlayer_nan_forward_fill(self):
        """测试前向填充逻辑"""
        # 创建需要前向填充的数据，NaN比例低于30%
        data = torch.tensor([[float('nan'), 2.0, 3.0, 4.0],
                            [1.0, float('nan'), 6.0, 7.0],
                            [4.0, 5.0, float('nan'), 8.0]], dtype=torch.float32)

        result = self.feature_manager._handle_interlayer_nan(data, level=1)

        # 验证结果
        self.assertFalse(torch.isnan(result).any())
        # 第一列第一个值应该被第二行的值填充
        self.assertEqual(result[0, 0].item(), 1.0)
        # 第二列第二个值应该被前一个值填充
        self.assertEqual(result[1, 1].item(), 2.0)

    def test_handle_interlayer_nan_all_nan_column(self):
        """测试整列都是NaN的情况"""
        # 创建整列都是NaN的数据，但总体NaN比例低于30%
        data = torch.tensor([[1.0, float('nan'), 3.0, 4.0, 5.0],
                            [4.0, float('nan'), 6.0, 7.0, 8.0],
                            [7.0, float('nan'), 9.0, 10.0, 11.0]], dtype=torch.float32)

        result = self.feature_manager._handle_interlayer_nan(data, level=1)

        # 验证结果
        self.assertFalse(torch.isnan(result).any())
        # 整列NaN应该被填充为0
        self.assertTrue(torch.all(result[:, 1] == 0.0))

    def test_handle_interlayer_nan_high_ratio_error(self):
        """测试NaN比例过高时抛出异常"""
        # 创建NaN比例过高的数据（超过30%）
        data = torch.tensor([[float('nan'), float('nan'), 3.0],
                            [float('nan'), float('nan'), 6.0],
                            [float('nan'), float('nan'), 9.0]], dtype=torch.float32)

        # 应该抛出ValueError
        with self.assertRaises(ValueError) as context:
            self.feature_manager._handle_interlayer_nan(data, level=1)

        self.assertIn("NaN值比例过高", str(context.exception))

    def test_handle_interlayer_nan_device_consistency(self):
        """测试设备一致性"""
        if torch.cuda.is_available():
            # 创建CUDA张量
            data = torch.tensor([[1.0, float('nan'), 3.0],
                                [4.0, 5.0, 6.0]], dtype=torch.float32, device='cuda')

            result = self.feature_manager._handle_interlayer_nan(data, level=1)

            # 验证设备一致性
            self.assertEqual(result.device, data.device)
            self.assertFalse(torch.isnan(result).any())

    def test_handle_interlayer_nan_empty_tensor(self):
        """测试空张量的情况"""
        # 创建空张量
        data = torch.empty((0, 3), dtype=torch.float32)

        result = self.feature_manager._handle_interlayer_nan(data, level=1)

        # 验证结果
        self.assertEqual(result.shape, data.shape)
        self.assertFalse(torch.isnan(result).any())

    def test_handle_interlayer_nan_single_row(self):
        """测试单行数据的情况"""
        # 创建单行数据，NaN比例低于30%
        data = torch.tensor([[float('nan'), 2.0, 3.0, 4.0, 5.0]], dtype=torch.float32)

        result = self.feature_manager._handle_interlayer_nan(data, level=1)

        # 验证结果
        self.assertFalse(torch.isnan(result).any())
        # 单行中的NaN应该被同行的有效值或0填充
        self.assertEqual(result.shape, data.shape)


if __name__ == '__main__':
    unittest.main()
