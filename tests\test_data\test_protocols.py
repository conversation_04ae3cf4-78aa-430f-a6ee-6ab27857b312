"""协议接口测试模块

相关模块:
1. 被测试模块:
   - src/data/protocol.py: 定义了数据集相关的协议接口
"""

from typing import Any
from unittest.mock import MagicMock

import pytest
import torch

# 导入需要测试的协议
from src.data.protocol import DatasetProtocol, WindowDataset


@pytest.mark.batch1  # 协议接口测试
class TestDatasetProtocols:
    """测试数据相关的协议接口"""

    def test_dataset_protocol_interface(self):
        """测试 DatasetProtocol 接口"""
        # 创建一个模拟对象，声称实现了协议
        mock_dataset = MagicMock(spec=DatasetProtocol)

        # 验证协议要求的方法是否存在
        assert hasattr(mock_dataset, '__len__')
        assert hasattr(mock_dataset, '__getitem__')

        # 模拟方法调用并验证签名（虽然 mock 不强制签名，但这是最佳实践）
        mock_dataset.__len__.return_value = 10
        mock_dataset.__getitem__.return_value = {'data': torch.tensor(1)}

        assert mock_dataset.__len__() == 10
        item = mock_dataset.__getitem__(0)
        assert isinstance(item, dict)
        mock_dataset.__getitem__.assert_called_with(0)

        # 使用 isinstance 检查运行时协议遵循 (需要 @runtime_checkable)
        class MockImplDataset:
            def __len__(self) -> int:
                return 5
            def __getitem__(self, index: int) -> dict[str, Any]:
                if index >= 5:
                    raise IndexError
                return {'value': index}

        impl_dataset = MockImplDataset()
        assert isinstance(impl_dataset, DatasetProtocol)
        assert len(impl_dataset) == 5
        assert impl_dataset[0] == {'value': 0}

    def test_window_dataset_protocol_interface(self):
        """测试 WindowDataset 接口"""
        # 创建一个模拟对象，声称实现了协议
        # 使用 PropertyMock 来正确模拟属性
        from unittest.mock import PropertyMock
        mock_window_dataset = MagicMock(spec=WindowDataset)

        # 为 feature_dim 属性设置 PropertyMock
        type(mock_window_dataset).feature_dim = PropertyMock(return_value=20)

        # 验证协议要求的属性和方法是否存在
        assert hasattr(mock_window_dataset, 'feature_dim')
        assert not callable(mock_window_dataset.feature_dim) # 验证是属性
        assert hasattr(mock_window_dataset, '__len__')
        assert hasattr(mock_window_dataset, '__getitem__')

        # 模拟方法调用
        mock_window_dataset.__len__.return_value = 50
        mock_window_dataset.__getitem__.return_value = {
            'features': torch.randn(10, 20), # window_size=10, feature_dim=20
            'target': torch.randn(10, 1)
        }

        assert mock_window_dataset.feature_dim == 20
        assert mock_window_dataset.__len__() == 50
        item = mock_window_dataset.__getitem__(5)
        assert isinstance(item, dict)
        assert 'features' in item
        assert 'target' in item
        mock_window_dataset.__getitem__.assert_called_with(5)

        # 使用 isinstance 检查运行时协议遵循 (需要 @runtime_checkable)
        class MockImplWindowDataset:
            def __init__(self, dim):
                self._feature_dim = dim
            @property
            def feature_dim(self) -> int:
                return self._feature_dim
            def __len__(self) -> int:
                return 15
            def __getitem__(self, index: int) -> dict[str, torch.Tensor]:
                if index >= 15:
                    raise IndexError
                return {
                    'features': torch.randn(5, self._feature_dim),
                    'target': torch.randn(5, 1)
                }

        impl_window_dataset = MockImplWindowDataset(dim=8)
        assert isinstance(impl_window_dataset, WindowDataset)
        assert impl_window_dataset.feature_dim == 8
        assert len(impl_window_dataset) == 15
        assert 'features' in impl_window_dataset[0]
