"""数据加载器模块 - 提供统一的数据加载和处理接口

相关模块:
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理器，负责加载和管理系统配置参数
   - src/utils/cuda_manager.py: CUDA资源管理器，处理GPU内存分配和优化
   - src/utils/logger.py: 日志管理系统，提供统一的日志记录功能
   - src/utils/cuda_manager.py: 监控管理器，提供资源监控和性能追踪

2. 共用模块:
   - src/data/preprocessing/*: 预处理相关模块
   - src/data/time_series_dataset.py: 时间序列数据集
   - src/data/window_dataset.py: 窗口数据集
   - src/data/FeatureSelector.py: 特征选择器
"""

from __future__ import annotations

import queue
import threading
from collections.abc import Iterator, Sized
from pathlib import Path
from typing import (
    TYPE_CHECKING,
    Any,
    Protocol,
    cast,
    runtime_checkable,
)

import pandas as pd
import torch
from torch.utils.data import DataLoader as TorchDataLoader

# 导入协议
from src.data.protocol import TimeSeriesDatasetProtocol
from src.data.standardization import StandardizationProtocol
from src.data.windowed_time_series import TimeSeriesWindowDataset
from src.utils.config_manager import ConfigManager
from src.utils.cuda.manager import CUDAManager
from src.utils.logger import get_logger


@runtime_checkable
class TimeSeriesDataLoaderProtocol(Protocol):
    """时间序列数据加载器协议"""

    @property
    def dataset(self) -> TimeSeriesDatasetProtocol:
        """获取底层数据集"""
        ...

    @property
    def batch_size(self) -> int:
        """获取批次大小"""
        ...

    def __iter__(self) -> Iterator[dict[str, torch.Tensor]]:
        """返回数据迭代器
        Returns:
            Iterator[Dict[str, torch.Tensor]]: 返回包含特征和目标张量的字典迭代器
        """
        ...

    def __len__(self) -> int:
        """返回批次数量"""
        ...

if TYPE_CHECKING:
    from src.data.windowed_time_series import TimeSeriesWindowDataset


class DataProcessError(Exception):
    """数据处理相关异常基类"""
    pass

class DataFormatError(DataProcessError):
    """数据格式异常"""
    pass

class BatchCalculationError(DataProcessError):
    """批处理计算异常"""
    pass

class TimeSeriesDataLoader(TimeSeriesDataLoaderProtocol):
    """增强的时序数据加载器，实现TimeSeriesDataLoaderProtocol协议"""

    def __init__(
        self,
        config: ConfigManager,
        dataset: TimeSeriesWindowDataset | None = None,
        shuffle: bool = False,
        num_workers: int = 0
    ) -> None:
        """初始化时序数据加载器

        职责:
        1. 验证输入参数
        2. 设置基本参数
        3. 初始化数据处理组件
        4. 准备资源管理

        Args:
            config: 配置管理器实例，必须包含data相关配置
            dataset: 可选的数据集对象
            shuffle: 是否打乱数据顺序
            num_workers: 数据加载工作进程数

        Raises:
            TypeError: 如果config不是ConfigManager实例
            ValueError: 如果关键配置缺失
        """
        # 参数验证
        if not isinstance(config, ConfigManager):
            raise TypeError(f"需要ConfigManager实例，实际得到: {type(config)}")

        # 基本参数设置
        self.config = config
        self._dataset: TimeSeriesWindowDataset | None = dataset
        self.shuffle = shuffle
        # 验证并设置 batch_size
        batch_size_config = getattr(self.config.data, 'batch_size', None)
        if batch_size_config is None:
            raise ValueError("配置中缺少 data.batch_size")
        try:
            self._batch_size = int(batch_size_config)
            if self._batch_size <= 0:
                raise ValueError(f"batch_size必须大于0，但获取到的是: {self._batch_size}")
        except (ValueError, TypeError) as e:
            raise ValueError(f"无效的 batch_size 配置: {batch_size_config} - {e}") from e

        self.logger = get_logger(self.__class__.__name__)
        self.num_workers = max(0, int(num_workers))

        # 核心组件初始化
        self._init_components()
        self._init_resource_management()

    def load_data(self) -> dict[str, torch.Tensor | pd.Series | list[str]]:
        """加载原始数据，分离特征、目标和日期，并转换为适当类型

        Returns:
            Dict[str, Union[torch.Tensor, pd.Series, List[str]]]: 包含'features', 'targets', 'dates', 'feature_names'的字典

        Raises:
            DataProcessError: 如果数据加载失败
        """
        try:
            # 获取数据路径，移除getattr回退
            try:
                data_path = self.config.data.data_path
                if not data_path:
                    raise ValueError("数据路径为空")
            except AttributeError as e:
                raise ValueError("配置中缺少数据路径 data.data_path") from e

            # 验证数据文件
            data_file = Path(data_path)
            if not data_file.exists():
                raise FileNotFoundError(f"数据文件不存在: {data_path}")
            if data_file.stat().st_size == 0:
                raise ValueError(f"数据文件为空: {data_path}")

            # 加载数据
            data = pd.read_csv(data_path)
            if len(data) == 0:
                raise ValueError("数据文件没有有效行")

            # --- 开始时间过滤逻辑 (使用 load_period 参数) ---
            date_col = 'date' # 定义日期列名
            from src.optimization.exceptions import ConfigurationError  # 提前导入

            if date_col not in data.columns:
                # 强制要求日期列存在
                raise ConfigurationError(f"数据中缺少必需的日期列 '{date_col}'")

            # 确保 'date' 列是 datetime 类型
            data[date_col] = pd.to_datetime(data[date_col])
            original_min_date = data[date_col].min()
            original_max_date = data[date_col].max()
            self.logger.info(f"原始数据时间范围: {original_min_date.date()} 到 {original_max_date.date()}")

            # 检查并解析 load_period 参数
            load_period_str = getattr(self.config.data, 'load_period', None)
            if not load_period_str:
                raise ConfigurationError("配置 data.load_period 未设置或为空，训练需要此配置。")

            try:
                self.logger.info(f"检测到配置 data.load_period: '{load_period_str}'")

                # 解析日期范围格式 'start_date/end_date'
                if '/' in load_period_str:
                    start_date_str, end_date_str = load_period_str.split('/')
                    start_date = pd.to_datetime(start_date_str)
                    end_date = pd.to_datetime(end_date_str)
                else:
                    # 如果不是范围格式，则假设是单一日期，用作开始日期
                    start_date = pd.to_datetime(load_period_str)
                    end_date = original_max_date if isinstance(original_max_date, pd.Timestamp) else pd.to_datetime(original_max_date)

                # 验证解析后的日期
                if pd.isna(start_date):
                    raise ValueError("解析后的 start_date 为 NaT")
                if pd.isna(end_date):
                    raise ValueError("解析后的 end_date 为 NaT")
                if start_date > end_date:
                    raise ValueError(f"起始日期 ({start_date.date()}) 不能晚于结束日期 ({end_date.date()})")

                # 应用过滤
                num_before = len(data)
                data = data[(data[date_col] >= start_date) & (data[date_col] <= end_date)].copy()
                num_after = len(data)

                if num_after == 0:
                    raise ValueError(f"使用 load_period 过滤后没有剩余数据。范围: [{start_date.date()}] - [{end_date.date()}]")

                self.logger.info(f"已应用 load_period 过滤: [{start_date.date()}] - [{end_date.date()}]")
                self.logger.info(f"过滤前样本数: {num_before}, 过滤后样本数: {num_after}")

            except Exception as e:
                # 捕获所有在日期处理和过滤中发生的错误
                error_msg = f"处理 data.load_period ('{load_period_str}') 或进行日期过滤时出错: {e}"
                self.logger.error(error_msg, exc_info=True)
                raise ConfigurationError(error_msg) from e
            # --- 结束时间过滤逻辑 ---

            # 确保日期列是 datetime 类型 (如果过滤成功，这一步是多余的，但保留以防万一)
            date_col = 'date'
            if date_col in data.columns and not pd.api.types.is_datetime64_any_dtype(data[date_col]): # 检查是否还不是 datetime
                    try:
                        data[date_col] = pd.to_datetime(data[date_col])
                        self.logger.info(f"已确保日期列 '{date_col}' 为 datetime 类型。")
                    except Exception as e:
                        # 如果在这里转换失败，可能意味着数据有问题，需要更强的警告或错误
                        self.logger.error(f"关键步骤：无法将列 '{date_col}' 转换为日期类型: {e!s}。后续处理可能失败。")
                        # 考虑是否应该在此处引发异常，因为日期列对于时间序列至关重要
                        # raise DataFormatError(f"无法处理日期列 '{date_col}': {e}") from e
                # else: # 如果已经是 datetime，则无需记录日志，因为过滤步骤已记录
                #    self.logger.debug(f"日期列 '{date_col}' 已是 datetime 类型。")


            # 检查缺失值 (在过滤之后检查更合适)
            if data.isnull().values.any():
                raise ValueError("数据包含缺失值，请先进行数据清洗")

            # 记录数据样本（前3行和后3行）
            self.logger.info(
                f"\n=== 数据样本 ===\n"
                f"前3行:\n{data.head(3)}\n\n"
                f"后3行:\n{data.tail(3)}"
            )

            # 验证特征列数 (规则 52: 移除或调整硬编码检查以提高通用性)
            # 原始检查: 要求必须有21列 (日期+20数值)
            # if len(data.columns) != 21:
            #     raise ValueError(f"数据列数应为21列(日期+20个数值列)，实际为{len(data.columns)}列")
            # 调整后的检查: 确保目标列存在，并至少有一个其他数值特征列
            # 强制要求 data.target 在配置中存在，移除hasattr检查
            try:
                target_col_name = self.config.data.target
            except AttributeError as e:
                raise ValueError("配置中缺少 data.target") from e
            if target_col_name not in data.columns:
                raise ValueError(f"数据中缺少目标列 '{target_col_name}'")
            # 检查是否存在日期列（假设为'date'）
            if 'date' not in data.columns:
                 self.logger.warning("数据中缺少 'date' 列，将无法提取日期信息。")
            # 检查是否存在除日期和目标列之外的数值列
            numeric_feature_cols = data.select_dtypes(include=['number']).columns.difference([target_col_name, 'date'])
            if len(numeric_feature_cols) == 0:
                 raise ValueError("数据中未能找到除日期和目标列之外的任何数值特征列。")
            self.logger.info(f"数据列数检查通过：总列数={len(data.columns)}, 目标列='{target_col_name}', 数值特征数={len(numeric_feature_cols)}")

            # 1. 目标列识别
            if not hasattr(self.config.data, 'target'):
                error_msg = "配置中缺少 data.target 配置项"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            target_col = self.config.data.target
            if target_col not in data.columns:
                raise ValueError(f"目标列 '{target_col}' 不存在于数据中")
            # 使用 numpy.array() 方法进行转换，避免类型错误
            import numpy as np
            targets = torch.tensor(np.array(data[target_col]), dtype=torch.float32).unsqueeze(1)

            # 2. 日期列识别
            # 直接使用确定的日期列名
            date_col = 'date'
            if date_col not in data.columns:
                raise ValueError(f"日期列 '{date_col}' 不存在于数据中")

            # 尝试转换日期列
            try:
                dates = pd.to_datetime(data[date_col])
                self.logger.info(f"成功转换日期列 '{date_col}'，日期范围: {dates.min()} 至 {dates.max()}")

                # 检查日期是否按顺序排列
                is_sorted = dates.is_monotonic_increasing
                if not is_sorted:
                    self.logger.warning("日期列不是按升序排列的，这可能会影响时间序列分析")

                # 检查日期间隔是否一致
                date_diffs = dates.diff().dropna()
                unique_diffs = date_diffs.unique()
                if len(unique_diffs) > 1:
                    self.logger.warning(f"日期间隔不一致，检测到 {len(unique_diffs)} 种不同的间隔")
            except Exception as e:
                error_msg = f"日期列 '{date_col}' 转换失败: {e!s}"
                self.logger.error(error_msg)
                raise ValueError(error_msg) from e

            # 3. 特征列识别 (除去目标列和日期列的所有列)
            feature_cols = [col for col in data.columns if col not in [target_col, date_col]]
            if not feature_cols:
                raise ValueError("数据中没有找到除目标列和日期列之外的特征列")

            # 4. 特征数据转换
            feature_data = data[feature_cols]
            # 使用 numpy.array() 方法进行转换，避免类型错误
            features = torch.tensor(np.array(feature_data), dtype=torch.float32)

            self.logger.info(
                f"数据列识别完成:\n"
                f"- 目标列: {target_col}\n"
                f"- 日期列: {date_col}\n"
                f"- 特征列: {feature_cols}\n"
                f"- 样本数: {len(data)}\n"
                f"- 特征维度: {features.shape[1]}"
            )
            # 返回包含特征、目标、日期和特征名称的字典
            # 确保日期序列是 pandas.Series 类型，而不是 DatetimeIndex
            if isinstance(dates, pd.DatetimeIndex):
                dates = pd.Series(dates)
            return {'features': features, 'targets': targets, 'dates': dates, 'feature_names': feature_cols}

        except Exception as e:
            self.logger.error(f"数据加载失败: {e!s}")
            raise DataProcessError(f"数据加载失败: {e!s}") from e

    @property
    def dataset(self) -> TimeSeriesDatasetProtocol:
        """获取底层数据集"""
        if self._dataset is None:
            # 初始化默认数据集
            from src.data.windowed_time_series import TimeSeriesWindowDataset
            self._dataset = TimeSeriesWindowDataset(self.config)
        return cast(TimeSeriesDatasetProtocol, self._dataset)

    @property
    def batch_size(self) -> int:
        """获取批次大小"""
        return self._batch_size

    def _init_components(self):
        """初始化数据处理组件"""
        try:
            # 从数据文件自动计算feature_dim
            data_path = getattr(self.config.data, 'data_path', None)
            if not data_path:
                raise ValueError("配置文件中未指定data.data_path")

            # 验证文件是否存在
            if not Path(data_path).exists():
                raise ValueError(f"数据文件不存在: {data_path}")

            # 读取数据并验证
            df = pd.read_csv(data_path)
            if df.empty:
                raise ValueError("数据文件为空")

            # 记录数据样本（前3行和后3行）
            self.logger.info(
                f"\n=== 数据样本 ===\n"
                f"前3行:\n{df.head(3)}\n\n"
                f"后3行:\n{df.tail(3)}"
            )

            # 计算特征维度(总列数减去目标列)
            if not hasattr(self.config.data, 'target'):
                error_msg = "配置中缺少 data.target 配置项"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            target_col = self.config.data.target
            if target_col not in df.columns:
                raise ValueError(
                    f"目标列'{target_col}'不存在于数据文件中\n"
                    f"可用列: {list(df.columns)}"
                )

            # 动态计算特征维度，允许为None
            feature_dim = len(df.columns) - 1
            if feature_dim <= 0:
                self.logger.warning(
                    f"计算的特征维度为{feature_dim}，系统将在运行时动态确定特征维度\n"
                    f"总列数: {len(df.columns)}, 目标列: '{target_col}'"
                )
                feature_dim = None

            # 检查数据长度是否足够创建窗口
            if not hasattr(self.config.data, 'window_size'):
                error_msg = "配置中缺少 data.window_size 配置项"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            window_size = self.config.data.window_size
            if window_size is None:
                raise ValueError("窗口大小不能为None")
            if len(df) < window_size:
                raise ValueError(
                    f"数据长度({len(df)})小于窗口大小({window_size})\n"
                    f"需要至少{window_size}行数据"
                )

            self.logger.info(
                f"数据验证通过 | "
                f"特征维度: {feature_dim} | "
                f"样本数: {len(df)} | "
                f"窗口大小: {window_size}"
            )
        except ValueError as e: # Catch specific ValueError for data issues
            # Log the specific error before raising DataProcessError
            self.logger.error(f"数据组件初始化失败: {e!s}")
            raise DataProcessError(f"组件初始化失败: {e!s}") from e
        except Exception as e: # Catch other unexpected exceptions
            # Log unexpected errors
            self.logger.exception(f"组件初始化时发生意外错误: {e!s}")
            # Re-raise as RuntimeError or a more specific unexpected error type if needed
            raise RuntimeError(f"组件初始化时发生意外错误: {e!s}") from e

    def _init_resource_management(self):
        """初始化资源管理

         简化资源管理，仅保留基本CUDA资源初始化
         """
        self.cuda_manager = CUDAManager()  # 单例模式自动初始化

        # 初始化预取相关属性
        self.prefetch_queue = queue.Queue(maxsize=2)  # 预取队列，最多存储2个批次
        self.prefetch_stop = threading.Event()  # 停止预取的事件
        self.prefetch_thread = None  # 预取线程
        # 数据加载流将在下面创建

        # 创建数据加载流
        self.data_stream = self.cuda_manager.create_stream("data_loading")
        self.logger.info("创建数据加载流")

    def __len__(self) -> int:
        """获取数据集长度"""
        if not isinstance(self.dataset, Sized):
            raise TypeError("数据集必须支持len()操作")
        try:
            length = len(self.dataset)
            if length is None:
                raise ValueError("数据集长度不能为None")
            # 添加类型断言确保返回int类型
            assert isinstance(length, int), f"数据集长度必须是int类型，实际为{type(length)}"
            return length
        except Exception as e:
            raise ValueError(f"获取数据集长度失败: {e!s}") from e

    def __iter__(self) -> Iterator[dict[str, torch.Tensor]]:
        """迭代批次数据，支持CUDA流并行加载"""
        if self._dataset is None:
            raise ValueError("数据集未初始化")

        # 创建数据加载器
        loader = TorchDataLoader(
            self._dataset,
            batch_size=self._batch_size,
            shuffle=self.shuffle,
            num_workers=self.num_workers,
            pin_memory=True  # 启用pin_memory以加速数据传输
        )

        # 使用CUDA流预取模式
        self.logger.debug("使用CUDA流预取模式加载数据")
        return self._stream_prefetch_iter(loader)

    def _stream_prefetch_iter(self, loader: TorchDataLoader) -> Iterator[dict[str, torch.Tensor]]:
        """使用CUDA流预取数据

        Args:
            loader: PyTorch数据加载器

        Returns:
            Iterator[Dict[str, torch.Tensor]]: 数据迭代器
        """
        # 重置停止事件
        self.prefetch_stop.clear()

        # 清空队列
        while not self.prefetch_queue.empty():
            try:
                self.prefetch_queue.get_nowait()
            except queue.Empty:
                break

        # 启动预取线程
        self.prefetch_thread = threading.Thread(
            target=self._prefetch_worker,
            args=(loader,),
            daemon=True
        )
        self.prefetch_thread.start()

        try:
            # 主线程从队列中获取数据
            while not self.prefetch_stop.is_set():
                try:
                    batch = self.prefetch_queue.get(timeout=5)
                    if batch is None:  # 结束标记
                        break
                    yield batch
                except queue.Empty:
                    # 检查线程是否还活着
                    if not self.prefetch_thread.is_alive():
                        self.logger.warning("预取线程已终止，可能发生错误")
                        break
                    continue
        finally:
            # 清理资源
            self.prefetch_stop.set()
            if self.prefetch_thread.is_alive():
                self.prefetch_thread.join(timeout=1)

    def _prefetch_worker(self, loader: TorchDataLoader) -> None:
        """预取工作线程

        Args:
            loader: PyTorch数据加载器
        """
        # 使用数据加载流
        with torch.cuda.stream(self.data_stream):
            for batch in loader:
                if self.prefetch_stop.is_set():
                    break

                # 处理不同类型的批次数据
                if isinstance(batch, list | tuple):
                    processed_batch = {
                        'features': batch[0].to(self.cuda_manager.device, non_blocking=True),
                        'target': batch[1].to(self.cuda_manager.device, non_blocking=True)
                    }
                elif isinstance(batch, dict):
                    processed_batch = {}
                    for k, v in batch.items():
                        if isinstance(v, torch.Tensor):
                            processed_batch[k] = v.to(self.cuda_manager.device, non_blocking=True)
                        else:
                            processed_batch[k] = v
                else:
                    processed_batch = {
                        'features': batch.to(self.cuda_manager.device, non_blocking=True),
                        'target': torch.zeros(len(batch), 1, device=self.cuda_manager.device)
                    }

                # 将处理后的批次数据放入队列
                self.prefetch_queue.put(processed_batch)

            # 所有数据处理完毕，放入结束标记
            self.prefetch_queue.put(None)

    def get_target_standardizer(self) -> StandardizationProtocol | None:
        """获取目标变量的标准化器

        从底层数据集中获取标准化器实例

        Returns:
            标准化器实例，如果数据集不支持则返回None
        """
        if self._dataset is None:
            return None
        if isinstance(self._dataset, StandardizationProtocol):
            return self._dataset.get_target_standardizer()
        return None

    @property
    def target_scale_factor(self) -> float:
        """获取目标缩放因子

        Returns:
            目标缩放因子，如果未设置则返回1.0
        """
        if self._dataset is None:
            return 1.0
        return getattr(self._dataset, 'target_scale_factor', 1.0)
    def __del__(self):
        """资源清理"""
        if hasattr(self, 'prefetch_stop'):
            self.prefetch_stop.set()
        if hasattr(self, 'prefetch_thread') and self.prefetch_thread and self.prefetch_thread.is_alive():
            self.prefetch_thread.join(timeout=1)

# 移除兼容性类型别名，强制使用新类名
# CustomDataLoader = TimeSeriesDataLoader

def create_dataloaders(config: ConfigManager) -> dict[str, TimeSeriesDataLoader]:
    """创建数据加载器

    Args:
        config: 配置对象

    Returns:
        Dict[str, Any]: 包含训练、验证和测试数据加载器的字典
    """
    logger = get_logger(__name__)
    logger.info("创建数据加载器...")

    try:
        from src.data.windowed_time_series import TimeSeriesWindowDataset

        # 创建数据集
        train_dataset = TimeSeriesWindowDataset(config, split='train')
        val_dataset = TimeSeriesWindowDataset(config, split='val')
        test_dataset = TimeSeriesWindowDataset(config, split='test')

        # 获取批次大小
        batch_size = config.training.batch_size # 强制从配置读取，无默认值

        # 创建数据加载器
        # 使用两种方式创建数据加载器，以支持不同的使用场景
        # 1. 使用TimeSeriesDataLoader类（支持CUDA流并行加载）
        custom_loaders = {
            'train': TimeSeriesDataLoader(config, train_dataset, shuffle=True),
            'val': TimeSeriesDataLoader(config, val_dataset),
            'test': TimeSeriesDataLoader(config, test_dataset)
        }

        # 2. 使用PyTorch原生的DataLoader
        # 强制要求 data.pin_memory 在配置中存在
        if not hasattr(config.data, 'pin_memory'):
            raise ValueError("配置中缺少 data.pin_memory")
        # 确保 pin_memory 是布尔值
        bool(config.data.pin_memory)
        # 不再使用原生的DataLoader，专注于自定义的TimeSeriesDataLoader

        # 默认返回自定义的TimeSeriesDataLoader（支持CUDA流）
        logger.info(f"数据加载器创建完成: 批次大小={batch_size}, 使用CUDA流={CUDAManager().is_cuda_available}")
        return custom_loaders
    except Exception as e:
        logger.error(f"创建数据加载器失败: {e!s}")
        raise


def execute_data_loading(config: ConfigManager) -> tuple[Any, Any, Any | None]:
    """执行完整数据加载流程

    Args:
        config: 配置管理器

    Returns:
        Tuple[Any, Any, Optional[Any]]: (train_loader, val_loader, test_loader)
    """
    loaders = create_dataloaders(config)

    # 检查必需的数据加载器
    if 'train' not in loaders:
        raise ValueError("缺少训练数据加载器")
    if 'val' not in loaders:
        raise ValueError("缺少验证数据加载器")

    return (
        loaders['train'],
        loaders['val'],
        loaders.get('test', None)
    )
