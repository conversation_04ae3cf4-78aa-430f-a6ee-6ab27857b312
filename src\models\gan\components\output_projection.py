"""输出投影模块 - 提供将特征投影到输出空间的功能

本模块实现了将特征投影到输出空间的功能，包括：
1. 基本输出投影
2. 多层输出投影
3. 残差输出投影
4. 注意力输出投影
5. 条件输出投影
"""


import torch
from torch import nn

from src.models.base.base_module import BaseModule


class OutputProjection(BaseModule):
    """输出投影 - 将特征投影到输出空间"""

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dim: int | None = None,
        num_layers: int = 2,
        dropout: float = 0.1,
        activation: str = "leaky_relu",
        use_residual: bool = True
    ):
        """初始化输出投影

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            hidden_dim: 隐藏维度，如果为None则使用input_dim
            num_layers: 层数
            dropout: Dropout比率
            activation: 激活函数，可选值：leaky_relu, relu, gelu, tanh
            use_residual: 是否使用残差连接
        """
        super().__init__("OutputProjection")

        # 设置隐藏维度
        if hidden_dim is None:
            hidden_dim = input_dim

        # 选择激活函数
        if activation == "leaky_relu":
            act_fn = nn.LeakyReLU(0.2)
        elif activation == "relu":
            act_fn = nn.ReLU()
        elif activation == "gelu":
            act_fn = nn.GELU()
        elif activation == "tanh":
            act_fn = nn.Tanh()
        else:
            raise ValueError(f"不支持的激活函数: {activation}")

        # 创建投影层
        layers = []

        # 第一层
        layers.append(nn.Linear(input_dim, hidden_dim))
        layers.append(act_fn)
        layers.append(nn.Dropout(dropout))

        # 中间层
        for _ in range(num_layers - 2):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            layers.append(act_fn)
            layers.append(nn.Dropout(dropout))

        # 最后一层
        if num_layers > 1:
            layers.append(nn.Linear(hidden_dim, output_dim))

        self.projection = nn.Sequential(*layers)

        # 残差连接
        self.use_residual = use_residual and input_dim == output_dim

        # 如果输入维度不等于输出维度，但需要使用残差连接，则创建一个线性投影
        if use_residual and input_dim != output_dim:
            self.residual_proj = nn.Linear(input_dim, output_dim)
        else:
            self.residual_proj = nn.Identity()

        # 层归一化
        # 仅在输出维度大于1时应用LayerNorm
        self.layer_norm = nn.LayerNorm(output_dim) if output_dim > 1 else nn.Identity()

        self.logger.info(
            f"输出投影初始化完成:\n"
            f"- 输入维度: {input_dim}\n"
            f"- 输出维度: {output_dim}\n"
            f"- 隐藏维度: {hidden_dim}\n"
            f"- 层数: {num_layers}\n"
            f"- 激活函数: {activation}\n"
            f"- 使用残差连接: {use_residual}"
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入特征 [batch_size, ..., input_dim]

        Returns:
            torch.Tensor: 输出特征 [batch_size, ..., output_dim]
        """
        # 应用投影
        projection = self.projection(x)

        # 应用残差连接
        if self.use_residual:
            residual = self.residual_proj(x)
            output = projection + residual
        else:
            output = projection

        # 层归一化
        # 应用层归一化 (如果 output_dim > 1)
        output = self.layer_norm(output)

        return output


class ConditionalOutputProjection(BaseModule):
    """条件输出投影 - 根据条件将特征投影到输出空间"""

    def __init__(
        self,
        input_dim: int,
        condition_dim: int,
        output_dim: int,
        hidden_dim: int | None = None,
        num_layers: int = 2,
        dropout: float = 0.1,
        activation: str = "leaky_relu"
    ):
        """初始化条件输出投影

        Args:
            input_dim: 输入维度
            condition_dim: 条件维度
            output_dim: 输出维度
            hidden_dim: 隐藏维度，如果为None则使用input_dim
            num_layers: 层数
            dropout: Dropout比率
            activation: 激活函数，可选值：leaky_relu, relu, gelu, tanh
        """
        super().__init__("ConditionalOutputProjection")

        # 设置隐藏维度
        if hidden_dim is None:
            hidden_dim = input_dim

        # 选择激活函数
        if activation == "leaky_relu":
            act_fn = nn.LeakyReLU(0.2)
        elif activation == "relu":
            act_fn = nn.ReLU()
        elif activation == "gelu":
            act_fn = nn.GELU()
        elif activation == "tanh":
            act_fn = nn.Tanh()
        else:
            raise ValueError(f"不支持的激活函数: {activation}")

        # 条件编码器
        self.condition_encoder = nn.Sequential(
            nn.Linear(condition_dim, hidden_dim),
            act_fn,
            nn.Dropout(dropout)
        )

        # 输入编码器
        self.input_encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            act_fn,
            nn.Dropout(dropout)
        )

        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            act_fn,
            nn.Dropout(dropout)
        )

        # 输出投影
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, output_dim)
        )

        # 层归一化
        self.layer_norm = nn.LayerNorm(output_dim)

        self.logger.info(
            f"条件输出投影初始化完成:\n"
            f"- 输入维度: {input_dim}\n"
            f"- 条件维度: {condition_dim}\n"
            f"- 输出维度: {output_dim}\n"
            f"- 隐藏维度: {hidden_dim}\n"
            f"- 层数: {num_layers}\n"
            f"- 激活函数: {activation}"
        )

    def forward(
        self,
        x: torch.Tensor,
        condition: torch.Tensor
    ) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入特征 [batch_size, ..., input_dim]
            condition: 条件特征 [batch_size, ..., condition_dim]

        Returns:
            torch.Tensor: 输出特征 [batch_size, ..., output_dim]
        """
        # 编码条件
        encoded_condition = self.condition_encoder(condition)

        # 编码输入
        encoded_input = self.input_encoder(x)

        # 融合特征
        fused = torch.cat([encoded_input, encoded_condition], dim=-1)
        fused = self.fusion(fused)

        # 输出投影
        output = self.output_proj(fused)

        # 层归一化
        output = self.layer_norm(output)

        return output
