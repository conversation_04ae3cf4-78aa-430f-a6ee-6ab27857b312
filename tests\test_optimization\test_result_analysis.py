#!/usr/bin/env python
"""
测试结果分析功能

本模块测试超参数优化中的结果分析功能，包括试验结果排序和筛选、
最佳参数组合报告生成等功能。

测试内容:
1. 试验结果分析
2. 最佳参数组合报告
3. 持续时间格式化
4. 参数重要性分析
"""

import logging
import os
import sys
import unittest
from unittest.mock import MagicMock, patch

import optuna

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# 导入被测试的函数
from src.optimization.result_analysis import (
    analyze_parameter_importance,
    analyze_trials,
    format_duration,
    get_best_trial_config,
    report_best_trials,
    report_optimization_summary,
)

# 配置日志
logging.basicConfig(level=logging.DEBUG)


class TestResultAnalysis(unittest.TestCase):
    """测试结果分析功能"""

    def setUp(self):
        """测试前的准备工作"""
        # 禁用日志输出，使测试输出更清晰
        logging.disable(logging.CRITICAL)

        # 创建模拟的Optuna Study
        self.study = MagicMock(spec=optuna.study.Study)

        # 创建模拟的试验
        self.trial1 = MagicMock(spec=optuna.trial.FrozenTrial)
        self.trial1.number = 1
        self.trial1.state = optuna.trial.TrialState.COMPLETE
        self.trial1.value = 0.5
        self.trial1.params = {'x': 1.0, 'y': 2.0}

        self.trial2 = MagicMock(spec=optuna.trial.FrozenTrial)
        self.trial2.number = 2
        self.trial2.state = optuna.trial.TrialState.COMPLETE
        self.trial2.value = 0.3
        self.trial2.params = {'x': 3.0, 'y': 4.0}

        self.trial3 = MagicMock(spec=optuna.trial.FrozenTrial)
        self.trial3.number = 3
        self.trial3.state = optuna.trial.TrialState.PRUNED
        self.trial3.value = None
        self.trial3.params = {'x': 5.0, 'y': 6.0}

        self.trial4 = MagicMock(spec=optuna.trial.FrozenTrial)
        self.trial4.number = 4
        self.trial4.state = optuna.trial.TrialState.FAIL
        self.trial4.value = None
        self.trial4.params = {'x': 7.0, 'y': 8.0}

        # 设置Study的trials属性
        self.study.trials = [self.trial1, self.trial2, self.trial3, self.trial4]

    def tearDown(self):
        """测试后的清理工作"""
        # 恢复日志输出
        logging.disable(logging.NOTSET)

    def test_analyze_trials(self):
        """测试试验结果分析"""
        # 调用试验结果分析函数
        trial_stats, sorted_trials = analyze_trials(self.study)

        # 验证统计结果
        self.assertEqual(trial_stats['all'], 4)
        self.assertEqual(trial_stats['completed'], 2)
        self.assertEqual(trial_stats['pruned'], 1)
        self.assertEqual(trial_stats['failed'], 1)

        # 验证排序结果
        self.assertEqual(len(sorted_trials), 2)
        self.assertEqual(sorted_trials[0].number, 2)  # trial2的值更小，应该排在前面
        self.assertEqual(sorted_trials[1].number, 1)

    @patch('src.optimization.result_analysis.logger')
    def test_report_best_trials(self, mock_logger):
        """测试最佳参数组合报告"""
        # 准备排序后的试验列表
        sorted_trials = [self.trial2, self.trial1]

        # 调用最佳参数组合报告函数
        report_best_trials(sorted_trials, top_n=1)

        # 验证日志调用
        self.assertTrue(mock_logger.info.called)
        # 验证至少调用了一次info方法，并且参数中包含trial2的编号
        mock_logger.info.assert_any_call(f"- 试验编号: #{self.trial2.number}")

    def test_format_duration(self):
        """测试持续时间格式化"""
        # 测试秒级别
        self.assertEqual(format_duration(10.5), "10.50秒")

        # 测试分钟级别
        self.assertEqual(format_duration(65.3), "1分钟5.30秒")

        # 测试小时级别
        self.assertEqual(format_duration(3600.7), "1小时0分钟0.70秒")
        self.assertEqual(format_duration(7200.2), "2小时0分钟0.20秒")

    @patch('optuna.importance.get_param_importances')
    @patch('src.optimization.result_analysis.logger')
    def test_analyze_parameter_importance(self, mock_logger, mock_get_importances):
        """测试参数重要性分析"""
        # 模拟参数重要性结果
        mock_get_importances.return_value = {'x': 0.7, 'y': 0.3}

        # 调用参数重要性分析函数
        importances = analyze_parameter_importance(self.study)

        # 验证结果
        self.assertEqual(importances, {'x': 0.7, 'y': 0.3})

        # 验证日志调用
        self.assertTrue(mock_logger.info.called)

    @patch('src.optimization.result_analysis.logger')
    def test_report_optimization_summary(self, mock_logger):
        """测试优化过程总结报告"""
        # 调用优化过程总结报告函数
        report_optimization_summary(self.study, start_time=0, phase_duration=100, phase_name="测试阶段")

        # 验证日志调用
        self.assertTrue(mock_logger.info.called)
        # 验证至少调用了一次info方法，并且参数中包含阶段名称
        mock_logger.info.assert_any_call("=== 测试阶段完成 (耗时: 1分钟40.00秒) ===")

    def test_get_best_trial_config(self):
        """测试获取最佳试验配置"""
        # 准备试验
        trial = MagicMock(spec=optuna.trial.FrozenTrial)
        trial.params = {
            'model.hidden_dim': 128,
            'model.use_self_attention': True,
            'data.window_size': 36,
            'training.lambda_gp': 10.0
        }

        # 调用获取最佳试验配置函数
        config = get_best_trial_config(trial)

        # 验证结果
        self.assertEqual(config['model']['hidden_dim'], 128)
        self.assertEqual(config['model']['use_self_attention'], True)
        self.assertEqual(config['data']['window_size'], 36)
        self.assertEqual(config['training']['lambda_gp'], 10.0)


if __name__ == '__main__':
    unittest.main()
