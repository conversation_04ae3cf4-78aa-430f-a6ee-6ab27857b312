"""模型保存器模块 - 提供模型保存和加载功能,不包含状态管理

模块路径: src/models/base/model_saver.py
父类模块: src/models/base/base_model.py
状态管理: src/models/base/model_state.py
测试模块: tests/test_models/test_gan/test_checkpoint_management.py
相关模块:
- src/models/gan/gan_model.py (使用ModelSaver进行模型保存)
- src/utils/logger.py (日志记录)
- src/utils/config_manager.py (配置管理)

本模块实现了模型保存和加载功能，包括：
1. 模型保存
2. 模型加载
3. 检查点管理
4. 最佳模型管理
5. 模型版本控制

设计原则:
- 单一职责: 只负责模型保存和加载
- 动态配置: 根据config.yaml动态调整保存参数
- 最小依赖: 仅依赖torch和基础工具模块
- 严格验证: 保存前验证模型完整性
"""

import json
import time
from pathlib import Path
from typing import Any, Optional

import torch
from torch import nn, optim

from src.models.base.model_state import ModelStateManager
from src.utils.config.validator import ConfigValidator, ValidationRule
from src.utils.logger import get_logger


class ModelSaver(ConfigValidator):
    """模型保存器 - 专注于模型的保存和加载,不包含状态管理功能"""

    def __init__(
        self,
        model_name: str,
        save_dir: str | Path,
        config: Any,
        state_manager: Optional['ModelStateManager'] = None
    ):
        """初始化模型保存器

        Args:
            model_name: 模型名称
            save_dir: 保存目录
            config: 配置对象 (必须包含training.checkpoint配置)
            state_manager: 状态管理器实例

        Raises:
            ValueError: 如果config无效或缺少必要配置
        """
        if config is None:
            raise ValueError("config参数不能为None")
        self.logger = get_logger(f"{model_name}Saver")
        self.model_name = model_name
        self.save_dir = Path(save_dir)
        self.config = config

        # 状态管理器设置
        self.state_manager = state_manager

        # 创建保存目录
        self.save_dir.mkdir(parents=True, exist_ok=True)

        # 调用父类初始化
        super().__init__(self.logger)

        # 添加验证规则
        self.add_rule(ValidationRule(
            field_path="training.checkpoint.metric_name",
            field_type=str,
            required=False,  # 有默认值
            allowed_values=['val_loss', 'val_accuracy', 'val_f1', 'val_mae']
        ))

        self.add_rule(ValidationRule(
            field_path="training.checkpoint.metric_mode",
            field_type=str,
            required=False,  # 有默认值
            allowed_values=['min', 'max']
        ))

        self.add_rule(ValidationRule(
            field_path="training.checkpoint.keep_best_k",
            field_type=int,
            required=False,  # 有默认值
            min_value=1,
            max_value=10
        ))

        self.add_rule(ValidationRule(
            field_path="training.checkpoint.save_freq",
            field_type=int,
            required=True,
            min_value=1
        ))

        # 验证配置
        self.validate_or_raise(config)

        checkpoint_config = self.config.training.checkpoint

        # 设置基本属性
        if hasattr(checkpoint_config, 'metric_name'):
            self.metric_name = checkpoint_config.metric_name
        else:
            self.metric_name = 'val_loss'

        if hasattr(checkpoint_config, 'metric_mode'):
            self.metric_mode = checkpoint_config.metric_mode
        else:
            self.metric_mode = 'min'

        if hasattr(checkpoint_config, 'keep_best_k'):
            self.keep_best_n = checkpoint_config.keep_best_k
        else:
            self.keep_best_n = 1

        # 初始化最佳指标
        self.best_metric = float('inf')

        self.logger.info(
            f"模型保存器初始化完成:\n"
            f"- 模型名称: {self.model_name}\n"
            f"- 保存目录: {self.save_dir}\n"
            f"- 状态管理器: {'已设置' if state_manager else '未设置'}\n"
            f"- 监控指标: {self.metric_name}\n"
            f"- 指标模式: {self.metric_mode}\n"
            f"- 保留最佳数: {self.keep_best_n}"
        )

    def set_state_manager(self, state_manager: 'ModelStateManager'):
        """设置状态管理器

        Args:
            state_manager: 状态管理器实例
        """
        self.state_manager = state_manager
        self.logger.info("状态管理器已设置")

    def save_model(
        self,
        model: nn.Module,
        optimizer: optim.Optimizer | None = None,
        epoch: int = 0,
        metrics: dict[str, float] | None = None,
        is_best: bool = False
    ):
        """保存模型

        Args:
            model: 模型
            optimizer: 优化器
            epoch: 当前轮次
            metrics: 评估指标
            is_best: 是否是最佳模型
        """
        self.logger.debug(f"开始保存模型 - 轮次: {epoch}, 是否最佳: {is_best}")

        # 验证必要参数
        if metrics is None:
            error_msg = "必须提供评估指标数据"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not isinstance(metrics, dict):
            error_msg = f"指标必须是字典类型，而不是{type(metrics)}"
            self.logger.error(error_msg)
            raise TypeError(error_msg)

        if not metrics:
            error_msg = "指标字典不能为空"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.logger.debug(f"指标信息: {metrics}")

        try:
            # 创建保存字典
            save_dict = {
                'model_state_dict': model.state_dict(),
                'epoch': epoch,
                'metrics': metrics,
                'timestamp': time.time()
            }

            # 处理优化器状态
            if optimizer is not None:
                try:
                    save_dict['optimizer_state_dict'] = optimizer.state_dict()
                    self.logger.debug("已添加优化器状态")
                except Exception as e:
                    error_msg = f"保存优化器状态失败: {e!s}"
                    self.logger.error(error_msg)
                    raise RuntimeError(error_msg) from e

            # 保存最佳模型
            if is_best:
                best_path = self.save_dir / f"{self.model_name}_best.pt"
                try:
                    torch.save(save_dict, best_path)
                    self.logger.info(f"最佳模型已保存: {best_path}")
                except Exception as e:
                    error_msg = f"保存最佳模型失败: {e!s}"
                    self.logger.error(error_msg)
                    raise RuntimeError(error_msg) from e

                # 更新最佳指标
                if self.metric_name in metrics:
                    old_metric = self.best_metric
                    self.best_metric = metrics[self.metric_name]
                    self.logger.debug(f"更新最佳指标 {self.metric_name}: {old_metric} -> {self.best_metric}")
        except Exception as e:
            self.logger.error(f"保存模型失败: {e!s}")

    def load_model(
        self,
        model: nn.Module,
        optimizer: optim.Optimizer | None = None,
        filename: str | None = None,
        load_best: bool = False
    ) -> tuple[nn.Module, optim.Optimizer | None, int, dict[str, float]]:
        """加载模型

        Args:
            model: 模型
            optimizer: 优化器
            filename: 文件名，如果为None则加载最新的模型
            load_best: 是否加载最佳模型

        Returns:
            Tuple[nn.Module, Optional[optim.Optimizer], int, Dict[str, float]]:
                模型、优化器、轮次、评估指标
        """
        # 确定加载路径
        if load_best:
            load_path = self.save_dir / f"{self.model_name}_best.pt"
        elif filename is not None:
            load_path = self.save_dir / filename
        else:
            # 查找最新的模型
            model_files = list(self.save_dir.glob(f"{self.model_name}_epoch_*.pt"))
            if not model_files:
                error_msg = "没有找到可加载的模型文件"
                self.logger.error(error_msg)
                raise FileNotFoundError(error_msg)

            # 按轮次排序
            model_files.sort(key=lambda p: int(p.stem.split('_')[-1]))
            load_path = model_files[-1]

        # 检查文件是否存在
        if not load_path.exists():
            error_msg = f"模型文件不存在: {load_path}"
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        try:
            # 加载检查点文件
            try:
                checkpoint = torch.load(load_path)
            except Exception as e:
                error_msg = f"加载检查点文件失败: {e!s}"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg) from e

            # 验证检查点数据完整性
            required_fields = ['model_state_dict', 'epoch', 'metrics']
            missing_fields = [field for field in required_fields if field not in checkpoint]
            if missing_fields:
                error_msg = f"检查点缺少必要字段: {', '.join(missing_fields)}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 加载模型参数
            try:
                model.load_state_dict(checkpoint['model_state_dict'])
            except Exception as e:
                error_msg = f"加载模型参数失败: {e!s}"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg) from e

            # 加载优化器参数(如果存在)
            if optimizer is not None:
                if 'optimizer_state_dict' not in checkpoint:
                    error_msg = "检查点中缺少优化器状态"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)
                try:
                    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
                except Exception as e:
                    error_msg = f"加载优化器参数失败: {e!s}"
                    self.logger.error(error_msg)
                    raise RuntimeError(error_msg) from e

            # 获取轮次和评估指标
            epoch = checkpoint['epoch']  # 必须存在
            metrics = checkpoint['metrics']  # 必须存在

            self.logger.info(
                f"模型加载成功:\n"
                f"- 文件: {load_path}\n"
                f"- 轮次: {epoch}\n"
                f"- 指标: {metrics}"
            )

            return model, optimizer, epoch, metrics

        except Exception as e:
            error_msg = f"模型加载失败: {e!s}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def should_save_checkpoint(self, epoch: int) -> bool:
        """通过状态管理器判断是否应该保存检查点

        Args:
            epoch: 当前轮次

        Returns:
            bool: 是否应该保存检查点
        """
        if self.state_manager is None:
            self.logger.warning("未设置状态管理器,使用默认保存策略")
            return True

        return self.state_manager.should_save_checkpoint(epoch)

    def is_best_model(self, metrics: dict[str, float]) -> bool:
        """通过状态管理器判断是否是最佳模型

        Args:
            metrics: 评估指标

        Returns:
            bool: 是否是最佳模型
        """
        if self.state_manager is None:
            self.logger.warning("未设置状态管理器,使用默认判断策略")
            return False

        return self.state_manager.is_best_model(metrics)

    def _cleanup_old_checkpoints(self):
        """清理旧的检查点文件,只保留最新的N个和best模型"""
        try:
            # 获取所有检查点文件
            checkpoints = list(self.save_dir.glob(f"{self.model_name}_epoch_*.pt"))
            if len(checkpoints) <= self.keep_best_n:
                return

            # 按修改时间排序,保留最新的N个
            checkpoints.sort(key=lambda x: x.stat().st_mtime)

            # 删除旧文件
            for checkpoint in checkpoints[:-self.keep_best_n]:
                try:
                    checkpoint.unlink()
                    self.logger.debug(f"删除旧检查点: {checkpoint}")
                except Exception as e:
                    self.logger.warning(f"删除检查点失败 {checkpoint}: {e!s}")

        except Exception as e:
            self.logger.warning(f"清理旧检查点失败: {e!s}")

    def save_config(self, config: Any):
        """保存配置

        Args:
            config: 配置对象，必须实现to_dict()方法

        Raises:
            TypeError: 如果配置对象没有实现to_dict()方法或返回值不是字典类型
            ValueError: 如果配置为None或to_dict()返回空字典
            RuntimeError: 如果保存配置失败
        """
        if config is None:
            error_msg = "配置对象不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not hasattr(config, 'to_dict'):
            error_msg = "配置对象必须实现to_dict()方法"
            self.logger.error(error_msg)
            raise TypeError(error_msg)

        try:
            # 获取配置字典
            config_dict = config.to_dict()

            # 验证返回值
            if not isinstance(config_dict, dict):
                error_msg = f"to_dict()必须返回字典类型，而不是{type(config_dict)}"
                self.logger.error(error_msg)
                raise TypeError(error_msg)

            if not config_dict:
                error_msg = "配置字典不能为空"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 确定保存路径
            config_path = self.save_dir / f"{self.model_name}_config.json"

            try:
                # 保存配置
                with open(config_path, 'w') as f:
                    json.dump(config_dict, f, indent=2)
                self.logger.info(
                    f"配置已保存:\n"
                    f"- 文件: {config_path}\n"
                    f"- 配置项数量: {len(config_dict)}"
                )
            except Exception as e:
                error_msg = f"保存配置文件失败: {e!s}"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg) from e

        except Exception as e:
            error_msg = f"配置保存失败: {e!s}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def load_config(self) -> dict[str, Any]:
        """加载配置

        Returns:
            Dict[str, Any]: 配置字典
        """
        # 确定加载路径
        config_path = self.save_dir / f"{self.model_name}_config.json"

        # 检查文件是否存在
        if not config_path.exists():
            self.logger.warning(f"配置文件不存在: {config_path}")
            return {}

        # 加载配置
        with open(config_path) as f:
            config_dict = json.load(f)

        self.logger.info(f"配置已加载: {config_path}")

        return config_dict
