"""噪声编码模块 - 负责噪声特征提取和变换
模块路径: src/models/gan/components/noise_encoder.py

继承自:
- src/models/base/base_module.py: BaseModule

协作模块:
- src/models/gan/components/noise_processor.py
"""
from torch import Tensor, nn

from src.models.base.base_module import BaseModule


class NoiseEncoder(BaseModule):
    def __init__(self, noise_dim: int, output_dim: int, hidden_dim: int):
        """初始化噪声编码器

        Args:
            noise_dim: 输入噪声维度
            output_dim: 输出维度
            hidden_dim: 隐藏层维度

        Raises:
            ValueError: 如果任何维度参数小于等于0
        """
        super().__init__("NoiseEncoder")

        # 验证参数
        if noise_dim <= 0:
            error_msg = f"noise_dim必须为正数，当前值: {noise_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if output_dim <= 0:
            error_msg = f"output_dim必须为正数，当前值: {output_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if hidden_dim <= 0:
            error_msg = f"hidden_dim必须为正数，当前值: {hidden_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.logger.info(f"初始化噪声编码器: noise_dim={noise_dim}, output_dim={output_dim}, hidden_dim={hidden_dim}")

        self.net = nn.Sequential(
            nn.Linear(noise_dim, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim, output_dim))

        # 应用权重初始化
        self.apply(self._init_weights)
        self.logger.info(f"噪声编码器初始化完成，参数数量: {self.count_parameters():,}")

    def forward(self, noise: Tensor) -> Tensor:
        """编码噪声输入

        Args:
            noise: 输入噪声 [batch, noise_dim] 或 [batch, seq_len, noise_dim]

        Returns:
            编码后的噪声特征 [batch, seq_len, output_dim]
        """
        # 记录输入形状
        self.logger.debug(f"噪声编码器输入: shape={noise.shape}, dim={noise.dim()}")

        if noise.dim() == 2:
            # [batch, noise_dim] -> [batch, 1, noise_dim]
            self.logger.debug("将二维噪声输入扩展为三维")
            noise = noise.unsqueeze(1)
        elif noise.dim() != 3:
            error_msg = f"噪声输入维度应为2或3，实际为{noise.dim()}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 检查数值稳定性
        self._check_numerical_stability(noise, "输入噪声")

        batch_size, seq_len, _ = noise.shape
        # 展平处理 [batch*seq_len, noise_dim]
        flat_noise = noise.reshape(-1, noise.size(-1))

        # 编码 [batch*seq_len, output_dim]
        encoded = self.net(flat_noise)
        self._check_numerical_stability(encoded, "编码后噪声(展平)")

        # 恢复形状 [batch, seq_len, output_dim]
        output = encoded.reshape(batch_size, seq_len, -1)
        self._check_numerical_stability(output, "编码后噪声(最终)")

        # 记录输出形状
        self.logger.info(f"噪声编码器输出: shape={output.shape}")

        return output
