"""比较判别器分支参数量

此脚本用于比较判别器分支修改前后的参数量变化。
"""

from torch import nn


def count_parameters(model):
    """计算模型参数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

# 原始版本的分支实现
class OriginalTrendBranch(nn.Module):
    def __init__(self, input_dim=240, hidden_dim=128):
        super().__init__()
        self.trend_extractor = nn.Sequential(
            nn.Conv1d(input_dim, hidden_dim, kernel_size=5, padding=2),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=5, padding=2),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=5, padding=2),
            nn.LeakyReLU(0.2)
        )
        self.trend_evaluator = nn.Sequential(
            nn.Conv1d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_dim // 2, hidden_dim // 2, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_dim // 2, 1, kernel_size=3, padding=1)
        )

class OriginalFeatureBranch(nn.Module):
    def __init__(self, input_dim=240):
        super().__init__()
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, input_dim * 2),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim * 2, input_dim * 2),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim * 2, input_dim),
            nn.LeakyReLU(0.2)
        )
        self.correlation_evaluator = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim // 2, input_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim // 2, 1)
        )

class OriginalTemporalBranch(nn.Module):
    def __init__(self, input_dim=240):
        super().__init__()
        self.temporal_extractor = nn.GRU(
            input_size=input_dim,
            hidden_size=input_dim * 2,
            num_layers=3,
            batch_first=True,
            bidirectional=True
        )
        self.pattern_evaluator = nn.Sequential(
            nn.Linear(input_dim * 4, input_dim * 2),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim * 2, input_dim * 2),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim * 2, 1)
        )

# 精简版本的分支实现
class OptimizedTrendBranch(nn.Module):
    def __init__(self, input_dim=240, hidden_dim=128):
        super().__init__()
        self.trend_extractor = nn.Sequential(
            nn.Conv1d(input_dim, hidden_dim, kernel_size=5, padding=2),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=5, padding=2),
            nn.LeakyReLU(0.2)
        )
        self.trend_evaluator = nn.Sequential(
            nn.Conv1d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2),
            nn.Conv1d(hidden_dim // 2, 1, kernel_size=3, padding=1)
        )

class OptimizedFeatureBranch(nn.Module):
    def __init__(self, input_dim=240):
        super().__init__()
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, int(input_dim * 1.5)),
            nn.LeakyReLU(0.2),
            nn.Linear(int(input_dim * 1.5), input_dim),
            nn.LeakyReLU(0.2)
        )
        self.correlation_evaluator = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim // 2, 1)
        )

class OptimizedTemporalBranch(nn.Module):
    def __init__(self, input_dim=240):
        super().__init__()
        self.temporal_extractor = nn.GRU(
            input_size=input_dim,
            hidden_size=input_dim,
            num_layers=1,
            batch_first=True,
            bidirectional=True
        )
        self.pattern_evaluator = nn.Sequential(
            nn.Linear(input_dim * 2, input_dim),
            nn.LeakyReLU(0.2),
            nn.Linear(input_dim, 1)
        )

def main():
    """主函数"""
    input_dim = 240
    hidden_dim = 128

    # 创建原始版本的分支
    orig_trend = OriginalTrendBranch(input_dim, hidden_dim)
    orig_feature = OriginalFeatureBranch(input_dim)
    orig_temporal = OriginalTemporalBranch(input_dim)

    # 创建精简版本的分支
    opt_trend = OptimizedTrendBranch(input_dim, hidden_dim)
    opt_feature = OptimizedFeatureBranch(input_dim)
    opt_temporal = OptimizedTemporalBranch(input_dim)

    # 计算参数量
    orig_trend_params = count_parameters(orig_trend)
    orig_feature_params = count_parameters(orig_feature)
    orig_temporal_params = count_parameters(orig_temporal)
    orig_total = orig_trend_params + orig_feature_params + orig_temporal_params

    opt_trend_params = count_parameters(opt_trend)
    opt_feature_params = count_parameters(opt_feature)
    opt_temporal_params = count_parameters(opt_temporal)
    opt_total = opt_trend_params + opt_feature_params + opt_temporal_params

    # 打印结果
    print("\n=== 判别器分支参数量比较 ===")
    print(f"原始趋势分支: {orig_trend_params:,}")
    print(f"精简趋势分支: {opt_trend_params:,}")
    print(f"减少: {orig_trend_params - opt_trend_params:,} ({(orig_trend_params - opt_trend_params) / orig_trend_params * 100:.2f}%)")
    print()

    print(f"原始特征分支: {orig_feature_params:,}")
    print(f"精简特征分支: {opt_feature_params:,}")
    print(f"减少: {orig_feature_params - opt_feature_params:,} ({(orig_feature_params - opt_feature_params) / orig_feature_params * 100:.2f}%)")
    print()

    print(f"原始时序分支: {orig_temporal_params:,}")
    print(f"精简时序分支: {opt_temporal_params:,}")
    print(f"减少: {orig_temporal_params - opt_temporal_params:,} ({(orig_temporal_params - opt_temporal_params) / orig_temporal_params * 100:.2f}%)")
    print()

    print(f"原始总参数量: {orig_total:,}")
    print(f"精简总参数量: {opt_total:,}")
    print(f"总减少: {orig_total - opt_total:,} ({(orig_total - opt_total) / orig_total * 100:.2f}%)")

if __name__ == "__main__":
    main()
