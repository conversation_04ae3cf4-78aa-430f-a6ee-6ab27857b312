"""生成器模块 - 提供GAN网络的时序数据生成框架
模块路径: src/models/gan/generator.py

重构说明：
1. 原TimeSeriesGenerator重构为协调类
2. 具体实现拆分为三个组件：
   - src/models/gan/components/feature_encoder.py
   - src/models/gan/components/noise_processor.py
   - src/models/gan/components/sequence_generator.py
3. 保持原有接口不变
"""

from typing import cast  # 导入 cast

from torch import Tensor

from src.models.base.base_module import BaseModule
from src.utils.config.model import GANModelConfig  # 导入 GANModelConfig
from src.utils.config_manager import ConfigManager
from src.utils.cuda import cuda_manager

from .components.feature_encoder import FeatureEncoder
from .components.noise_processor import NoiseProcessor
from .components.sequence_generator import SequenceGenerator
from .dynamic_feature_fusion import DynamicFeatureFusion  # 导入新模块


class TimeSeriesGenerator(BaseModule):
    """时序生成器（协调各组件工作）"""
    def __init__(self, config: ConfigManager, feature_dim: int | None = None):
        super().__init__("TimeSeriesGenerator")
        self.config = config
        # 处理dimensions字段，可能是字典或对象
        self.dims = config.model.dimensions
        # 确保 base_dim 存在
        if isinstance(self.dims, dict) and 'base_dim' not in self.dims:
            self.dims = type('Dimensions', (), {'base_dim': config.model.hidden_dim})
        self.device = cuda_manager.device

        # 初始化特征维度
        self.current_feature_dim = None
        self._init_feature_dim(feature_dim)

        # 初始化各组件
        if self.current_feature_dim is not None:
            # 如果特征维度已知，直接初始化
            self.feature_encoder = FeatureEncoder(
                config=self.config, # 传递 config
                input_dim=self.current_feature_dim,
                feature_extractor_config=cast(GANModelConfig, self.config.model).feature_extractor
            )
        else:
            # 如果特征维度未知，延迟初始化
            self.feature_encoder = None

        self.noise_processor = NoiseProcessor(
            noise_dim=config.model.noise_dim,
            output_dim=self.config.model.hidden_dim # 使用配置的 hidden_dim
        )
        # 初始化动态特征融合模块
        # 使用编码后的噪声作为上下文，动态加权编码后的特征
        self.dynamic_fusion = DynamicFeatureFusion(
            feature_dim=self.config.model.hidden_dim, # 使用配置的 hidden_dim
            context_dim=self.config.model.hidden_dim, # 使用配置的 hidden_dim
            hidden_dim=self.config.model.hidden_dim   # 使用配置的 hidden_dim
        ).to(self.device)

        # 初始化序列生成器，传递配置对象
        self.sequence_generator = SequenceGenerator(
            config=self.config, # 传递配置对象
            input_dim=self.config.model.hidden_dim, # 使用配置的 hidden_dim
            hidden_dim=self.config.model.hidden_dim # 使用配置的 hidden_dim
        )

    def _init_feature_dim(self, feature_dim: int | None = None):
        """初始化特征维度

        Args:
            feature_dim: 可选特征维度
        """
        if feature_dim is not None:
            if feature_dim <= 0:
                raise ValueError(f"无效的特征维度: {feature_dim}")
            self.current_feature_dim = feature_dim
            return

        # 尝试从配置中获取
        if hasattr(self.config.data, 'feature_dim') and self.config.data.feature_dim is not None:
            feature_dim = self.config.data.feature_dim
            # 先检查非 None，再检查值
            if feature_dim is not None and feature_dim <= 0:
                raise ValueError(f"配置中的特征维度无效: {feature_dim}")
            self.current_feature_dim = feature_dim
            return

        # 如果无法确定，则在运行时动态确定
        self.logger.info("特征维度未指定，将在第一次前向传播时动态确定")
        self.current_feature_dim = None

    def _update_feature_dim(self, new_feature_dim: int):
        """动态更新特征维度

        Args:
            new_feature_dim: 新特征维度
        """
        if new_feature_dim == self.current_feature_dim:
            return

        if new_feature_dim <= 0:
            raise ValueError(f"无效的特征维度: {new_feature_dim}")

        self.logger.info(f"更新生成器特征维度: {self.current_feature_dim} -> {new_feature_dim}")

        # 保存当前状态
        state_dict = None
        if self.feature_encoder is not None:
            state_dict = self.feature_encoder.state_dict()

        # 重新初始化特征编码器
        self.feature_encoder = FeatureEncoder(
            config=self.config, # 传递 config
            input_dim=new_feature_dim,
            feature_extractor_config=cast(GANModelConfig, self.config.model).feature_extractor
        ).to(self.device)

        # 恢复兼容的参数
        if state_dict is not None:
            new_state_dict = self.feature_encoder.state_dict()
            for name, param in state_dict.items():
                if name in new_state_dict and param.shape == new_state_dict[name].shape:
                    new_state_dict[name].copy_(param)

        self.current_feature_dim = new_feature_dim

    def forward(self, condition_features: Tensor, noise: Tensor) -> Tensor:
        """前向传播

        Args:
            condition_features: 条件特征 [batch, seq_len, feature_dim]
            noise: 噪声输入 [batch, seq_len, noise_dim]

        Returns:
            生成的序列 [batch, seq_len, 1]
        """
        # 检查并更新特征维度
        actual_feature_dim = condition_features.size(2)

        # 如果特征编码器未初始化或维度变化
        if self.feature_encoder is None:
            # 首次运行，初始化特征编码器
            self.logger.info(f"首次运行，初始化特征编码器，特征维度: {actual_feature_dim}")
            self.current_feature_dim = actual_feature_dim
            self.feature_encoder = FeatureEncoder(
                config=self.config, # 传递 config
                input_dim=actual_feature_dim,
                feature_extractor_config=cast(GANModelConfig, self.config.model).feature_extractor
            ).to(self.device)
        elif actual_feature_dim != self.current_feature_dim:
            # 维度变化，更新特征编码器
            self._update_feature_dim(actual_feature_dim)

        # 特征编码
        encoded_features = self.feature_encoder(condition_features)
        self.logger.debug(f"编码后特征形状: {encoded_features.shape}")

        # 噪声处理
        encoded_noise = self.noise_processor(noise)

        # 动态特征融合
        # 使用编码后的噪声作为上下文，动态加权编码后的特征
        fused_features = self.dynamic_fusion(features=encoded_features, context=encoded_noise)

        # 序列生成
        return self.sequence_generator(fused_features)
