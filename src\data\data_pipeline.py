"""数据流水线协调器 - 负责协调完整数据处理工作流

本模块实现从原始数据到模型输入的全流程数据处理接口。

相关模块：
- src/data/base_time_series.py: 基础数据集实现
- src/data/protocol.py: 数据集接口协议
- src/data/preprocessing/: 数据预处理模块
- src/data/feature_engineer.py: 特征工程模块
- src/data/FeatureSelector.py: 特征选择模块
"""

from pathlib import Path
from typing import Any

import pandas as pd
import torch
from torch import Tensor

from src.data.standardization import Standardizer  # 导入 Standardizer
from src.utils.config_manager import ConfigManager

# TimeSeriesDataset已移除
# from .base_time_series import TimeSeriesDataset
from .protocol import TimeSeriesDatasetProtocol


class DataProcessError(Exception):
    """数据处理相关异常基类"""
    pass

class DataPipeline(TimeSeriesDatasetProtocol):
    """数据流水线协调器 - 实现完整数据处理工作流

    该类作为纯粹的协调器，负责协调各个数据处理模块的工作，不包含具体的数据处理逻辑。
    实现TimeSeriesDatasetProtocol协议，提供标准的数据集接口。

    主要职责：
    1. 协调数据加载、验证、清洗、特征工程等模块
    2. 管理数据处理流程的状态和异常
    3. 提供标准的数据集接口供模型使用
    4. 确保各处理模块之间的数据流转和兼容性
    """


    def __init__(self, raw_data_path: str, config: dict[str, Any] | ConfigManager | None = None):
        """初始化数据流水线

        Args:
            raw_data_path: 原始数据文件路径
            config: 配置参数，可以是字典或ConfigManager实例
        """
        from src.utils.logger import LoggerFactory

        # 基本属性
        self.raw_data_path = raw_data_path
        self.logger = LoggerFactory().get_logger(self.__class__.__name__)

        # 确保 self.config 是 ConfigManager 实例
        if isinstance(config, ConfigManager):
            self.config = config
        elif isinstance(config, dict):
            config_path = Path(__file__).parent.parent.parent / "config.yaml"
            self.config = ConfigManager.from_yaml(config_path)
            self.config.update(config)
            self.logger.debug("传入的字典配置已转换为 ConfigManager 实例")
        elif config is None:
             config_path = Path(__file__).parent.parent.parent / "config.yaml"
             self.config = ConfigManager.from_yaml(config_path)
             self.logger.warning("未提供配置，已创建空的 ConfigManager")
        else:
            raise TypeError(f"无效的配置类型: {type(config)}")

        # 数据处理状态
        self._processed = False
        self.feature_dim = None  # 特征维度，将在数据加载后设置

        # 尝试从模型文件获取特征维度 (现在 self.config 确定是 ConfigManager)
        try:
            # 检查路径是否存在
            if hasattr(self.config, 'data') and hasattr(self.config.data, 'model_path') and self.config.data.model_path:
                 self.config.data.update_feature_dim_from_model(self.config.data.model_path)
                 self.feature_dim = self.config.data.feature_dim
                 self.logger.info(f"从模型文件 {self.config.data.model_path} 获取特征维度: {self.feature_dim}")
            elif hasattr(self.config, 'data') and hasattr(self.config.data, 'feature_dim') and self.config.data.feature_dim is not None:
                 self.feature_dim = self.config.data.feature_dim # 使用配置中已有的 feature_dim
                 self.logger.info(f"使用配置中的特征维度: {self.feature_dim}")

        except AttributeError as e:
             self.logger.warning(f"访问配置项以获取特征维度时出错: {e}")
        except Exception as e:
            self.logger.warning(f"尝试从模型文件自动获取特征维度时发生错误: {e!s}")

        # 数据存储
        self._features: torch.Tensor | None = None  # 特征数据
        self._targets: torch.Tensor | None = None   # 目标数据
        self._dates: pd.Series | None = None      # 日期数据
        self._feature_names: list[str] | None = None # 特征列名
        self._original_feature_names: list[str] | None = None # 原始特征名称的备份
        self.windows = None    # 窗口数据

        # 处理模块
        self._data_cleaner = None
        self._data_loader = None
        self._data_validator = None  # 数据验证器将在validate_data方法中按需创建
        self._standardizer = None # Standardizer for features
        self.target_standardizer = None # Standardizer for the target variable

        self.logger.info(f"数据流水线初始化完成 | 数据路径: {raw_data_path}")

    def load_data(self) -> None:
        """数据加载阶段

        协调数据加载过程，使用TimeSeriesDataLoader加载原始数据。
        实际的数据加载逻辑由TimeSeriesDataLoader实现。

        Raises:
            DataProcessError: 如果数据加载失败
        """
        self.logger.info(f"开始加载数据 - 路径: {self.raw_data_path}")
        try:
            if self._data_loader is None:
                from src.data.data_loader import TimeSeriesDataLoader

                # 处理配置
                if isinstance(self.config, dict):
                    # 如果是字典，创建一个副本
                    config_dict = self.config.copy()
                    # 确保数据路径存在
                    if 'data' in config_dict:
                        if isinstance(config_dict['data'], dict):
                            config_dict['data']['data_path'] = self.raw_data_path
                        else:
                            config_dict['data'] = {'data_path': self.raw_data_path}
                    else:
                        config_dict['data'] = {'data_path': self.raw_data_path}

                    # 创建ConfigManager实例
                    from src.utils.config_manager import ConfigManager
                    config_path = Path(__file__).parent.parent.parent / "config.yaml"
                    config_manager = ConfigManager.from_yaml(config_path)
                    config_manager.update(config_dict)
                    self._data_loader = TimeSeriesDataLoader(config_manager)
                else:
                    # 如果已经是ConfigManager实例，直接使用
                    self._data_loader = TimeSeriesDataLoader(self.config)

            # 加载数据，现在返回一个字典
            loaded_data = self._data_loader.load_data()

            # 验证返回类型和内容
            if not isinstance(loaded_data, dict):
                raise DataProcessError(f"数据加载器未返回预期的字典，而是: {type(loaded_data)}")

            features = loaded_data.get('features')
            targets = loaded_data.get('targets')
            dates = loaded_data.get('dates')
            feature_names = loaded_data.get('feature_names') # 获取特征名称

            # 检查关键数据是否存在且类型正确
            if not isinstance(features, torch.Tensor):
                 raise DataProcessError("加载的数据中'features'不是Tensor或不存在")
            if not isinstance(targets, torch.Tensor):
                 raise DataProcessError("加载的数据中'targets'不是Tensor或不存在")
            if not isinstance(dates, pd.Series):
                 raise DataProcessError("加载的数据中'dates'不是pd.Series或不存在")
            if not isinstance(feature_names, list) or not all(isinstance(name, str) for name in feature_names):
                 self.logger.warning("加载的数据中'feature_names'不是字符串列表或不存在，后续将使用通用名称")
                 feature_names = None # 如果类型不对或不存在，则设为None

            # 赋值给实例属性
            self._features = features
            self._targets = targets
            self._dates = dates
            self._feature_names = feature_names # 存储特征名称

            # 保存原始特征名称的备份，以便在处理过程中可能需要恢复
            if feature_names:
                self.logger.info(f"保存原始特征名称: {feature_names}")
                self._original_feature_names = feature_names.copy()
            else:
                self.logger.warning("未能获取原始特征名称，无法创建备份")

            # 安全地记录形状信息
            feature_shape = features.shape if features is not None else "N/A"
            target_shape = targets.shape if targets is not None else "N/A"
            self.logger.info(f"数据加载完成 - 特征形状: {feature_shape}, 目标形状: {target_shape}, 日期序列长度: {len(dates) if dates is not None else 'N/A'}, 特征名称数: {len(feature_names) if feature_names else 'N/A'}")

        except Exception as e:
            self.logger.error(f"数据加载失败: {e!s}")
            raise DataProcessError(f"数据加载失败: {e!s}") from e

    def validate_data(self) -> None:
        """数据验证阶段 - 在数据加载后立即进行基本验证

        执行时机：
        - 在数据加载后立即执行
        - 在数据清洗和特征工程之前执行
        - 作为数据处理流水线的第二步（加载→验证→清洗→...）

        功能：
        1. 必要字段检查（日期列、目标列等）
        2. 数据类型验证（数值列、日期列等）
        3. 数据范围检查（无效值、无穷值等）
        4. 无法修正的价格跳变检测

        实现方式：
        - 调用DataValidator类的validate方法
        - 实际的验证逻辑由DataValidator实现
        - 任何验证失败都会抛出异常，停止执行

        与其他验证步骤的区别：
        - 与_check_data_quality方法的区别：本方法在数据加载后立即执行，而_check_data_quality在数据标准化后执行
        - 本方法检查DataFrame数据，而_check_data_quality检查张量数据（Tensor）

        Raises:
            DataProcessError: 如果数据验证失败
        """
        try:
            if self._features is None:
                raise ValueError("特征数据未初始化，请先执行数据加载")

            # 如果数据验证器未初始化，则初始化
            if self._data_validator is None:
                from src.data.preprocessing.data_validator import DataValidator
                self.logger.debug("初始化数据验证器")
                self._data_validator = DataValidator(self.config)

            # 转换为pandas DataFrame进行验证
            data_df = self._convert_to_dataframe()

            # 调用验证方法，如果验证失败会抛出DataValidationError异常
            self._data_validator.validate(data_df)

            # 如果验证通过，记录成功信息
            # 获取目标列名
            target_col = None
            if isinstance(self.config, dict):
                if 'data' in self.config and isinstance(self.config['data'], dict) and 'target' in self.config['data']:
                    target_col = self.config['data']['target']
            elif hasattr(self.config, 'data') and hasattr(self.config.data, 'target'):
                target_col = self.config.data.target

            self.logger.info(
                f"数据验证通过\n"
                f"- 目标列: {target_col}"
            )

        except DataProcessError:
            raise  # 直接重新抛出已知异常
        except Exception as e:
            # 检查是否是DataValidationError
            if e.__class__.__name__ == 'DataValidationError':
                # 将DataValidationError转换为DataProcessError
                error_msg = f"数据验证失败: {e!s}"
                self.logger.error(error_msg)
                raise DataProcessError(error_msg) from e
            else:
                # 其他异常
                error_msg = f"数据验证过程中发生意外错误: {e!s}"
                self.logger.error(error_msg)
                raise DataProcessError(error_msg) from e

    def clean_data(self) -> None:
        """数据清洗阶段

        协调数据清洗流程，使用DataCleaner处理：
        1. 缺失值
        2. 异常值
        3. 数据类型转换

        实际的清洗逻辑由DataCleaner实现。

        Raises:
            DataProcessError: 如果数据清洗失败
        """
        self.logger.info("开始数据清洗流程")
        try:
            if self._features is None:
                error_msg = "特征数据未初始化，请先执行数据加载"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 转换为pandas DataFrame进行清洗
            data_df = self._convert_to_dataframe()

            # 初始化DataCleaner
            from src.data.preprocessing.data_cleaner import DataCleaner
            data_cleaner = DataCleaner(self.config)

            # 执行数据清洗
            cleaned_df = data_cleaner.clean(data_df)

            # 分离日期、目标和特征列
            date_col_name = 'date'
            target_col_name = self.config.data.target # 获取目标列名

            # 1. 分离日期列
            if date_col_name in cleaned_df.columns:
                try:
                    self._dates = pd.to_datetime(cleaned_df[date_col_name])
                    self.logger.info(f"已分离日期列: {date_col_name}，日期范围: {self._dates.min()} 至 {self._dates.max()}")

                    # 检查日期是否按顺序排列
                    is_sorted = self._dates.is_monotonic_increasing
                    if not is_sorted:
                        self.logger.warning("日期列不是按升序排列的，这可能会影响时间序列分析")

                    # 检查日期间隔是否一致
                    date_diffs = self._dates.diff().dropna()
                    unique_diffs = date_diffs.unique()
                    if len(unique_diffs) > 1:
                        self.logger.warning(f"日期间隔不一致，检测到 {len(unique_diffs)} 种不同的间隔")
                except Exception as e:
                    self.logger.error(f"处理日期列时出错: {e!s}")
                    self._dates = None
            else:
                self._dates = None
                self.logger.error(f"清洗后的数据中未找到日期列: {date_col_name}，这将影响时间特征生成")

            # 2. 分离目标列
            if target_col_name in cleaned_df.columns:
                self._targets = torch.tensor(cleaned_df[target_col_name].values, dtype=torch.float32).unsqueeze(1)
                self.logger.debug(f"已分离目标列: {target_col_name}")
            else:
                # 如果目标列在清洗后丢失，这是严重错误
                error_msg = f"目标列 '{target_col_name}' 在数据清洗后丢失！"
                self.logger.error(error_msg)
                raise DataProcessError(error_msg)

            # 3. 分离特征列
            exclude_cols = [date_col_name, target_col_name]
            feature_cols = [col for col in cleaned_df.columns if col not in exclude_cols]

            if not feature_cols:
                 # 如果没有特征列，这也是严重错误
                 error_msg = "数据清洗后未能识别出任何特征列！"
                 self.logger.error(error_msg)
                 raise DataProcessError(error_msg)

            self._features = torch.from_numpy(cleaned_df[feature_cols].values.astype('float32'))
            self._feature_names = feature_cols # 更新特征名称列表
            self.feature_dim = self._features.shape[1] # 更新特征维度
            self.logger.debug(f"已分离特征列: {feature_cols}")

            self.logger.info(
                f"数据清洗完成 (日期、目标、特征已分离)\n"
                f"- 清洗后特征形状: {self._features.shape}\n"
                f"- 清洗后目标形状: {self._targets.shape}\n"
                f"- 清洗后日期序列长度: {len(self._dates) if self._dates is not None else 'N/A'}\n"
                f"- 特征维度: {self.feature_dim}"
            )

        except Exception as e:
            error_msg = f"数据清洗失败: {e!s}"
            self.logger.error(error_msg)
            raise DataProcessError(error_msg) from e

    # 目标标准化器拟合方法已删除，标准化过程将在standardize_data方法中统一处理

    # 特征编码方法已删除，特征编码功能将由特征工程模块统一处理

    def engineer_features(self) -> None:
        """特征工程阶段

        协调特征工程过程，使用FeatureEngineer进行特征增强，包括：
        1. 时间序列差分特征
        2. 滚动统计量特征
        3. 傅里叶变换特征
        4. 特征质量控制

        实际的特征工程逻辑由FeatureEngineer实现。

        Raises:
            DataProcessError: 如果特征工程失败
        """
        try:
            if self._features is None:
                raise ValueError("特征数据未初始化，请先执行数据加载和验证")

            from src.data.feature_engineering import FeatureManager

            # 直接使用原始配置字典
            feature_manager = FeatureManager(self.config)

            # 转换为DataFrame进行特征工程
            data_df = self._convert_to_dataframe()
            self.logger.info("开始执行特征工程流程 (调用 feature_manager.process)...")
            engineered_data = feature_manager.process(data_df)

            # 更新特征数据
            self._update_from_dataframe(engineered_data)

            self.logger.info(
                f"特征工程完成\n"
                f"- 工程后特征形状: {self._features.shape}\n"
                f"- 特征维度: {self._features.shape[1]}"
            )

        except Exception as e:
            error_msg = f"特征工程失败: {e!s}"
            self.logger.error(error_msg)
            raise DataProcessError(error_msg) from e

    def select_features(self) -> None:
        """特征选择阶段

        协调特征选择过程，使用FeatureSelector进行特征选择，基于相关性和噪声水平分析。
        实际的特征选择逻辑由FeatureSelector实现。

        Raises:
            DataProcessError: 如果特征选择失败
        """
        try:
            if self._features is None or self._targets is None:
                raise ValueError("特征或目标数据未初始化，请先执行数据加载和验证")

            from src.data.preprocessing.feature_selector import FeatureSelector
            from src.utils.logger import LoggerFactory  # Import LoggerFactory

            # 获取 LoggerFactory 实例 (可以从 self.logger 获取，或者新建)
            # 为了简单起见，这里新建一个，但更好的做法是传递 self.logger.factory 或类似机制
            logger_factory = LoggerFactory()

            # 实例化 FeatureSelector，传入 config 和 logger_factory
            # 规则 66: 依赖注入
            feature_selector = FeatureSelector(self.config, logger_factory)

            # 准备输入 DataFrame (需要特征列名)
            # 假设初始加载时有列名，或者在此处生成通用列名
            # 注意：如果特征工程改变了列数，需要确保列名一致性
            num_features = self._features.shape[1]
            features_np = self._features.detach().cpu().numpy()
            targets_np = self._targets.detach().cpu().numpy()

            # 严格检查并获取特征列名
            if self._feature_names and len(self._feature_names) == num_features:
                feature_columns = self._feature_names
                self.logger.debug(f"特征选择：使用已存储的特征名称: {feature_columns}")
            else:
                error_msg = ""
                if self._feature_names: # 如果存在但不匹配
                    error_msg = f"特征选择前：存储的特征名称数量 ({len(self._feature_names)}) 与当前特征数量 ({num_features}) 不匹配。"
                else: # 如果不存在
                    error_msg = "特征选择前：未找到有效的特征名称列表 (_feature_names is None)。"
                self.logger.error(f"无法进行特征选择: {error_msg} 无法保证列名正确性。")
                raise ValueError(f"无法进行特征选择: {error_msg}") # 强制报错

            # 创建 DataFrame
            df_for_selection = pd.DataFrame(features_np, columns=pd.Index(feature_columns))
            # 确保目标列名与 FeatureSelector 中使用的 target_col 一致
            df_for_selection[feature_selector.target_col] = targets_np

            # 调用新的 select 方法，获取选中的特征名称列表
            selected_feature_names = feature_selector.select(df_for_selection)

            # 使用选中的特征名称列表更新特征数据
            if selected_feature_names:
                # 从原始 DataFrame 中选取列，然后转回 Tensor
                selected_df = df_for_selection[selected_feature_names]
                self._features = torch.from_numpy(selected_df.values.astype('float32'))
                # 更新特征维度和特征名称列表
                self.feature_dim = self._features.shape[1]
                self._feature_names = selected_feature_names # 更新存储的特征名称
                self.logger.info(
                    f"特征选择完成\n"
                    f"- 选中特征数量: {len(selected_feature_names)}\n"
                    f"- 选中特征名称: {selected_feature_names}\n" # 添加日志记录选中的名称
                    f"- 选择后特征形状: {self._features.shape}"
                )
            else:
                # 根据 FeatureSelector 的逻辑，如果 select 返回空列表会抛出异常，
                # 但为防万一，添加日志
                self.logger.error("特征选择返回了空列表，这是一个意外情况！")
                raise DataProcessError("特征选择未能选出任何特征。")

        except Exception as e:
            error_msg = f"特征选择失败: {e!s}"
            self.logger.error(error_msg)
            raise DataProcessError(error_msg) from e

    def standardize_data(self) -> None:
        """数据标准化阶段

        协调数据标准化过程，对特征和目标数据进行标准化。
        特征和目标的标准化分别使用不同的Standardizer实例处理。
        实际的标准化逻辑由Standardizer实现。

        Raises:
            DataProcessError: 如果标准化失败
            ValueError: 如果特征或目标数据未初始化
        """
        self.logger.info("开始数据标准化...")
        try:
            if self._features is None:
                raise ValueError("特征数据未初始化，无法进行标准化")
            if self._targets is None:
                raise ValueError("目标数据未初始化，无法进行标准化")

            # 特征数据标准化
            self._standardizer = Standardizer() # Standardizer for features
            # Standardizer 需要 [N, F] 或 [N, S, F] 格式，当前 _features 是 [N, F]
            self._standardizer.fit(self._features)
            self._features = self._standardizer.transform(self._features)
            self.logger.info(f"特征数据标准化完成，形状: {self._features.shape}")

            # 目标数据标准化
            target_standardizer = Standardizer()
            target_standardizer.fit(self._targets)
            self._targets = target_standardizer.transform(self._targets)
            self.target_standardizer = target_standardizer  # 保存标准化器实例供后续使用
            self.logger.info(
                f"目标数据标准化完成，形状: {self._targets.shape}\n"
                f"目标值范围: [{self._targets.min().item():.2f}, {self._targets.max().item():.2f}]"
            )

            self.logger.info("数据标准化阶段完成")

        except Exception as e:
            error_msg = f"数据标准化失败: {e!s}"
            self.logger.error(error_msg)
            raise DataProcessError(error_msg) from e

    def group_channels(self) -> None:
        """多通道分组阶段

        注意：此方法已简化，不再使用MultiChannelFeatureProcessor
        保留此方法仅为了保持接口兼容性
        """
        if self._features is None:
            self.logger.warning("特征数据未初始化，跳过多通道分组阶段")
            return

        # 记录特征维度
        if self.feature_dim is None and self._features is not None:
            self.feature_dim = self._features.shape[1]

        self.logger.info(
            f"多通道分组阶段已简化\n"
            f"- 当前特征维度: {self.feature_dim if self.feature_dim else '未知'}"
        )
        # 不执行任何操作，保持特征不变

    def create_windows(self, window_size: int | None = None) -> None:
        """滑动窗口阶段

        协调滑动窗口创建过程，为模型训练准备时序数据。
        窗口大小和步长必须从配置文件(data节)读取。
        本方法负责窗口创建的协调工作，不包含复杂的窗口处理逻辑。

        Args:
            window_size: 可选窗口大小，如未提供则必须从配置读取

        Raises:
            DataProcessError: 如果窗口创建失败或配置无效
        """
        try:
            if self._features is None or self._targets is None:
                raise ValueError("特征或目标数据未初始化，请先执行数据加载和验证")

            # 从配置获取参数(不允许任何默认值)
            if isinstance(self.config, dict):
                if 'data' not in self.config or not isinstance(self.config['data'], dict):
                    raise ValueError("配置中缺少有效的数据配置段")
                config_data = self.config['data']
                effective_window_size = window_size if window_size is not None else config_data['window_size']
                stride = config_data['stride']
            elif hasattr(self.config, 'data'):
                config_data = self.config.data
                effective_window_size = window_size if window_size is not None else config_data.window_size
                stride = config_data.stride
            else:
                raise ValueError("无效的配置对象类型")

            # 参数验证
            if not isinstance(effective_window_size, int) or effective_window_size <= 0:
                raise ValueError(f"无效的窗口大小配置: {effective_window_size}")
            if not isinstance(stride, int) or stride <= 0:
                raise ValueError(f"无效的步长配置: {stride}")

            n_samples = len(self._features)
            if effective_window_size > n_samples:
                raise ValueError(f"窗口大小{effective_window_size}超过数据长度{n_samples}")

            # 创建窗口
            self.windows = []
            for i in range(0, n_samples - effective_window_size + 1, stride):
                feature_window = self._features[i:i+effective_window_size]
                target_window = self._targets[i:i+effective_window_size] # Extract the full window
                self.windows.append((feature_window, target_window))

            # 记录配置
            self.window_size = effective_window_size
            self.stride = stride
            self.feature_dim = self._features.shape[1]

            self.logger.info(
                f"滑动窗口创建完成(严格从配置读取参数)\n"
                f"- 窗口大小: {effective_window_size} (配置值)\n"
                f"- 滑动步长: {stride} (配置值)\n"
                f"- 窗口数量: {len(self.windows)}\n"
                f"- 特征维度: {self.feature_dim}"
            )

        except Exception as e:
            error_msg = f"滑动窗口创建失败: {e!s}"
            self.logger.error(error_msg)
            raise DataProcessError(error_msg) from e

    def prepare_for_model(self) -> dict[str, Tensor]:
        """模型输入准备阶段

        协调模型输入数据准备过程，将处理后的数据转换为适合模型的格式。
        本方法主要负责数据格式转换的协调工作，不包含复杂的数据处理逻辑。

        Returns:
            Dict[str, Tensor]: 包含特征和目标的字典

        Raises:
            DataProcessError: 如果数据准备失败
        """
        try:
            if not hasattr(self, 'windows') or not self.windows:
                raise ValueError("窗口数据未初始化，请先调用create_windows")

            # 准备批量特征和目标
            batch_features = []
            batch_targets = []

            for feature_window, target_window in self.windows:
                batch_features.append(feature_window)
                batch_targets.append(target_window)

            # 将列表转换为张量
            features_tensor = torch.stack(batch_features)
            targets_tensor = torch.stack(batch_targets)

            # 标准化逻辑已移至 standardize_data 方法
            # 这里只负责将窗口数据堆叠成 Tensor
            self.logger.info("模型准备阶段：堆叠窗口数据...")

            # 准备返回数据
            model_input = {
                'features': features_tensor,
                'targets': targets_tensor # 现在是标准化后的目标
            }

            self.logger.info(
                f"模型输入准备完成 (仅堆叠窗口)\n"
                f"- 最终特征形状 (批次, 窗口大小, 特征数): {features_tensor.shape}\n"
                f"- 最终目标形状 (批次, 窗口大小, 目标数): {targets_tensor.shape}"
            )

            return model_input

        except Exception as e:
            error_msg = f"模型输入准备失败: {e!s}"
            self.logger.error(error_msg)
            raise DataProcessError(error_msg) from e

    def run_pipeline(self, window_size: int | None = None) -> dict[str, Tensor]:
        """执行完整数据处理流水线

        作为纯粹的协调器，协调完整的数据处理流程，包括：
        1. 数据加载
        2. 数据清洗
        3. 特征工程 (可选)
        4. 特征选择 (可选)
        5. 数据标准化
        6. 数据验证
        7. 窗口创建
        8. 模型输入准备

        每个步骤都由专门的处理模块实现，本方法仅负责协调调用顺序和异常处理。

        Args:
            window_size: 滑动窗口大小(可选，默认从配置获取)

        Returns:
            Dict[str, Tensor]: 模型输入数据字典

        Raises:
            DataProcessError: 如果数据处理流程中出现错误
        """
        try:
            self.logger.info("开始执行数据处理流水线...")

            # 1. 数据加载
            self.load_data()
            # 日志已移至 load_data 方法内部

            # 2. 数据验证（在清洗前进行基本验证）
            self.validate_data()
            # 日志已移至 validate_data 方法内部

            # 3. 数据清洗
            self.clean_data()
            # 日志已移至 clean_data 方法内部

            # 4. 特征工程 (根据配置决定是否执行)
            try:
                # 检查特征工程全局开关
                if isinstance(self.config, ConfigManager):
                    # 确保路径存在
                    if not hasattr(self.config, 'feature_engineering') or not hasattr(self.config.feature_engineering, 'enable'):
                        raise AttributeError("配置中缺少 'feature_engineering.enable' 项")
                    feature_engineering_enabled = self.config.feature_engineering.enable
                elif isinstance(self.config, dict):
                    # 确保路径存在
                    if 'feature_engineering' not in self.config or not isinstance(self.config['feature_engineering'], dict) or \
                       'enable' not in self.config['feature_engineering']:
                        raise KeyError("配置中缺少 'feature_engineering.enable' 项")
                    feature_engineering_enabled = self.config['feature_engineering']['enable']
                else:
                    raise TypeError("无效的配置对象类型")

                if not isinstance(feature_engineering_enabled, bool):
                    raise TypeError("'feature_engineering.enable' 必须是布尔值")

            except (AttributeError, KeyError, TypeError) as e:
                self.logger.error(f"读取特征工程配置失败: {e}")
                raise DataProcessError(f"读取特征工程配置失败: {e}") from e

            if feature_engineering_enabled:
                self.logger.info("根据配置，执行特征工程...")
                self.engineer_features()
                # 日志已移至 engineer_features 方法内部
            else:
                self.logger.info("根据配置，跳过特征工程步骤。")

            # 5. 特征选择 (严格根据配置决定是否执行)
            config_path = "feature_selection.enable" # 明确目标配置项 (修正路径)
            try:
                feature_selection_enabled = None # 初始化

                if isinstance(self.config, ConfigManager):
                    # 严格检查路径是否存在 (修正路径)
                    if not hasattr(self.config, 'feature_selection') or not hasattr(self.config.feature_selection, 'enable'):
                        raise AttributeError(f"配置项 '{config_path}' 在 ConfigManager 中缺失") # 保持错误消息中的路径
                    feature_selection_enabled = self.config.feature_selection.enable # 修正访问路径

                elif isinstance(self.config, dict):
                    # 严格检查路径是否存在 (修正路径)
                    if 'feature_selection' not in self.config or not isinstance(self.config['feature_selection'], dict) or 'enable' not in self.config['feature_selection']:
                         raise KeyError(f"配置项 '{config_path}' 在字典配置中缺失")
                    feature_selection_enabled = self.config['feature_selection']['enable'] # 修正访问路径
                else:
                    # 配置对象类型无效
                    raise TypeError(f"无效的配置对象类型: {type(self.config)}")

                # 严格类型检查 - 必须是布尔值
                if not isinstance(feature_selection_enabled, bool):
                    raise TypeError(f"配置项 '{config_path}' 的值必须是布尔类型，但获取到的是: {type(feature_selection_enabled)}") # 保持错误消息中的路径

                self.logger.info(f"从配置 '{config_path}' 读取到特征选择启用状态: {feature_selection_enabled}") # 保持日志消息中的路径

            except (AttributeError, KeyError, TypeError) as e:
                # 捕获特定的配置错误，记录并重新抛出，中断流程
                error_msg = f"读取特征选择配置 '{config_path}' 失败: {e}" # 保持错误消息中的路径
                self.logger.error(error_msg)
                # 规则 60: 直接抛出异常，中断程序执行
                raise DataProcessError(error_msg) from e

            if feature_selection_enabled:
                self.logger.info("根据配置，执行特征选择...")
                self.select_features()
                # 日志已移至 select_features 方法内部
            else:
                self.logger.info("根据配置，跳过特征选择步骤。")
                # 确保记录特征维度
                if self._features is not None and self.feature_dim is None:
                     self.feature_dim = self._features.shape[1]
                     self.logger.info(f"跳过选择后特征形状: {self._features.shape}")

            # 6. 数据标准化 (新顺序)
            self.standardize_data()
            # 日志已移至 standardize_data 方法内部

            # 7. 标准化后数据质量检查
            self._check_data_quality()
            # 日志已移至 _check_data_quality 方法内部
            # 8. 窗口创建
            if window_size is None:
                # 检查配置中是否有窗口大小
                if isinstance(self.config, dict):
                    if 'data' not in self.config or not isinstance(self.config['data'], dict) or 'window_size' not in self.config['data']:
                        error_msg = "配置中缺少 data.window_size 配置项"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)
                    effective_window_size = self.config['data']['window_size']
                else:
                    if not hasattr(self.config, 'data') or not hasattr(self.config.data, 'window_size'):
                        error_msg = "配置中缺少 data.window_size 配置项"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)
                    effective_window_size = self.config.data.window_size
            else:
                effective_window_size = window_size

            self.create_windows(effective_window_size)
            # 日志已移至 create_windows 方法内部

            # 9. 模型输入准备 (仅堆叠)
            model_input = self.prepare_for_model()

            self.logger.info(
                f"数据处理流水线执行完成\n"
                f"- 最终特征形状: {model_input['features'].shape}\n"
                f"- 最终目标形状: {model_input['targets'].shape}\n"
                f"- 处理顺序: 加载 → 验证 → 清洗 → 工程(可选) → 选择(可选) → 标准化 → 质量检查 → 窗口 → 准备"
            )

            # 设置处理完成标志
            self._processed = True

            return model_input

        except Exception as e:
            error_msg = f"数据处理流水线执行失败: {e!s}"
            self.logger.error(error_msg)
            raise DataProcessError(error_msg) from e

    def _convert_to_dataframe(self) -> pd.DataFrame:
        """将内部tensor数据转换为pandas DataFrame

        协调数据格式转换，将特征数据转换为DataFrame格式，便于后续处理模块使用。
        如果目标数据已初始化，也会添加目标列。

        Returns:
            pd.DataFrame: 转换后的数据框

        Raises:
            ValueError: 如果特征数据未初始化
        """
        if self._features is None:
            raise ValueError("特征数据未初始化")

        # 确保数据是数组格式
        if isinstance(self._features, torch.Tensor):
            features_array = self._features.detach().cpu().numpy()
        else:
            features_array = self._features

        # 创建特征DataFrame，必须使用存储的特征名称
        num_features = features_array.shape[1]
        if self._feature_names and len(self._feature_names) == num_features:
            feature_columns = self._feature_names
            self.logger.info(f"使用原始特征名称: {feature_columns}")
        else:
            error_msg = ""
            if self._feature_names: # 如果存在但不匹配
                 error_msg = f"存储的特征名称数量 ({len(self._feature_names)}) 与实际特征数量 ({num_features}) 不匹配。"
                 self.logger.error(f"列名不匹配: {error_msg}")
                 self.logger.error(f"存储的特征名称: {self._feature_names}")

                 # 不再尝试恢复或使用备份，直接抛出异常
                 error_msg = f"特征名称数量 ({len(self._feature_names)}) 与实际特征数量 ({num_features}) 不匹配"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)
            else: # 如果不存在
                 # 不再尝试使用备份，直接抛出异常
                 error_msg = "特征名称列表不存在，无法创建DataFrame"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)

        features_df = pd.DataFrame(features_array, columns=pd.Index(feature_columns))

        # 添加日期列 (如果存在)
        if self._dates is not None:
            # 将日期插入到第一列，确保后续处理模块能找到它
            date_values = self._dates.to_numpy()  # 转换为 numpy 数组
            features_df.insert(0, 'date', date_values)

        # 添加目标列 (如果存在)
        if self._targets is not None:
            # 获取目标列名
            if not hasattr(self.config, 'data') or not hasattr(self.config.data, 'target'):
                error_msg = "配置中缺少 data.target 配置项"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            target_col = self.config.data.target
            # 将目标数据转换为numpy数组
            if isinstance(self._targets, torch.Tensor):
                targets_array = self._targets.detach().cpu().numpy()
            else:
                targets_array = self._targets
            # 添加目标列
            features_df[target_col] = targets_array
            self.logger.info(f"已将目标列 '{target_col}' 添加到DataFrame中")

        return features_df

    def _update_from_dataframe(self, df: pd.DataFrame) -> None:
        """从特征工程等步骤返回的DataFrame更新内部特征数据和名称列表

        协调数据格式转换，将处理后的DataFrame数据（可能包含新的或修改过的特征列，
        以及可能仍然存在的日期列和目标列）转换回tensor格式，并更新内部特征数据。
        目标列和日期列会被识别并排除，剩余列被视为新的特征集。

        Args:
            df: 包含处理后数据的DataFrame

        Raises:
            ValueError: 如果DataFrame为空或处理后没有剩余的特征列
        """
        if df.empty:
            raise ValueError("输入DataFrame为空，无法更新特征")

        # 检查是否包含非数值数据 (仅检查潜在的特征列)
        date_col_name = 'date'
        target_col_name = self.config.data.target
        potential_feature_cols = [col for col in df.columns if col not in [date_col_name, target_col_name]]

        if not potential_feature_cols:
             error_msg = f"从DataFrame更新时未能识别出任何潜在的特征列 (已排除: {[date_col_name, target_col_name]})。DataFrame 列: {df.columns.tolist()}"
             self.logger.error(error_msg)
             raise ValueError(error_msg) # 强制报错，因为没有特征可以更新

        # 检查潜在特征列中的NaN值
        if df[potential_feature_cols].isnull().any().any():
             self.logger.info(f"输入DataFrame的特征列 {potential_feature_cols} 包含空值，后续步骤可能失败。")

        # 提取最终的特征列数据和名称
        final_feature_df = df[potential_feature_cols]
        self._features = torch.from_numpy(final_feature_df.values.astype('float32'))

        # 如果原始特征名称备份不存在，先创建备份
        if self._original_feature_names is None and self._feature_names:
            self._original_feature_names = self._feature_names.copy()
            self.logger.info(f"创建原始特征名称备份: {self._original_feature_names}")

        # 更新特征名称列表
        self._feature_names = potential_feature_cols
        self.feature_dim = self._features.shape[1] # 更新特征维度
        self.logger.info(f"从DataFrame更新特征数据，新特征名称: {self._feature_names}, 新形状: {self._features.shape}")

        # 检查特征和目标的样本数是否一致，如果不一致则记录警告
        if self._targets is not None and self._features.nelement() > 0 and len(self._features) != len(self._targets):
             self.logger.warning(f"特征和目标的样本数不一致 | 特征样本数: {len(self._features)} | 目标样本数: {len(self._targets)}")

    def __len__(self) -> int:
        """返回数据集长度

        Returns:
            int: 数据集长度（窗口数量）
        """
        if hasattr(self, 'windows') and self.windows:
            return len(self.windows)
        elif self._features is not None:
            return len(self._features)
        else:
            return 0

    def __getitem__(self, index: int) -> dict[str, torch.Tensor]:
        """获取指定索引的数据样本

        Args:
            index: 样本索引

        Returns:
            Dict[str, torch.Tensor]: 包含特征和目标的字典

        Raises:
            IndexError: 如果索引超出范围
        """
        if hasattr(self, 'windows') and self.windows:
            if index >= len(self.windows):
                raise IndexError(f"索引{index}超出范围[0, {len(self.windows)-1}]")

            feature_window, target_window = self.windows[index]
            return {
                'features': feature_window,
                'target': target_window
            }
        else:
            return self._get_data([index])

    def get_all_data(self) -> dict[str, torch.Tensor]:
        """获取全部数据

        Returns:
            Dict[str, torch.Tensor]: 包含所有数据的字典
        """
        if hasattr(self, 'windows') and self.windows:
            batch_features = []
            batch_targets = []

            for feature_window, target_window in self.windows:
                batch_features.append(feature_window)
                batch_targets.append(target_window)

            return {
                'features': torch.stack(batch_features),
                'targets': torch.stack(batch_targets)
            }
        elif self._features is not None and self._targets is not None:
            return {
                'features': self._features,
                'targets': self._targets
            }
        else:
            return {'features': torch.tensor([]), 'targets': torch.tensor([])}

    def _get_data(self, indices: list[int]) -> dict[str, Tensor]:
        """获取指定索引的原始数据

        从原始特征和目标数据中获取指定索引的数据。
        这是一个内部辅助方法，主要用于支持__getitem__方法。

        Args:
            indices: 数据索引列表

        Returns:
            Dict[str, Tensor]: 包含特征和目标的字典

        Raises:
            RuntimeError: 如果数据未经过处理
            ValueError: 如果特征或目标数据未初始化
        """
        if not self._processed:
            raise RuntimeError("数据未处理，请先调用run_pipeline()")

        if self._features is None or self._targets is None:
            raise ValueError("特征或目标数据未初始化")

        # 确保索引有效
        max_idx = len(self._features) - 1
        for idx in indices:
            if idx < 0 or idx > max_idx:
                raise IndexError(f"索引{idx}超出范围[0, {max_idx}]")

        return {
            'features': self._features[indices],
            'targets': self._targets[indices]
        }

    def _check_data_quality(self) -> None:
        """标准化后数据质量检查

        执行时机：
        - 在数据标准化后执行
        - 在窗口创建和模型输入准备之前执行
        - 作为数据处理流水线的第七步（...→标准化→质量检查→窗口→准备）

        功能：
        1. 检查标准化后的数据是否包含NaN或Inf值
        2. 检查数据范围是否在合理区间内（最大绝对值检查）
        3. 检查数据变异性是否足够（方差检查）
        4. 确保数据质量满足模型训练要求

        实现方式：
        - 调用DataQualityChecker类的check_data方法
        - 实际的检查逻辑由DataQualityChecker实现
        - 任何检查失败都会抛出异常，停止执行

        与其他验证步骤的区别：
        - 与validate_data方法的区别：本方法在数据标准化后执行，而validate_data在数据加载后立即执行
        - 本方法检查张量数据（Tensor），而validate_data检查DataFrame数据

        Raises:
            DataProcessError: 如果数据质量检查失败
        """
        try:
            if self._features is None or self._targets is None:
                raise ValueError("特征或目标数据未初始化，无法进行数据质量检查")

            # 导入DataQualityChecker
            from src.data.data_quality_checker import DataQualityChecker

            # 获取配置参数
            max_abs_value = 10.0  # 默认值
            min_variance = 1e-6   # 默认值

            # 尝试从配置中读取参数
            if isinstance(self.config, dict):
                if 'data_quality' in self.config and isinstance(self.config['data_quality'], dict):
                    quality_config = self.config['data_quality']
                    if 'max_abs_value' in quality_config:
                        max_abs_value = quality_config['max_abs_value']
                    if 'min_variance' in quality_config:
                        min_variance = quality_config['min_variance']
            elif hasattr(self.config, 'data_quality'):
                if hasattr(self.config.data_quality, 'max_abs_value'):
                    max_abs_value = self.config.data_quality.max_abs_value
                if hasattr(self.config.data_quality, 'min_variance'):
                    min_variance = self.config.data_quality.min_variance

            self.logger.info(f"开始标准化后数据质量检查，参数：max_abs_value={max_abs_value}, min_variance={min_variance}")

            # 执行数据质量检查
            DataQualityChecker.check_data(
                features=self._features,
                targets=self._targets,
                max_abs_value=max_abs_value,
                min_variance=min_variance
            )

            self.logger.info("标准化后数据质量检查通过")

        except Exception as e:
            # 检查是否是DataQualityError
            if e.__class__.__name__ == 'DataQualityError':
                error_msg = f"数据质量检查失败: {e!s}"
                self.logger.error(error_msg)
                raise DataProcessError(error_msg) from e
            else:
                # 其他异常
                error_msg = f"数据质量检查过程中发生意外错误: {e!s}"
                self.logger.error(error_msg)
                raise DataProcessError(error_msg) from e

    @property
    def get_target_standardizer(self) -> Standardizer | None:
        """获取目标变量的Standardizer实例"""
        if not hasattr(self, 'target_standardizer'):
             self.logger.warning("尝试获取目标Standardizer，但尚未初始化")
             return None
        return self.target_standardizer

__all__ = ['DataPipeline']
