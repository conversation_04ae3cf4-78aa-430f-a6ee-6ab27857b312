"""标准化处理器测试模块

相关模块:
1. 被测试模块:
   - src/data/standardization.py: 标准化处理实现
2. 依赖模块:
   - src/models/base/base_module.py: 基础模块
   - src/utils/logger.py: 日志系统
"""

from unittest.mock import patch

import numpy as np
import pytest
import torch

from src.data.standardization import Standardizer


@pytest.fixture
def sample_data():
    """创建测试数据"""
    # 生成2D测试数据
    data_2d = torch.tensor([
        [1.0, 2.0, 3.0],
        [4.0, 5.0, 6.0],
        [7.0, 8.0, 9.0]
    ])
    return data_2d

@pytest.fixture
def sample_data_3d():
    """创建3D测试数据"""
    # 生成3D测试数据，模拟时间序列
    data_3d = torch.tensor([
        [[1.0, 2.0], [3.0, 4.0]],
        [[5.0, 6.0], [7.0, 8.0]]
    ])
    return data_3d

@pytest.mark.batch3  # 标准化处理器测试
class TestStandardizer:
    """测试标准化处理器"""

    def test_initialization(self):
        """测试初始化"""
        processor = Standardizer()
        assert processor.scaler is None

    def test_fit_2d(self, sample_data):
        """测试2D数据拟合"""
        processor = Standardizer()
        processor.fit(sample_data)

        # Roo-Add: Add assertion to ensure scaler is not None after fit
        assert processor.scaler is not None, "Scaler should be initialized after fit"

        # 验证均值和标准差计算
        assert 'mean' in processor.scaler
        assert 'std' in processor.scaler
        assert torch.allclose(processor.scaler['mean'], sample_data.mean(dim=0, keepdim=True))
        assert torch.allclose(
            processor.scaler['std'],
            sample_data.std(dim=0, keepdim=True) + 1e-8
        )

    def test_fit_3d(self, sample_data_3d):
        """测试3D数据拟合"""
        processor = Standardizer()
        processor.fit(sample_data_3d)

        # Roo-Add: Add assertion to ensure scaler is not None after fit
        assert processor.scaler is not None, "Scaler should be initialized after fit"

        # 验证3D数据处理
        assert processor.scaler['mean'].dim() == sample_data_3d.dim()
        assert processor.scaler['std'].dim() == sample_data_3d.dim()

    def test_transform(self, sample_data):
        """测试标准化转换"""
        processor = Standardizer()
        processor.fit(sample_data)

        # 执行转换
        transformed = processor.transform(sample_data)

        # 验证转换结果
        expected_mean = torch.zeros_like(transformed.mean(dim=0))
        expected_std = torch.ones_like(transformed.std(dim=0))

        assert torch.allclose(transformed.mean(dim=0), expected_mean, atol=1e-6)
        assert torch.allclose(transformed.std(dim=0), expected_std, atol=1e-6)

    def test_inverse_transform(self, sample_data):
        """测试逆向转换"""
        processor = Standardizer()
        processor.fit(sample_data)

        # 执行转换和逆转换
        transformed = processor.transform(sample_data)
        recovered = processor.inverse_transform(transformed)

        # 验证恢复结果
        assert torch.allclose(recovered, sample_data)

    def test_numpy_input(self):
        """测试numpy数组输入"""
        processor = Standardizer()
        numpy_data = np.array([[1.0, 2.0], [3.0, 4.0]])

        # 测试fit和transform支持numpy输入
        processor.fit(numpy_data)
        transformed = processor.transform(numpy_data)

        assert isinstance(transformed, torch.Tensor)

    def test_invalid_dimensions(self):
        """测试无效维度输入"""
        processor = Standardizer()

        # 1D数据
        with pytest.raises(ValueError):
            processor.fit(torch.tensor([1.0, 2.0, 3.0]))

        # 4D数据
        with pytest.raises(ValueError):
            processor.fit(torch.randn(2, 3, 4, 5))

    def test_transform_before_fit(self):
        """测试未拟合就转换"""
        processor = Standardizer()

        with pytest.raises(RuntimeError):
            processor.transform(torch.randn(3, 4))

    def test_inverse_transform_before_fit(self):
        """测试未拟合就逆转换"""
        processor = Standardizer()

        with pytest.raises(RuntimeError):
            processor.inverse_transform(torch.randn(3, 4))

    @patch('src.data.standardization.time.time')
    def test_logging(self, mock_time, sample_data):
        """测试日志输出"""
        mock_time.side_effect = [0, 1]  # 模拟耗时1秒

        processor = Standardizer()

        with patch.object(processor.logger, 'info') as mock_logger:
            processor.fit(sample_data)

            # 验证日志调用
            assert mock_logger.called
            # 验证日志包含关键信息
            log_msg = mock_logger.call_args[0][0]
            assert '标准化参数计算' in log_msg
            assert '输入形状' in log_msg
            assert '均值范围' in log_msg
            assert '标准差范围' in log_msg
