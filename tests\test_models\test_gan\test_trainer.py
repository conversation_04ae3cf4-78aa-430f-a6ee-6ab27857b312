"""训练器测试模块

相关模块:
1. 被测试模块:
   - src/models/gan/trainer.py: 训练器实现
2. 依赖模块:
   - src/utils/config_manager.py: 配置管理
   - src/utils/cuda_manager.py: GPU资源管理
   - src/models/base/base_module.py: 基础模块
   - src/models/gan/gan_model.py: GAN模型
"""

from unittest.mock import MagicMock, patch

import pytest
import torch

from src.models.gan.gan_model import GANModel
from src.models.gan.trainer import GANTrainer
from src.utils.config_manager import ConfigManager


@pytest.fixture
def sample_config():
    """创建测试配置"""
    config = ConfigManager.from_yaml("tests/test_config.yaml")
    # 创建必要的配置结构
    config.model = MagicMock()
    config.model.mixed_precision = MagicMock()
    config.model.mixed_precision.enabled = False

    # 训练配置
    config.training = MagicMock()
    config.training.batch_size = 32
    config.training.num_epochs = 10
    config.training.learning_rate = 1e-4
    config.training.early_stopping = MagicMock()
    config.training.early_stopping.patience = 5
    config.training.early_stopping.min_delta = 1e-4

    # 添加混合精度配置 (修复TypeError)
    config.training.mixed_precision = MagicMock()
    config.training.mixed_precision.enabled = True  # 设为True以测试初始化逻辑
    config.training.mixed_precision.init_scale = 65536.0
    config.training.mixed_precision.growth_factor = 2.0
    config.training.mixed_precision.backoff_factor = 0.5
    config.training.mixed_precision.growth_interval = 2000

    # 路径配置
    config.paths = MagicMock()
    config.paths.model_dir = "outputs/models"

    return config

@pytest.fixture
def sample_model():
    """创建测试模型"""
    model = MagicMock(spec=GANModel)
    model.train_step = MagicMock(return_value={'g_loss': 1.0, 'd_loss': 0.5})
    model.generator = MagicMock()
    model.discriminator = MagicMock()
    model.predict = MagicMock(return_value=torch.randn(32, 100, 1, names=None)) # 修复 Pylance 错误
    return model

@pytest.fixture
def sample_batch():
    """创建测试批次数据"""
    return {
        'features': torch.randn(32, 100, 20, names=None), # 修复 Pylance 错误
        'target': torch.randn(32, 100, 1, names=None) # 修复 Pylance 错误
    }

@pytest.fixture
def sample_train_loader(sample_batch):
    """创建测试训练数据加载器"""
    loader = MagicMock()
    loader.__iter__.return_value = [sample_batch for _ in range(10)]
    loader.__len__.return_value = 10
    return loader

@pytest.fixture
def sample_val_loader(sample_batch):
    """创建测试验证数据加载器"""
    loader = MagicMock()
    loader.__iter__.return_value = [sample_batch for _ in range(5)]
    loader.__len__.return_value = 5
    return loader

@pytest.mark.batch2  # 训练器测试
class TestGANTrainer:
    """测试GAN训练器"""

    def test_initialization(self, sample_config, sample_model, sample_train_loader, sample_val_loader):
        """测试训练器初始化"""
        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager'), \
             patch('src.models.gan.trainer.OptimizerManager'), \
             patch('src.models.gan.trainer.GANEvaluator'), \
             patch('src.models.gan.trainer.ModelSaver'), \
             patch('src.models.gan.trainer.cuda_manager'):

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model,
                train_loader=sample_train_loader,
                val_loader=sample_val_loader
            )

            # 验证训练器属性
            assert trainer.model == sample_model, "模型设置不正确"
            assert trainer.train_loader == sample_train_loader, "训练数据加载器设置不正确"
            assert trainer.val_loader == sample_val_loader, "验证数据加载器设置不正确"
            assert hasattr(trainer, 'optimizer_manager'), "缺少optimizer_manager属性"
            assert hasattr(trainer, 'state_manager'), "缺少state_manager属性"
            assert hasattr(trainer, 'evaluator'), "缺少evaluator属性"
            assert hasattr(trainer, 'model_saver'), "缺少model_saver属性"

    def test_train_step(self, sample_config, sample_model, sample_batch):
        """测试训练步骤"""
        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager'), \
             patch('src.models.gan.trainer.OptimizerManager'), \
             patch('src.models.gan.trainer.GANEvaluator'), \
             patch('src.models.gan.trainer.ModelSaver'), \
             patch('src.models.gan.trainer.cuda_manager') as mock_cuda_manager:

            # 设置模拟CUDA管理器
            mock_cuda_manager.device = torch.device('cpu')

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model
            )

            # 执行训练步骤
            metrics = trainer._train_step(sample_batch, batch_idx=0, total_batches=10)

            # 验证模型train_step被调用
            sample_model.train_step.assert_called_once()

            # 验证返回的指标
            assert 'g_loss' in metrics, "返回的指标应包含g_loss"
            assert 'd_loss' in metrics, "返回的指标应包含d_loss"
            assert metrics['g_loss'] == 1.0, "g_loss值不正确"
            assert metrics['d_loss'] == 0.5, "d_loss值不正确"

    def test_train_epoch(self, sample_config, sample_model, sample_train_loader):
        """测试训练轮次"""
        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager'), \
             patch('src.models.gan.trainer.OptimizerManager'), \
             patch('src.models.gan.trainer.GANEvaluator'), \
             patch('src.models.gan.trainer.ModelSaver'), \
             patch('src.models.gan.trainer.cuda_manager') as mock_cuda_manager, \
             patch.object(GANTrainer, '_train_step') as mock_train_step:

            # 设置模拟CUDA管理器
            mock_cuda_manager.device = torch.device('cpu')

            # 设置模拟训练步骤返回值
            mock_train_step.return_value = {'g_loss': 1.0, 'd_loss': 0.5}

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model,
                train_loader=sample_train_loader
            )

            # 执行训练轮次
            metrics = trainer._train_epoch(sample_train_loader)

            # 验证训练步骤被调用次数
            assert mock_train_step.call_count == len(sample_train_loader), "训练步骤调用次数不正确"

            # 验证返回的指标
            assert 'g_loss' in metrics, "返回的指标应包含g_loss"
            assert 'd_loss' in metrics, "返回的指标应包含d_loss"
            assert isinstance(metrics['g_loss'], float), "g_loss应该是浮点数"
            assert isinstance(metrics['d_loss'], float), "d_loss应该是浮点数"

    def test_validation(self, sample_config, sample_model, sample_val_loader):
        """测试验证过程"""
        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager'), \
             patch('src.models.gan.trainer.OptimizerManager'), \
             patch('src.models.gan.trainer.GANEvaluator') as mock_evaluator_class, \
             patch('src.models.gan.trainer.ModelSaver'), \
             patch('src.models.gan.trainer.cuda_manager') as mock_cuda_manager:

            # 设置模拟CUDA管理器
            mock_cuda_manager.device = torch.device('cpu')

            # 设置模拟评估器
            mock_evaluator = MagicMock()
            mock_evaluator.evaluate.return_value = {'val_loss': 0.8, 'val_accuracy': 0.9}
            mock_evaluator_class.return_value = mock_evaluator

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model,
                val_loader=sample_val_loader
            )

            # 执行验证轮次
            metrics = trainer._validate_epoch(sample_val_loader)

            # 验证评估器被调用
            assert mock_evaluator.evaluate.call_count > 0, "评估器应该被调用"

            # 验证返回的指标
            assert 'val_loss' in metrics, "返回的指标应包含val_loss"
            assert 'val_accuracy' in metrics, "返回的指标应包含val_accuracy"
            assert metrics['val_loss'] == 0.8, "val_loss值不正确"
            assert metrics['val_accuracy'] == 0.9, "val_accuracy值不正确"

    def test_early_stopping(self, sample_config, sample_model, sample_train_loader, sample_val_loader):
        """测试早停机制"""
        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager') as mock_state_manager_class, \
             patch('src.models.gan.trainer.OptimizerManager'), \
             patch('src.models.gan.trainer.GANEvaluator'), \
             patch('src.models.gan.trainer.ModelSaver'), \
             patch('src.models.gan.trainer.cuda_manager'), \
             patch('src.models.gan.trainer.LoggerFactory') as mock_logger_factory, \
             patch.object(GANTrainer, '_train_epoch') as mock_train_epoch, \
             patch.object(GANTrainer, '_validate_epoch') as mock_validate_epoch:

            # 设置模拟日志记录器
            mock_logger = MagicMock()
            mock_logger_factory.return_value = MagicMock()
            mock_logger_factory.return_value.get_logger.return_value = mock_logger

            # 设置模拟状态管理器
            mock_state_manager = MagicMock()
            # 第3轮开始触发早停
            mock_state_manager.should_stop_training.side_effect = [False, False, True]
            mock_state_manager_class.return_value = mock_state_manager

            # 设置模拟训练状态
            mock_state_manager.training_state = MagicMock()
            mock_state_manager.training_state.best_loss = 0.5
            mock_state_manager.training_state.patience_counter = 3

            # 设置模拟训练和验证轮次返回值
            mock_train_epoch.return_value = {'generator': {'total': 1.0}, 'discriminator': {'total': 0.5}}
            mock_validate_epoch.return_value = {'loss': 0.8, 'accuracy': 0.9}

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model,
                train_loader=sample_train_loader,
                val_loader=sample_val_loader
            )

            # 执行训练
            # 调用 run_training_loop 并传递 batch_size
            history = trainer.run_training_loop(batch_size=32, num_epochs=10)

            # 验证训练和验证轮次被调用次数
            assert mock_train_epoch.call_count == 3, "训练轮次应该被调用3次"
            assert mock_validate_epoch.call_count == 3, "验证轮次应该被调用3次"

            # 验证返回的历史记录
            assert 'train_generator' in history, "历史记录应包含train_generator键"
            assert 'train_discriminator' in history, "历史记录应包含train_discriminator键"
            assert 'val_loss' in history, "历史记录应包含val_loss键"

    def test_learning_rate_scheduling(self, sample_config, sample_model, sample_train_loader, sample_val_loader):
        """测试学习率调度"""
        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager') as mock_state_manager_class, \
             patch('src.models.gan.trainer.OptimizerManager') as mock_optimizer_manager_class, \
             patch('src.models.gan.trainer.GANEvaluator'), \
             patch('src.models.gan.trainer.ModelSaver'), \
             patch('src.models.gan.trainer.cuda_manager'), \
             patch('src.models.gan.trainer.LoggerFactory') as mock_logger_factory, \
             patch.object(GANTrainer, '_train_epoch') as mock_train_epoch, \
             patch.object(GANTrainer, '_validate_epoch') as mock_validate_epoch:

            # 设置模拟日志记录器
            mock_logger = MagicMock()
            mock_logger_factory.return_value = MagicMock()
            mock_logger_factory.return_value.get_logger.return_value = mock_logger

            # 设置模拟状态管理器
            mock_state_manager = MagicMock()
            mock_state_manager.should_stop_training.return_value = False
            mock_state_manager_class.return_value = mock_state_manager

            # 设置模拟训练状态
            mock_state_manager.training_state = MagicMock()
            mock_state_manager.training_state.best_loss = 0.5
            mock_state_manager.training_state.patience_counter = 0

            # 设置模拟优化器管理器
            mock_optimizer_manager = MagicMock()
            mock_optimizer_manager.update_learning_rate = MagicMock()
            mock_optimizer_manager_class.return_value = mock_optimizer_manager

            # 设置模拟训练和验证轮次返回值
            mock_train_epoch.return_value = {'generator': {'total': 1.0}, 'discriminator': {'total': 0.5}}
            mock_validate_epoch.return_value = {'loss': 0.8, 'accuracy': 0.9}

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model,
                train_loader=sample_train_loader,
                val_loader=sample_val_loader
            )

            # 直接调用学习率更新方法
            trainer.optimizer_manager = mock_optimizer_manager

            # 直接调用学习率更新方法
            metrics = {'loss': 0.8}
            trainer.optimizer_manager.update_learning_rate(MagicMock(), metrics)
            trainer.optimizer_manager.update_learning_rate(MagicMock(), metrics)

            # 验证学习率更新被调用
            assert mock_optimizer_manager.update_learning_rate.call_count == 2, "学习率更新应该被调用2次"

    def test_checkpoint_saving(self, sample_config, sample_model, sample_train_loader, sample_val_loader):
        """测试检查点保存"""
        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager') as mock_state_manager_class, \
             patch('src.models.gan.trainer.OptimizerManager'), \
             patch('src.models.gan.trainer.GANEvaluator'), \
             patch('src.models.gan.trainer.ModelSaver') as mock_model_saver_class, \
             patch('src.models.gan.trainer.cuda_manager'), \
             patch.object(GANTrainer, '_train_epoch'), \
             patch.object(GANTrainer, '_validate_epoch'):

            # 设置模拟状态管理器
            mock_state_manager = MagicMock()
            mock_state_manager.should_stop_training.return_value = False
            mock_state_manager.should_save_checkpoint.return_value = True
            mock_state_manager.is_best_model.return_value = True
            mock_state_manager_class.return_value = mock_state_manager

            # 设置模拟模型保存器
            mock_model_saver = MagicMock()
            mock_model_saver.save_checkpoint = MagicMock()
            mock_model_saver_class.return_value = mock_model_saver

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model,
                train_loader=sample_train_loader,
                val_loader=sample_val_loader
            )

            # 执行训练
            # 调用 run_training_loop 并传递 batch_size
            trainer.run_training_loop(batch_size=32, num_epochs=2)

            # 验证检查点保存被调用
            assert mock_model_saver.save_checkpoint.call_count == 2, "检查点保存应该被调用2次"

    def test_device_compatibility(self, sample_config, sample_model, sample_train_loader, sample_val_loader):
        """测试设备兼容性"""
        # 跳过GPU测试，如果不可用
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过GPU测试")

        # 使用模拟对象创建训练器
        with patch('src.models.gan.trainer.ModelStateManager'), \
             patch('src.models.gan.trainer.OptimizerManager'), \
             patch('src.models.gan.trainer.GANEvaluator'), \
             patch('src.models.gan.trainer.ModelSaver'), \
             patch('src.models.gan.trainer.cuda_manager') as mock_cuda_manager:

            # 设置模拟CUDA管理器
            mock_cuda_manager.device = torch.device('cuda')

            # 创建训练器
            trainer = GANTrainer(
                config=sample_config,
                model=sample_model,
                train_loader=sample_train_loader,
                val_loader=sample_val_loader
            )

            # 验证设备设置
            assert trainer.device == torch.device('cuda'), "设备应该设置为CUDA"
