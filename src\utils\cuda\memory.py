"""CUDA 内存管理模块"""

import threading
import time
from typing import Any

import psutil
import torch

from src.utils.logger import get_logger

from .device import cuda_device_manager
from .types import GPUMemoryInfo


class CUDAMemoryManager:
    """CUDA内存管理器，负责内存监控和优化"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize()
        return cls._instance

    def _initialize(self):
        """初始化内存管理器"""
        self._logger = get_logger(__name__)

        # 内存监控相关属性
        self._monitoring_enabled = False
        self._monitoring_thread = None
        self._monitoring_interval = 5  # 默认监控间隔(秒)
        self._monitoring_data = {
            'gpu_memory': [],
            'gpu_utilization': [],
            'cpu_usage': [],
            'sys_memory': []
        }

        # 内存峰值监控相关属性
        self._peak_memory_monitoring_enabled = False
        self._peak_memory_monitoring_thread = None
        self._peak_memory_monitoring_interval = 5  # 默认每5秒采样一次
        self._peak_memory_window_size = 60  # 默认60秒窗口
        self._peak_memory_samples = []  # 存储采样数据
        self._peak_memory_lock = threading.Lock()  # 线程安全锁
        self._current_peak_memory_info = None  # 当前峰值内存信息

        # 批次内内存峰值监控相关属性
        self._batch_peak_memory_info = None  # 当前批次的峰值内存信息
        self._batch_peak_memory_lock = threading.Lock()  # 批次峰值的线程安全锁

    def get_memory_info(self) -> GPUMemoryInfo | None:
        """获取GPU内存信息

        Returns:
            Optional[GPUMemoryInfo]: GPU内存信息对象，如果CUDA不可用则返回None
        """
        if not cuda_device_manager.is_cuda_available:
            self._logger.warning("CUDA不可用，无法获取GPU内存信息")
            return None

        try:
            # 获取设备属性
            props = torch.cuda.get_device_properties(cuda_device_manager.device)
            total_memory = props.total_memory / (1024**3)  # GB

            # 获取已分配内存
            memory_allocated = torch.cuda.memory_allocated(cuda_device_manager.device) / (1024**3)

            # 获取已缓存内存
            memory_cached = torch.cuda.memory_reserved(cuda_device_manager.device) / (1024**3)

            # 计算可用内存
            memory_free = total_memory - memory_allocated

            # 计算利用率
            utilization = memory_allocated / total_memory if total_memory > 0 else 0

            # 创建并返回内存信息对象
            memory_info = GPUMemoryInfo(
                used_gb=memory_allocated,
                free_gb=memory_free,
                cached_gb=memory_cached,
                total_gb=total_memory,
                utilization=utilization
            )

            self._logger.debug(str(memory_info))
            return memory_info

        except Exception as e:
            error_msg = f"获取GPU内存信息失败: {e!s}"
            self._logger.error(error_msg)
            raise RuntimeError(error_msg)

    def get_memory_stats(self) -> dict[str, float]:
        """获取内存统计数据，以MB为单位"""
        if not cuda_device_manager.is_cuda_available:
            return {
                'allocated_mb': 0,
                'reserved_mb': 0,
                'max_allocated_mb': 0,
                'max_reserved_mb': 0
            }

        try:
            return {
                'allocated_mb': torch.cuda.memory_allocated() / (1024 * 1024),
                'reserved_mb': torch.cuda.memory_reserved() / (1024 * 1024),
                'max_allocated_mb': torch.cuda.max_memory_allocated() / (1024 * 1024),
                'max_reserved_mb': torch.cuda.max_memory_reserved() / (1024 * 1024)
            }
        except Exception as e:
            self._logger.error(f"获取内存统计失败: {e!s}")
            raise RuntimeError(f"获取内存统计失败: {e!s}")

    def get_peak_memory_info(self) -> GPUMemoryInfo | None:
        """获取窗口内的峰值内存信息

        Returns:
            Optional[GPUMemoryInfo]: 窗口内的峰值内存信息，如果监控未启动则返回None
        """
        if not self._peak_memory_monitoring_enabled:
            self._logger.warning("峰值内存监控未启动")
            return None

        with self._peak_memory_lock:
            if not self._current_peak_memory_info:
                self._logger.warning("峰值内存监控数据不足")
                return None

            self._logger.debug(
                f"返回窗口内存峰值信息:\n"
                f"- 峰值利用率: {self._current_peak_memory_info.utilization * 100:.1f}%\n"
                f"- 峰值已用内存: {self._current_peak_memory_info.used_gb:.2f}GB\n"
                f"- 窗口大小: {self._peak_memory_window_size}秒\n"
                f"- 样本数量: {len(self._peak_memory_samples)}"
            )
            return self._current_peak_memory_info

    def optimize_memory(self, threshold: float):
        """优化GPU内存使用

        Args:
            threshold: 内存使用率阈值，超过则触发优化
        """
        if not cuda_device_manager.is_cuda_available:
            return

        memory_info = self.get_memory_info()
        if memory_info and memory_info.utilization > threshold:
            self._logger.warning(
                f"GPU内存使用率过高: {memory_info.utilization:.1%}\n"
                f"- 已用: {memory_info.used_gb:.1f}GB\n"
                f"- 缓存: {memory_info.cached_gb:.1f}GB\n"
                f"- 可用: {memory_info.free_gb:.1f}GB"
            )
            self.clear_cache()

    def clear_cache(self):
        """清理CUDA缓存"""
        if cuda_device_manager.is_cuda_available:
            torch.cuda.empty_cache()
            self._logger.debug("已清理CUDA缓存")

    def reset_stats(self):
        """重置内存统计数据"""
        if cuda_device_manager.is_cuda_available:
            torch.cuda.reset_peak_memory_stats()
            torch.cuda.reset_accumulated_memory_stats()
            self._logger.info("已重置内存统计数据")

    def start_monitoring(self, interval: int = 5):
        """启动内存监控

        Args:
            interval: 监控间隔(秒)
        """
        if not cuda_device_manager.is_cuda_available:
            self._logger.warning("CUDA不可用，无法启动监控")
            return

        if self._monitoring_thread is not None and self._monitoring_thread.is_alive():
            self._logger.warning("监控线程已在运行")
            return

        self._monitoring_interval = interval
        self._monitoring_enabled = True
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        self._logger.info(f"启动内存监控，间隔: {interval}秒")

    def stop_monitoring(self):
        """停止内存监控"""
        self._monitoring_enabled = False
        if self._monitoring_thread is not None:
            self._monitoring_thread.join(timeout=2)
        self._logger.info("内存监控已停止")

    def _monitoring_loop(self):
        """监控循环"""
        while self._monitoring_enabled:
            try:
                timestamp = time.time()

                # 获取GPU内存信息
                memory_info = self.get_memory_info()
                if memory_info:
                    self._monitoring_data['gpu_memory'].append({
                        'timestamp': timestamp,
                        **memory_info.to_dict()
                    })

                # 获取GPU利用率
                try:
                    import subprocess
                    result = subprocess.run(
                        ['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'],
                        capture_output=True,
                        text=True,
                        check=True
                    )
                    gpu_util = float(result.stdout.strip())
                    self._monitoring_data['gpu_utilization'].append({
                        'timestamp': timestamp,
                        'utilization': gpu_util
                    })
                except Exception:
                    # 如果无法获取GPU利用率，使用内存利用率作为近似值
                    if memory_info:
                        self._monitoring_data['gpu_utilization'].append({
                            'timestamp': timestamp,
                            'utilization': memory_info.utilization * 100
                        })

                # 获取CPU使用率
                cpu_usage = psutil.cpu_percent(interval=0.1)
                self._monitoring_data['cpu_usage'].append({
                    'timestamp': timestamp,
                    'usage': cpu_usage
                })

                # 获取系统内存信息
                mem = psutil.virtual_memory()
                self._monitoring_data['sys_memory'].append({
                    'timestamp': timestamp,
                    'total_gb': mem.total / (1024**3),
                    'used_gb': mem.used / (1024**3),
                    'free_gb': mem.free / (1024**3),
                    'percent': mem.percent
                })

                # 限制历史数据量
                for key in self._monitoring_data:
                    if len(self._monitoring_data[key]) > 1000:
                        self._monitoring_data[key] = self._monitoring_data[key][-1000:]

                time.sleep(self._monitoring_interval)
            except Exception as e:
                self._logger.error(f"内存监控循环出错: {e!s}")
                time.sleep(1.0)

    def get_monitoring_data(self) -> dict[str, list[dict[str, Any]]]:
        """获取监控数据

        Returns:
            Dict[str, List[Dict[str, Any]]]: 监控数据历史记录
        """
        return self._monitoring_data

    def start_peak_memory_monitoring(self, interval: int = 5, window_size: int = 60):
        """启动峰值内存监控

        Args:
            interval: 采样间隔(秒)
            window_size: 窗口大小(秒)
        """
        if not cuda_device_manager.is_cuda_available:
            self._logger.warning("CUDA不可用，无法启动峰值内存监控")
            return

        if self._peak_memory_monitoring_thread is not None and self._peak_memory_monitoring_thread.is_alive():
            self._logger.warning("峰值内存监控线程已在运行")
            return

        self._peak_memory_monitoring_interval = interval
        self._peak_memory_window_size = window_size
        self._peak_memory_monitoring_enabled = True
        self._peak_memory_samples = []

        self._peak_memory_monitoring_thread = threading.Thread(
            target=self._peak_memory_monitoring_loop,
            daemon=True,
            name="PeakMemoryMonitor"
        )
        self._peak_memory_monitoring_thread.start()
        self._logger.info(f"启动峰值内存监控，间隔: {interval}秒，窗口: {window_size}秒")

    def stop_peak_memory_monitoring(self):
        """停止峰值内存监控"""
        self._peak_memory_monitoring_enabled = False
        if self._peak_memory_monitoring_thread:
            self._peak_memory_monitoring_thread.join(timeout=2)
        self._peak_memory_monitoring_thread = None
        self._logger.info("峰值内存监控已停止")

    def _peak_memory_monitoring_loop(self):
        """峰值内存监控循环"""
        while self._peak_memory_monitoring_enabled:
            try:
                memory_info = self.get_memory_info()
                if memory_info:
                    with self._peak_memory_lock:
                        now = time.time()
                        self._peak_memory_samples.append({
                            'timestamp': now,
                            'memory_info': memory_info
                        })

                        # 移除过期样本
                        cutoff = now - self._peak_memory_window_size
                        self._peak_memory_samples = [
                            s for s in self._peak_memory_samples
                            if s['timestamp'] >= cutoff
                        ]

                        # 更新峰值
                        if self._peak_memory_samples:
                            self._current_peak_memory_info = max(
                                self._peak_memory_samples,
                                key=lambda s: s['memory_info'].utilization
                            )['memory_info']

                            self._logger.debug(
                                f"更新峰值内存信息: "
                                f"{self._current_peak_memory_info.utilization*100:.1f}% "
                                f"({self._current_peak_memory_info.used_gb:.2f}GB)"
                            )

                    # 同时更新批次内峰值内存信息
                    self._update_batch_peak_memory(memory_info)

                time.sleep(self._peak_memory_monitoring_interval)
            except Exception as e:
                self._logger.error(f"峰值内存监控循环出错: {e!s}")
                time.sleep(1.0)

    def _update_batch_peak_memory(self, memory_info: GPUMemoryInfo) -> None:
        """更新批次内峰值内存信息

        Args:
            memory_info: 当前内存信息
        """
        if memory_info is None:
            return

        with self._batch_peak_memory_lock:
            # 如果当前批次还没有峰值记录或当前内存利用率更高，则更新
            if (self._batch_peak_memory_info is None or
                memory_info.utilization > self._batch_peak_memory_info.utilization):
                self._batch_peak_memory_info = memory_info
                self._logger.debug(
                    f"更新批次内峰值内存信息: "
                    f"{self._batch_peak_memory_info.utilization*100:.1f}% "
                    f"({self._batch_peak_memory_info.used_gb:.2f}GB)"
                )

    def reset_batch_peak_memory(self) -> None:
        """重置批次内峰值内存统计

        在每个批次开始前调用，重置批次内峰值内存记录
        """
        with self._batch_peak_memory_lock:
            self._batch_peak_memory_info = None
            # 同时重置CUDA峰值内存统计
            if cuda_device_manager.is_cuda_available:
                torch.cuda.reset_peak_memory_stats()
                self._logger.debug("已重置批次内峰值内存统计和CUDA峰值内存统计")

    def get_batch_peak_memory_info(self) -> GPUMemoryInfo | None:
        """获取批次内峰值内存信息

        Returns:
            Optional[GPUMemoryInfo]: 批次内的峰值内存信息，如果未记录则返回None
        """
        with self._batch_peak_memory_lock:
            # 如果没有批次内峰值记录，尝试获取当前内存信息作为峰值
            if self._batch_peak_memory_info is None:
                current_info = self.get_memory_info()
                if current_info:
                    self._batch_peak_memory_info = current_info
                    self._logger.debug(
                        f"批次内峰值未记录，使用当前内存信息: "
                        f"{current_info.utilization*100:.1f}% "
                        f"({current_info.used_gb:.2f}GB)"
                    )

            if self._batch_peak_memory_info:
                self._logger.debug(
                    f"返回批次内峰值内存信息: "
                    f"{self._batch_peak_memory_info.utilization*100:.1f}% "
                    f"({self._batch_peak_memory_info.used_gb:.2f}GB)"
                )
            else:
                self._logger.warning("批次内峰值内存信息不可用")

            return self._batch_peak_memory_info


# 全局单例
try:
    cuda_memory_manager = CUDAMemoryManager()
except Exception as e:
    logger = get_logger("CUDAMemoryManager")
    logger.error(f"CUDA内存管理器初始化失败: {e!s}")
    raise RuntimeError(f"CUDA内存管理器初始化失败: {e!s}")
