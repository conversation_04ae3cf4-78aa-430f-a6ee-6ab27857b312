"""窗口时序数据集扩展测试模块

相关模块:
1. 被测试模块:
   - src/data/windowed_time_series.py: 窗口时序数据集实现
2. 依赖模块:
   - src/data/protocol.py: 数据集协议
   - src/utils/logger.py: 日志系统
"""

import numpy as np
import pandas as pd
import pytest
import torch

from src.data.windowed_time_series import TimeSeriesWindowDataset


@pytest.fixture
def sample_data():
    """创建测试数据"""
    data = {
        'date': pd.date_range(start='2024-01-01', periods=100),
        'value1': np.sin(np.linspace(0, 10, 100)),
        'value2': np.cos(np.linspace(0, 10, 100)),
        'value3': np.sin(np.linspace(0, 10, 100)) * np.cos(np.linspace(0, 10, 100)),
        'value15': np.sin(np.linspace(0, 10, 100)) + np.random.normal(0, 0.1, 100)
    }
    return pd.DataFrame(data)

@pytest.fixture
def sample_config():
    """创建测试配置"""
    return {
        'data': {
            'window_size': 10,
            'stride': 2,
            'target': 'value15',
            'columns': {
                'numeric': ['value1', 'value2', 'value3']
            },
            'train_ratio': 0.7,
            'val_ratio': 0.15,
            'test_ratio': 0.15
        }
    }

@pytest.fixture
def numpy_data():
    """创建NumPy数组测试数据"""
    # 创建特征和目标数据
    features = np.random.rand(100, 3)  # 100个样本，3个特征
    targets = np.random.rand(100, 1)   # 100个样本，1个目标
    # 合并为一个数组，最后一列为目标
    return np.hstack((features, targets))

@pytest.fixture
def tensor_data():
    """创建PyTorch张量测试数据"""
    # 创建特征和目标数据
    features = torch.rand(100, 3)  # 100个样本，3个特征
    targets = torch.rand(100, 1)   # 100个样本，1个目标
    # 合并为一个张量，最后一列为目标
    return torch.cat((features, targets), dim=1)

@pytest.mark.batch2  # 关键组件测试
class TestWindowedTimeSeriesExtended:
    """测试窗口时序数据集扩展功能"""

    def test_window_creation_boundary_conditions(self, sample_config, sample_data):
        """测试窗口创建逻辑和边界条件"""
        # 测试窗口大小等于数据长度的情况
        window_size = len(sample_data)
        dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            window_size=window_size,
            stride=1
        )
        # 应该只创建一个窗口
        assert len(dataset) == 1

        # 测试窗口大小接近数据长度的情况
        window_size = len(sample_data) - 1
        dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            window_size=window_size,
            stride=1
        )
        # 应该创建2个窗口
        assert len(dataset) == 2

        # 测试步长大于1的情况
        window_size = 10
        stride = 5
        dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            window_size=window_size,
            stride=stride
        )
        # 计算预期窗口数量
        expected_windows = (len(sample_data) - window_size) // stride + 1
        assert len(dataset) == expected_windows

        # 测试窗口大小大于数据长度的情况
        with pytest.raises(ValueError):
            TimeSeriesWindowDataset(
                config=sample_config,
                data=sample_data,
                window_size=len(sample_data) + 1,
                stride=1
            )

    def test_different_data_types_processing(self, sample_config, numpy_data, tensor_data):
        """测试不同数据类型的处理"""
        # 1. 测试NumPy数组
        np_dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=numpy_data,
            window_size=10,
            stride=2
        )
        assert len(np_dataset) > 0
        sample = np_dataset[0]
        assert isinstance(sample, dict)
        assert 'features' in sample
        assert 'target' in sample
        assert isinstance(sample['features'], torch.Tensor)
        assert isinstance(sample['target'], torch.Tensor)

        # 2. 测试PyTorch张量
        tensor_dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=tensor_data,
            window_size=10,
            stride=2
        )
        assert len(tensor_dataset) > 0
        sample = tensor_dataset[0]
        assert isinstance(sample, dict)
        assert 'features' in sample
        assert 'target' in sample
        assert isinstance(sample['features'], torch.Tensor)
        assert isinstance(sample['target'], torch.Tensor)

        # 3. 验证两种数据类型处理结果的一致性
        assert np_dataset.feature_dim == tensor_dataset.feature_dim
        assert len(np_dataset) == len(tensor_dataset)

    def test_window_size_and_stride_parameters(self, sample_config, sample_data):
        """测试窗口大小和步长参数的影响"""
        # 测试不同窗口大小
        window_sizes = [5, 10, 20]
        for window_size in window_sizes:
            dataset = TimeSeriesWindowDataset(
                config=sample_config,
                data=sample_data,
                window_size=window_size,
                stride=1
            )
            # 验证窗口大小设置正确
            assert dataset.window_size == window_size
            # 验证窗口数量计算正确
            expected_windows = len(sample_data) - window_size + 1
            assert len(dataset) == expected_windows
            # 验证窗口形状正确
            sample = dataset[0]
            assert sample['features'].shape[0] == window_size

        # 测试不同步长
        strides = [1, 2, 5]
        window_size = 10
        for stride in strides:
            dataset = TimeSeriesWindowDataset(
                config=sample_config,
                data=sample_data,
                window_size=window_size,
                stride=stride
            )
            # 验证步长设置正确
            assert dataset.stride == stride
            # 验证窗口数量计算正确
            expected_windows = (len(sample_data) - window_size) // stride + 1
            assert len(dataset) == expected_windows

    def test_data_splitting_functionality(self, sample_config, sample_data):
        """测试数据分割功能"""
        # 设置分割比例
        sample_config['data']['train_ratio'] = 0.6
        sample_config['data']['val_ratio'] = 0.2
        sample_config['data']['test_ratio'] = 0.2

        # 直接创建数据集并传入数据，避免从配置加载
        train_dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            split='train',
            window_size=10,
            stride=2
        )

        # 创建验证集
        val_dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            split='val',
            window_size=10,
            stride=2
        )

        # 创建测试集
        test_dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            split='test',
            window_size=10,
            stride=2
        )

        # 验证三个数据集都有窗口
        assert len(train_dataset.windows) > 0
        assert len(val_dataset.windows) > 0
        assert len(test_dataset.windows) > 0

        # 验证分割属性设置正确
        assert train_dataset.split == 'train'
        assert val_dataset.split == 'val'
        assert test_dataset.split == 'test'

        # 验证不同分割的数据集窗口数量不同
        # 注意：由于我们传入了相同的数据，窗口数量实际上是相同的
        # 在实际使用中，不同分割会有不同的数据量
        assert len(train_dataset.windows) == len(val_dataset.windows)
        assert len(val_dataset.windows) == len(test_dataset.windows)

    def test_feature_dim_and_get_all_data(self, sample_config, sample_data):
        """测试特征维度属性和获取全部数据方法"""
        dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            window_size=10,
            stride=2
        )

        # 测试特征维度属性
        assert dataset.feature_dim == 3  # 根据sample_config中的numeric列数

        # 测试获取全部数据方法
        all_data = dataset.get_all_data()
        assert isinstance(all_data, dict)
        assert 'features' in all_data
        assert 'targets' in all_data
        assert isinstance(all_data['features'], torch.Tensor)
        assert isinstance(all_data['targets'], torch.Tensor)

        # 验证全部数据的形状
        num_windows = len(dataset)
        window_size = dataset.window_size
        feature_dim = dataset.feature_dim
        assert all_data['features'].shape[0] == num_windows * window_size
        assert all_data['features'].shape[1] == feature_dim
        assert all_data['targets'].shape[0] == num_windows * window_size
        assert all_data['targets'].shape[1] == 1  # 目标是单列

    def test_getitem_and_device_handling(self, sample_config, sample_data):
        """测试__getitem__方法和设备处理"""
        # 创建数据集
        dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            window_size=10,
            stride=2
        )

        # 测试获取单个样本
        sample = dataset[0]
        assert isinstance(sample, dict)
        assert 'features' in sample
        assert 'target' in sample

        # 测试索引越界处理
        with pytest.raises(IndexError):
            dataset[len(dataset) + 1]

        # 测试设备处理
        # 使用CPU设备进行测试，避免CUDA设备的差异
        device = torch.device('cpu')

        device_dataset = TimeSeriesWindowDataset(
            config=sample_config,
            data=sample_data,
            window_size=10,
            stride=2,
            device=device
        )

        # 验证样本在正确的设备上
        device_sample = device_dataset[0]
        assert device_sample['features'].device == device
        assert device_sample['target'].device == device
