"""
测试模块路径：tests/test_cuda_manager.py
测试目标：验证src/utils/cuda_manager.py的CUDA管理功能

测试要点：
1. 设备初始化和验证
2. 内存管理功能
3. 监控功能
4. 异常处理
5. 单例模式验证
"""

import unittest
from unittest.mock import patch

import pytest

from src.utils.cuda.manager import CUDAManager
from src.utils.cuda.types import CUDAInitError, GPUMemoryInfo


@pytest.mark.batch1  # 核心CUDA管理测试
class TestCUDAManager(unittest.TestCase):
    def setUp(self):
        """在每个测试前重置CUDA管理器"""
        CUDAManager._instance = None

    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.device_count', return_value=1)
    def test_initialization_with_gpu(self, mock_count, mock_available):
        """测试有GPU时的初始化"""
        manager = CUDAManager()

        # 验证设备类型
        self.assertTrue(manager.is_cuda_available)
        self.assertEqual(manager.device.type, 'cuda')

        # 验证单例模式
        manager2 = CUDAManager()
        self.assertIs(manager, manager2)

    @patch('torch.cuda.is_available', return_value=False)
    def test_initialization_without_gpu(self, mock_available):
        """测试无GPU时的初始化"""
        manager = CUDAManager()

        # 验证设备类型
        self.assertFalse(manager.is_cuda_available)
        self.assertEqual(manager.device.type, 'cpu')

    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.init', side_effect=RuntimeError("CUDA init failed"))
    def test_initialization_failure(self, mock_init, mock_available):
        """测试初始化失败情况"""
        with self.assertRaises(CUDAInitError):
            CUDAManager()

    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.memory_allocated', return_value=1024**3)  # 1GB
    @patch('torch.cuda.memory_reserved', return_value=2*1024**3)  # 2GB
    @patch('torch.cuda.get_device_properties')
    def test_memory_info(self, mock_props, mock_reserved, mock_allocated, mock_available):
        """测试内存信息获取"""
        # 模拟设备属性
        mock_props.return_value.total_memory = 8*1024**3  # 8GB

        manager = CUDAManager()
        mem_info = manager.get_memory_info()

        # 验证内存信息
        self.assertIsInstance(mem_info, GPUMemoryInfo)
        assert mem_info is not None # Add assertion to satisfy Pylance
        self.assertAlmostEqual(mem_info.used_gb, 1.0, places=1)
        self.assertAlmostEqual(mem_info.cached_gb, 2.0, places=1)
        self.assertAlmostEqual(mem_info.total_gb, 8.0, places=1)

    @patch('torch.cuda.is_available', return_value=True)
    def test_memory_management(self, mock_available):
        """测试内存管理功能"""
        manager = CUDAManager()

        # 测试缓存清理
        with patch('torch.cuda.empty_cache') as mock_empty:
            manager.clear_cache()
            mock_empty.assert_called_once()

        # 测试重置状态
        with patch('torch.cuda.reset_peak_memory_stats') as mock_reset:
            manager.reset()
            mock_reset.assert_called_once()

    @patch('torch.cuda.is_available', return_value=True)
    @patch('torch.cuda.utilization', return_value=0.5)
    def test_monitoring(self, mock_util, mock_available):
        """测试监控功能"""
        manager = CUDAManager()

        # 启动监控
        with patch('threading.Thread.start') as mock_start:
            manager.start_monitoring(interval=1)
            mock_start.assert_called_once()

        # 停止监控
        with patch('threading.Thread.join') as mock_join:
            manager.stop_monitoring()
            mock_join.assert_called_once()

    @patch('torch.cuda.is_available', return_value=True)
    def test_gpu_memory_info_str(self, mock_available):
        """测试GPU内存信息字符串表示"""
        mem_info = GPUMemoryInfo(
            used_gb=2.5,
            free_gb=5.5,
            cached_gb=1.0,
            total_gb=8.0,
            utilization=0.3125
        )

        info_str = str(mem_info)
        self.assertIn("2.50GB", info_str)
        self.assertIn("31.2%", info_str)
        self.assertIn("5.50GB", info_str)

if __name__ == '__main__':
    unittest.main()


@pytest.mark.batch2  # CUDA使用规范测试
class TestCUDAManagerUsagePatterns(unittest.TestCase):
    """测试CUDA管理模块的使用规范（新增）"""

    def setUp(self):
        """在每个测试前重置CUDA管理器"""
        CUDAManager._instance = None

    @patch('torch.cuda.is_available', return_value=True)
    def test_device_access_pattern(self, mock_available):
        """测试设备访问规范（规则第56条）"""
        manager = CUDAManager()

        # 正确访问方式
        device = manager.device
        self.assertEqual(device.type, 'cuda')

        # 错误访问方式应抛出异常
        with self.assertRaises(TypeError): # Expect TypeError for missing __getitem__
            _ = manager['device']  # type: ignore[misc] # 禁止字典式访问

    @patch('torch.cuda.is_available', return_value=True)
    def test_no_fallback_mechanism(self, mock_available):
        """测试禁止降级机制（规则第44条）"""
        manager = CUDAManager()

        # 模拟CUDA操作失败
        with patch('torch.cuda.empty_cache', side_effect=RuntimeError("CUDA error")), \
             self.assertRaises(RuntimeError):
            manager.clear_cache()  # 应直接抛出异常而非降级处理

    @patch('torch.cuda.is_available', return_value=True)
    def test_resource_management(self, mock_available):
        """测试资源管理规范（规则第14条）"""
        manager = CUDAManager()

        # 验证资源释放
        with patch('torch.cuda.empty_cache') as mock_empty:
            manager.clear_cache() # Use the correct method name
            mock_empty.assert_called_once()
