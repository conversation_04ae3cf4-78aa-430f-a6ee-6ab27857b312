"""项目主入口程序 - 时间序列预测系统

提供统一的命令行接口,支持训练、预测和评估模式。

用法:
    python main.py --mode train --config config.yaml
    python main.py --mode predict --config config.yaml --input data/test.csv
    python main.py --mode evaluate --config config.yaml --model outputs/models/generator.pt

项目结构模块索引:
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理
   - src/utils/logger.py: 日志系统
   - src/utils/cuda_manager.py: GPU管理
   - src/utils/resource_manager.py: 资源管理

2. 数据处理模块:
   - src/data/data_pipeline.py: 数据流水线
   - src/data/preprocessing/: 数据预处理

3. 模型模块:
   - src/models/gan/gan_model.py: GAN模型实现
   - src/models/base/base_model.py: 模型基类

4. 评估模块:
   - src/models/gan/gan_evaluator.py: 评估指标
"""

# 设置PyTorch CUDA内存分配器配置,减少内存碎片化
import os

os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'

import argparse
import logging
import time
from collections.abc import Callable
from pathlib import Path
from typing import Any

# 第三方库导入
import numpy as np
import torch

# 导入 DataLoader 和 TensorDataset
from torch.utils.data import DataLoader, TensorDataset

from src.data.data_pipeline import DataPipeline
from src.models.gan.gan_evaluator import GANEvaluator
from src.models.gan.gan_model import GANModel
from src.models.gan.trainer import GANTrainer
from src.predict import Predictor
from src.utils.config_manager import ConfigManager
from src.utils.cuda.manager import CUDAManager

# 项目内部导入
from src.utils.error_handling import handle_errors, setup_global_exception_handlers
from src.utils.logger import LoggerFactory
from src.utils.resource_manager import ResourceManager


class PipelineRunner:
    """流水线运行器 - 负责协调完整的运行流程"""

    def __init__(self, config_path: str):
        """初始化流水线运行器

        Args:
            config_path: 配置文件路径
        """
        # 初始化日志系统
        from src.utils.logger import LoggerFactory
        self.logger = LoggerFactory().get_logger("PipelineRunner")
        self.logger.info(f"初始化流水线运行器 - 配置文件: {config_path}")

        # 加载配置
        self.config = ConfigManager.from_yaml(config_path)
        self.logger.info(f"配置加载完成 - 版本: {self.config.version}")

        # 初始化资源管理器
        self.resource_manager = ResourceManager(self.config)
        self.logger.info("资源管理器初始化完成")

        # 初始化CUDA管理器
        self.cuda_manager = CUDAManager()

        # 从配置文件中读取CUDA配置并配置CUDA管理器
        if hasattr(self.config, 'system') and hasattr(self.config.system, 'cuda'):
            self.logger.info("从配置文件中读取CUDA配置")
            cuda_config = self.config.system.cuda
            # 将对象转换为字典
            if not isinstance(cuda_config, dict):
                cuda_config = cuda_config.__dict__
            self.cuda_manager.configure(cuda_config)
            self.logger.info("CUDA管理器配置完成")
        else:
            self.logger.error("配置文件中缺少system.cuda配置,CUDA管理器将使用默认配置")
            raise ValueError("配置文件中缺少system.cuda配置")

        self.device = self.cuda_manager.device
        self.logger.info(f"CUDA管理器初始化完成 - 设备: {self.device}")

        # 创建必要的目录
        self.config.paths.setup_directories()
        self.logger.info("目录结构初始化完成")

        # 检查混合精度训练设置
        # 严格检查混合精度配置
        if not hasattr(self.config, 'training'):
            error_msg = "配置中缺少training部分"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if not hasattr(self.config.training, 'mixed_precision'):
            error_msg = "配置中缺少training.mixed_precision部分"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        mixed_precision = self.config.training.mixed_precision
        if mixed_precision is None:
            error_msg = "training.mixed_precision配置不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 从字典或对象中获取enabled属性
        if isinstance(mixed_precision, dict):
            if 'enabled' not in mixed_precision:
                error_msg = "training.mixed_precision.enabled配置缺失"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            is_amp_enabled = mixed_precision['enabled']
        else:
            if not hasattr(mixed_precision, 'enabled'):
                error_msg = "training.mixed_precision.enabled配置缺失"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            is_amp_enabled = mixed_precision.enabled

        if not isinstance(is_amp_enabled, bool):
            error_msg = f"training.mixed_precision.enabled必须是布尔值,实际类型: {type(is_amp_enabled)}"
            self.logger.error(error_msg)
            raise TypeError(error_msg)

        if is_amp_enabled:
            self.logger.info("混合精度训练已启用")
        else:
            self.logger.info("混合精度训练已禁用")

    def prepare_data_and_get_dimensions(self) -> dict[str, Any]:
        """执行数据准备并返回计算出的维度和处理后的数据"""
        self.logger.info("开始数据准备和维度计算")
        try:
            data_pipeline = DataPipeline(
                raw_data_path=str(self.config.paths.raw_data),
                config=self.config
            )
            model_input = data_pipeline.run_pipeline(
                window_size=self.config.data.window_size
            )
            feature_dim = model_input['features'].shape[-1]
            self.logger.info(f"数据准备完成 - 计算出的特征维度: {feature_dim}")
            return {
                "model_input": model_input,
                "feature_dim": feature_dim,
                "data_pipeline": data_pipeline # 返回 pipeline 以便获取 standardizer
            }
        except Exception as e:
            self.logger.error(f"数据准备和维度计算失败: {e!s}")
            raise

    def train(self,
              resume: bool = False,
              checkpoint_path: str | None = None,
              precomputed_data: dict[str, Any] | None = None,
              precomputed_feature_dim: int | None = None,
              precomputed_pipeline: DataPipeline | None = None,
              pruning_callback: Callable | None = None # 添加剪枝回调参数
             ) -> dict[str, Any]:
        """执行训练流程

        Args:
            resume: 是否从检查点恢复训练
            checkpoint_path: 检查点路径（如果resume=True）
            precomputed_data: 预先计算的数据 (包含 'features', 'targets')
            precomputed_feature_dim: 预先计算的特征维度
            precomputed_pipeline: 预先创建的数据管道实例

        Returns:
            Dict[str, Any]: 训练结果
        """
        self.logger.info("开始训练流程")

        # 启用梯度异常检测以定位 NaN/Inf 来源
        torch.autograd.set_detect_anomaly(True)
        self.logger.info("已启用PyTorch梯度异常检测")

        try:
            # 1. 数据准备 (如果未提供预计算数据)
            if precomputed_data is None or precomputed_feature_dim is None or precomputed_pipeline is None:
                self.logger.info("未提供预计算数据,执行数据准备步骤")
                prep_result = self.prepare_data_and_get_dimensions()
                model_input = prep_result["model_input"]
                feature_dim = prep_result["feature_dim"]
                data_pipeline = prep_result["data_pipeline"] # 获取 pipeline 实例
            else:
                self.logger.info("使用预计算的数据和维度")
                model_input = precomputed_data
                feature_dim = precomputed_feature_dim
                data_pipeline = precomputed_pipeline # 使用预计算的 pipeline

            # 2. 模型初始化
            self.logger.info("初始化模型")
            # feature_dim 已从 prepare_data_and_get_dimensions 或参数获取
            window_size = self.config.data.window_size
            model = GANModel(self.config, feature_dim=feature_dim, window_size=window_size)
            model.log_module_info()

            # 3. 从检查点恢复(如果需要)
            if resume and checkpoint_path:
                self.logger.info(f"从检查点恢复训练: {checkpoint_path}")
                self._load_model(model, checkpoint_path)

            # 4. 准备数据集
            self.logger.info("准备训练和验证数据集")
            features = model_input['features'] # 使用获取或传入的 model_input
            targets = model_input['targets']


            total_windows = features.shape[0]

            # 检查配置项是否存在且不为None
            if not hasattr(self.config.data, 'train_ratio') or self.config.data.train_ratio is None:
                raise ValueError("配置缺少train_ratio参数")
            if not hasattr(self.config.data, 'val_ratio') or self.config.data.val_ratio is None:
                raise ValueError("配置缺少val_ratio参数")

            train_ratio = float(self.config.data.train_ratio)
            val_ratio = float(self.config.data.val_ratio)

            # 验证比例值
            if not (0 < train_ratio < 1) or not (0 < val_ratio < 1):
                raise ValueError(f"无效的训练/验证比例: train_ratio={train_ratio}, val_ratio={val_ratio}")

            train_end_idx = int(total_windows * train_ratio)
            val_end_idx = train_end_idx + int(total_windows * val_ratio)
            # 确保索引不越界,并将剩余部分划给最后一个集合(这里是验证集)
            val_end_idx = min(val_end_idx, total_windows)
            train_size = train_end_idx # 重命名变量以便后续代码兼容

            # 自定义数据集,提供特征和目标的对应关系,以符合 GANTrainer 的输入要求
            class CustomDataset(torch.utils.data.Dataset):
                def __init__(self, features, targets):
                    self.features = features
                    self.targets = targets

                def __len__(self):
                    return len(self.features)

                def __getitem__(self, index):
                    return {
                        'features': self.features[index],
                        'target': self.targets[index]
                    }

            # 训练集
            train_features = features[:train_size]
            train_targets = targets[:train_size]
            train_dataset = CustomDataset(train_features, train_targets)

            # 验证集
            val_features = features[train_end_idx:val_end_idx]
            val_targets = targets[train_end_idx:val_end_idx]
            val_dataset = CustomDataset(val_features, val_targets)

            self.logger.info(f"数据集创建完成 - 训练集: {len(train_dataset)}样本, 验证集: {len(val_dataset)}样本")


            # 5. 初始化训练器
            self.logger.info("初始化训练器")
            # 获取标准化器用于预测值的逆变换
            target_standardizer = data_pipeline.get_target_standardizer # 使用获取或传入的 data_pipeline
            if target_standardizer is None:
                error_msg = "无法获取 Target Standardizer,无法进行训练"
                self.logger.error(error_msg)
                raise ValueError(error_msg)


            trainer = GANTrainer(
                config=self.config,
                model=model,
                target_standardizer=target_standardizer # 显式传递 target_standardizer
            )

            # 6. 执行训练
            self.logger.info("开始模型训练")

            # 使用专业配置读取模块获取动态批次大小配置
            from src.utils.config.batch_size_optimizer import (
                get_batch_size_optimizer_config,
                get_dynamic_batch_size_enabled,
            )

            # 从配置文件读取动态批次大小启用状态
            dynamic_batch_size = get_dynamic_batch_size_enabled(self.config)

            # 读取批次大小优化器配置
            batch_size_optimizer_config = get_batch_size_optimizer_config(self.config)

            # 启动GPU内存峰值监控
            from src.utils.cuda import cuda_manager
            if cuda_manager is None:
                error_msg = "CUDA管理器未初始化,无法启动GPU内存峰值监控"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg)

            # 启动峰值内存监控,每5秒采样一次,窗口大小为30秒
            cuda_manager.start_peak_memory_monitoring(interval=5, window_size=30)
            self.logger.info("已启动GPU内存峰值监控,采样间隔: 5秒,窗口大小: 30秒")

            self.logger.info(f"动态批次大小调整: {'启用' if dynamic_batch_size else '禁用'}")
            self.logger.info(f"初始批次大小: {self.config.training.batch_size}")

            # 输出批次大小优化器配置详情
            self.logger.info(f"批次大小优化器配置: min={batch_size_optimizer_config['min_batch_size']}, "
                             f"max={batch_size_optimizer_config['max_batch_size']}, "
                             f"strategy={batch_size_optimizer_config['strategy']}, "
                             f"memory_utilization_target={batch_size_optimizer_config['memory_utilization_target'] * 100:.0f}%")

            # 检查训练配置项
            if not hasattr(self.config.training, 'batch_size') or self.config.training.batch_size is None:
                raise ValueError("配置缺少training.batch_size参数")
            if not hasattr(self.config.training, 'num_workers') or self.config.training.num_workers is None:
                raise ValueError("配置缺少training.num_workers参数")

            batch_size = int(self.config.training.batch_size)
            num_workers = int(self.config.training.num_workers)

            # 调用重命名后的方法 run_training_loop
            history = trainer.run_training_loop(
                batch_size=batch_size,
                train_dataset=train_dataset,
                val_dataset=val_dataset,
                num_workers=num_workers,
                dynamic_batch_size=dynamic_batch_size,
                pruning_callback=pruning_callback # 传递剪枝回调
            )
            self.logger.info("模型训练完成")

            # 停止GPU内存峰值监控
            from src.utils.cuda import cuda_manager
            if cuda_manager is None:
                error_msg = "CUDA管理器未初始化,无法停止GPU内存峰值监控"
                self.logger.error(error_msg)
                raise RuntimeError(error_msg)

            cuda_manager.stop_peak_memory_monitoring()
            self.logger.info("GPU内存峰值监控已停止")

            # 7. 返回训练结果
            return {
                "history": history,
                "model": model,
                "feature_dim": feature_dim, # 返回正确的 feature_dim
                "window_size": window_size
            }

        except Exception as e:
            self.logger.error(f"训练流程失败: {e!s}")
            raise

    def evaluate(self, model_path: str) -> dict[str, float]:
        """执行评估流程

        Args:
            model_path: 模型路径

        Returns:
            Dict[str, float]: 评估指标
        """
        self.logger.info("开始评估流程")

        if model_path is None:
            raise ValueError("必须提供model_path参数,不能为None")

        try:
            # 1. 数据准备
            self.logger.info("开始数据准备")
            data_pipeline = DataPipeline(
                raw_data_path=str(self.config.paths.raw_data),
                config=self.config
            )
            model_input = data_pipeline.run_pipeline(
                window_size=self.config.data.window_size
            )
            self.logger.info("数据准备完成")

            # 2. 模型加载
            self.logger.info("加载模型")
            feature_dim = model_input['features'].shape[-1]
            window_size = self.config.data.window_size
            model = GANModel(self.config, feature_dim=feature_dim, window_size=window_size)

            # 使用提供的模型路径
            model_path_str = str(model_path)

            self._load_model(model, model_path_str)
            model.eval()

            # 3. 准备测试数据集
            self.logger.info("准备测试数据集")
            features = model_input['features']
            targets = model_input['targets']

            # 使用配置文件定义的比例来确定评估数据集(通常是测试集,但这里代码逻辑是用验证集)
            total_windows = features.shape[0]

            # 检查配置项是否存在且不为None
            if not hasattr(self.config.data, 'train_ratio') or self.config.data.train_ratio is None:
                raise ValueError("配置缺少data.train_ratio参数")
            if not hasattr(self.config.data, 'val_ratio') or self.config.data.val_ratio is None:
                raise ValueError("配置缺少data.val_ratio参数")

            train_ratio = float(self.config.data.train_ratio)
            val_ratio = float(self.config.data.val_ratio)

            # 验证比例值
            if not (0 < train_ratio < 1) or not (0 < val_ratio < 1):
                raise ValueError(f"无效的训练/验证比例: train_ratio={train_ratio}, val_ratio={val_ratio}")

            train_end_idx = int(total_windows * train_ratio)
            val_end_idx = train_end_idx + int(total_windows * val_ratio)
            # 确保索引不越界
            val_end_idx = min(val_end_idx, total_windows)

            # 根据当前代码逻辑,评估使用的是验证集部分
            test_features = features[train_end_idx:val_end_idx]
            test_targets = targets[train_end_idx:val_end_idx]
            self.logger.info(f"评估将使用索引 {train_end_idx} 到 {val_end_idx} 的数据 (验证集部分)")

            # 创建评估数据集和 DataLoader
            eval_batch_size = self.config.evaluation.batch_size # 从配置读取评估批大小
            eval_dataset = TensorDataset(test_features, test_targets)
            eval_loader = DataLoader(eval_dataset, batch_size=eval_batch_size, shuffle=False)
            self.logger.info(f"评估 DataLoader 创建完成 - 批大小: {eval_batch_size}, 总批次数: {len(eval_loader)}")


            # 4. 初始化评估器
            self.logger.info("初始化评估器")
            # 获取标准化器用于预测值的逆变换
            target_standardizer = data_pipeline.get_target_standardizer
            if target_standardizer is None:
                error_msg = "无法获取 Target Standardizer,无法进行评估"
                self.logger.error(error_msg)
                raise ValueError(error_msg)


            evaluator = GANEvaluator(config=self.config, standardizer=target_standardizer)


            # 5. 执行批量评估
            self.logger.info("开始模型批量评估")
            all_metrics = []
            # 确保模型在评估模式
            model.eval()
            with torch.no_grad():
                for batch_idx, (batch_features, batch_targets) in enumerate(eval_loader):
                    self.logger.debug(f"评估批次 {batch_idx+1}/{len(eval_loader)}")
                    # 准备当前批次数据
                    current_batch = {
                        'features': batch_features.to(self.device),
                        'target': batch_targets.to(self.device)
                    }
                    # 对当前批次进行评估
                    batch_metrics = evaluator.evaluate(model=model, batch=current_batch)
                    all_metrics.append(batch_metrics)

            # 计算所有批次的平均指标
            if not all_metrics:
                 error_msg = "没有收集到任何评估指标，评估失败"
                 self.logger.error(error_msg)
                 raise ValueError(error_msg)

            # 使用 defaultdict 简化聚合
            from collections import defaultdict
            aggregated_metrics = defaultdict(list)
            for m in all_metrics:
                for k, v in m.items():
                    # 确保值是数值类型
                    if isinstance(v, int | float):
                         aggregated_metrics[k].append(v)
                    elif isinstance(v, torch.Tensor):
                         aggregated_metrics[k].append(v.item())

            # 计算平均值并显式转换为 float
            metrics = {
                k: float(np.mean(v)) for k, v in aggregated_metrics.items()
            }
            # numpy 已在文件顶部导入

            self.logger.info(f"批量评估完成 - 平均指标: {metrics}")


            # 6. 保存评估结果
            results_path = self.config.paths.results_dir / "evaluation_results.txt"
            with open(results_path, "w") as f:
                for k, v in metrics.items():
                    f.write(f"{k}: {v:.4f}\n")
            self.logger.info(f"评估结果已保存到: {results_path}")

            # 7. 返回评估指标
            return metrics

        except Exception as e:
            self.logger.error(f"评估流程失败: {e!s}")
            raise

    def process_data(self, save_processed: bool, output_dir: str) -> dict[str, Any]:
        """执行数据处理流程

        Args:
            save_processed: 是否保存处理后的数据
            output_dir: 输出目录路径

        Returns:
            Dict[str, Any]: 处理结果
        """
        self.logger.info("开始数据处理流程")

        if output_dir is None:
            raise ValueError("必须提供output_dir参数，不能为None")

        try:
            # 1. 设置输出目录
            output_path = Path(output_dir)

            if save_processed:
                output_path.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"输出目录: {output_path}")

            # 2. 数据准备
            self.logger.info(f"开始数据准备 - 输入路径: {self.config.paths.raw_data}")
            data_pipeline = DataPipeline(
                raw_data_path=str(self.config.paths.raw_data),
                config=self.config
            )

            # 加载原始数据并记录其形状，用于后续数据处理前后的对比分析
            data_pipeline.load_data()

            # 获取原始数据的特征和目标维度
            original_shape = {
                'features': data_pipeline._features.shape if data_pipeline._features is not None else None,
                'targets': data_pipeline._targets.shape if data_pipeline._targets is not None else None
            }
            self.logger.info(f"原始数据形状: 特征={original_shape['features']}, 目标={original_shape['targets']}")

            # 执行完整数据处理流程
            model_input = data_pipeline.run_pipeline(
                window_size=self.config.data.window_size
            )
            self.logger.info("数据处理完成")

            # 3. 保存处理后的数据（如果需要）
            if save_processed:
                import pickle

                import numpy as np

                # 保存处理后的数据
                np_file = output_path / "processed_data.npz"
                np.savez(
                    np_file,
                    features=model_input['features'].numpy(),
                    targets=model_input['targets'].numpy()
                )

                # 准备元数据并将完整的处理结果（数据+元数据）保存为pickle格式
                pickle_file = output_path / "processed_data.pkl"

                # 收集处理过程的元信息
                metadata = {
                    'window_size': self.config.data.window_size,
                    'original_shape': original_shape,
                    'processing_config': self.config.data.to_dict() if hasattr(self.config.data, 'to_dict') else vars(self.config.data),
                    'timestamp': time.time(),
                    'feature_dim': model_input['features'].shape[-1],
                    'num_windows': model_input['features'].shape[0],
                    'processing_steps': [
                        "1. 数据加载",
                        "2. 数据验证",
                        "3. 数据清洗",
                        "4. 特征编码",
                        "5. 特征工程",
                        "6. 特征选择",
                        "7. 滑动窗口创建",
                        "8. 模型输入准备"
                    ]
                }

                # 保存pickle文件
                with open(pickle_file, 'wb') as f:
                    pickle.dump({
                        'features': model_input['features'],
                        'targets': model_input['targets'],
                        'metadata': metadata
                    }, f)

                # 创建详细的README文档，包含数据处理结果说明、使用方法和注意事项
                readme_file = output_path / "README.txt"
                with open(readme_file, 'w', encoding='utf-8') as f:
                    from datetime import datetime
                    current_time = datetime.fromtimestamp(time.time()).strftime('%Y-%m-%d %H:%M:%S')

                    # 获取数据源信息
                    data_source = str(self.config.paths.raw_data)

                    # 获取特征选择信息
                    # 直接计算特征选择信息，不使用try-except回退
                    features_shape = original_shape.get('features')
                    # 验证数据结构
                    if not isinstance(features_shape, tuple | list | type(None)):
                        error_msg = f"特征形状格式不正确，预期tuple/list，实际: {type(features_shape)}"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)

                    original_features = features_shape[1] if features_shape and len(features_shape) > 1 else 0
                    selected_features = model_input['features'].shape[-1]
                    reduction_ratio = round((1 - selected_features / original_features) * 100, 2) if original_features > 0 else 0

                    # 构造特征选择信息
                    feature_selection_info = {
                        'original_features': original_features,
                        'selected_features': selected_features,
                        'reduction_ratio': reduction_ratio
                    }

                    self.logger.debug(
                        f"特征选择信息计算完成:\n"
                        f"- 原始特征数: {original_features}\n"
                        f"- 选择后特征数: {selected_features}\n"
                        f"- 降维比例: {reduction_ratio}%"
                    )

                    # 获取窗口信息
                    window_info = {
                        'window_size': self.config.data.window_size,
                        'stride': self.config.data.stride, # Strict access
                        'num_windows': model_input['features'].shape[0]
                    }

                    # 写入摘要信息
                    summary = f"""数据处理结果说明
===================

摘要：
本文件描述了时间序列预测系统的数据处理结果。原始数据从{data_source}加载，经过清洗、特征工程和特征选择后，形成了{window_info['num_windows']}个时间窗口，每个窗口包含{window_info['window_size']}个时间步长和{feature_selection_info['selected_features']}个特征。这些处理后的数据可用于训练时间序列预测模型。

文件说明：
-----------
1. processed_data.npz: NumPy压缩格式的处理后数据，包含特征和目标数组
2. processed_data.pkl: Python Pickle格式的完整数据，包含特征、目标和元数据
3. README.txt: 本文件，描述数据处理结果

数据源信息：
-----------
- 原始数据文件：{data_source}
- 数据处理时间：{current_time}
- 配置文件：{self.config.config_path} # Strict access
- 数据版本：{self.config.version} # Strict access

数据处理流程：
-----------
"""

                    # 直接格式化字符串，不使用try-except回退
                    # 由于summary中没有实际的格式化占位符，这里不需要调用format方法

                    # 写入摘要
                    f.write(summary)

                    # 添加处理步骤描述
                    for step in metadata['processing_steps']:
                        f.write(f"{step}\n")

                    # 添加数据形状信息
                    f.write("\n\n数据形状信息：\n-----------\n")
                    f.write("原始数据形状: \n")
                    f.write(f"- 特征: {original_shape['features']}\n")
                    f.write(f"- 目标: {original_shape['targets']}\n")
                    features_shape = original_shape.get('features')
                    if features_shape is None or not isinstance(features_shape, tuple | list) or len(features_shape) < 2:
                        total_samples = 0
                        num_features = 0
                    else:
                        total_samples = features_shape[0]
                        num_features = features_shape[1]
                    f.write(f"- 总样本数: {total_samples}\n")
                    f.write(f"- 原始特征数: {num_features}\n\n")

                    f.write("处理后数据形状: \n")
                    f.write(f"- 特征: {model_input['features'].shape}\n")
                    f.write(f"- 目标: {model_input['targets'].shape}\n")
                    f.write(f"- 窗口数量: {model_input['features'].shape[0]}\n")
                    f.write(f"- 窗口大小: {window_info['window_size']}\n")
                    f.write(f"- 滑动步长: {window_info['stride']}\n")
                    f.write(f"- 特征维度: {model_input['features'].shape[-1]}\n")

                    # 添加特征工程和选择信息
                    f.write("\n\n特征工程和选择信息：\n-----------\n")
                    f.write(f"- 原始特征数: {feature_selection_info['original_features']}\n")
                    f.write(f"- 特征选择后特征数: {feature_selection_info['selected_features']}\n")
                    f.write(f"- 特征减少比例: {feature_selection_info['reduction_ratio']}%\n")
                    # 安全地访问 correlation_threshold
                    correlation_threshold = "未配置" # 默认值
                    feature_selection_config = getattr(self.config.data, 'feature_selection', None)
                    if feature_selection_config is not None:
                        if isinstance(feature_selection_config, dict):
                             correlation_threshold = feature_selection_config.get('correlation_threshold', "未找到")
                        elif hasattr(feature_selection_config, 'correlation_threshold'):
                             # 假设 feature_selection 是一个对象
                             correlation_threshold = getattr(feature_selection_config, 'correlation_threshold', "未找到")
                        # 如果 feature_selection 既不是字典也不是具有该属性的对象，则保持默认值

                    f.write(f"- 相关性阈值: {correlation_threshold}\n")

                    # 添加数据统计信息
                    f.write("\n\n数据统计信息：\n-----------\n")
                    if not hasattr(self.config.data, 'train_ratio') or self.config.data.train_ratio is None:
                        raise ValueError("配置缺少data.train_ratio参数")
                    train_ratio = float(self.config.data.train_ratio)
                    if not (0 < train_ratio < 1):
                        raise ValueError(f"无效的训练集比例: {train_ratio}")
                    f.write(f"- 训练集比例: {train_ratio * 100:.1f}%\n")
                    f.write(f"- 验证集比例: {(1 - train_ratio) * 100:.1f}%\n")
                    f.write(f"- 批大小: {self.config.training.batch_size}\n") # Strict access

                    # 直接使用简化版本的使用说明，避免格式化问题
                    # 使用 f-string 动态插入 batch_size
                    usage_info = f"""\n\n数据使用说明：\n-----------\n加载NumPy数据 (.npz):
```python
import numpy as np

# 加载数据
data = np.load('processed_data.npz')
features = data['features']  # 特征数据
targets = data['targets']    # 目标数据

# 数据形状
print(f"特征形状: {{features.shape}}")  # (窗口数, 窗口大小, 特征维度)
print(f"目标形状: {{targets.shape}}")  # (窗口数, 窗口大小, 1)
```

加载Pickle数据 (.pkl):
```python
import pickle
from datetime import datetime

# 加载数据
with open('processed_data.pkl', 'rb') as f:
    data = pickle.load(f)

features = data['features']    # 特征数据
targets = data['targets']      # 目标数据
metadata = data['metadata']    # 元数据

# 查看元数据
print(f"窗口大小: {{metadata['window_size']}}")
print(f"原始数据形状: {{metadata['original_shape']}}")
print(f"处理时间: {{datetime.fromtimestamp(metadata['timestamp']).strftime('%Y-%m-%d %H:%M:%S')}}")
```

创建数据集用于模型训练:
```python
import torch
from torch.utils.data import TensorDataset, DataLoader

# 假设我们已经加载了数据
# features 形状: (46, 16, 19) # 示例形状，实际可能不同
# targets 形状: (46, 16, 1) # 示例形状，实际可能不同

# 创建 PyTorch 张量
features_tensor = torch.tensor(features, dtype=torch.float32)
targets_tensor = torch.tensor(targets, dtype=torch.float32)

# 创建数据集
dataset = TensorDataset(features_tensor, targets_tensor)

# 创建数据加载器 (使用配置中的 batch_size)
batch_size = {self.config.training.batch_size} # 从配置动态获取
dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

# 现在可以使用 dataloader 进行模型训练
```
"""

                    # 使用说明已经在上面直接定义，不需要格式化

                    # 添加使用说明
                    f.write(usage_info)

                    # 添加注意事项
                    f.write("\n\n注意事项：\n-----------\n")
                    f.write("""本数据已经过标准化处理，直接用于模型训练。如果需要将数据还原为原始值，请使用元数据中的缩放参数。

数据处理过程中已经完成了以下操作：
1. 缺失值处理
2. 异常值检测与处理
3. 特征编码和标准化
4. 特征工程与选择
5. 时间窗口切分

如果需要对新数据进行预测，请确保新数据经过相同的预处理步骤。
""")

                    # 添加时间戳
                    f.write(f"\n\n生成时间: {current_time}")

                self.logger.info(f"处理后的数据已保存到: {output_path}")
                self.logger.info(f"- NumPy文件: {np_file}")
                self.logger.info(f"- Pickle文件: {pickle_file}")
                self.logger.info(f"- 说明文件: {readme_file}")

            # 4. 返回处理结果
            return {
                "original_shape": original_shape,
                "features_shape": model_input['features'].shape,
                "targets_shape": model_input['targets'].shape,
                "num_windows": model_input['features'].shape[0],
                "output_path": output_path if save_processed else None
            }

        except Exception as e:
            self.logger.error(f"数据处理流程失败: {e!s}")
            raise

    def predict(self, input_path: str, model_path: str, output_path: str) -> dict[str, Any]:
        """执行预测流程

        Args:
            input_path: 输入数据路径
            model_path: 模型路径
            output_path: 输出结果路径

        Returns:
            Dict[str, Any]: 预测结果
        """
        self.logger.info("开始预测流程")

        if input_path is None:
            raise ValueError("必须提供input_path参数，不能为None")
        if model_path is None:
            raise ValueError("必须提供model_path参数，不能为None")
        if output_path is None:
            raise ValueError("必须提供output_path参数，不能为None")

        try:
            # 1. 设置路径
            self.config.paths.raw_data = Path(input_path)
            output_dir = Path(output_path)
            output_dir.mkdir(parents=True, exist_ok=True)

            # 2. 数据准备
            self.logger.info(f"开始数据准备 - 输入路径: {self.config.paths.raw_data}")
            data_pipeline = DataPipeline(
                raw_data_path=str(self.config.paths.raw_data),
                config=self.config
            )
            model_input = data_pipeline.run_pipeline(
                window_size=self.config.data.window_size
            )
            self.logger.info("数据准备完成")

            # 3. 初始化预测器
            self.logger.info("初始化预测器")
            predictor = Predictor(self.config, self.cuda_manager)

            # 4. 加载模型
            model_path_str = str(model_path)

            self._load_model(predictor.model, model_path_str)
            predictor.model.eval()

            # 5. 执行预测
            self.logger.info("开始预测")
            features = model_input['features'].numpy()
            predictions = predictor.predict(features)
            self.logger.info(f"预测完成 - 形状: {predictions.shape}")

            # 6. 保存预测结果
            output_file = output_dir / "predictions.npy"
            import numpy as np
            np.save(output_file, predictions)
            self.logger.info(f"预测结果已保存到: {output_file}")

            # 7. 返回预测结果
            return {
                "predictions": predictions,
                "output_path": output_file
            }

        except Exception as e:
            self.logger.error(f"预测流程失败: {e!s}")
            raise

    def _load_model(self, model: GANModel, load_path: str | Path) -> None:
        """加载模型状态字典，支持不同特征维度的模型加载

        Args:
            model: 模型实例
            load_path: 加载路径
        """
        load_path = Path(load_path)
        if not load_path.exists():
            raise FileNotFoundError(f"模型文件不存在: {load_path}")

        # 加载模型状态
        checkpoint = torch.load(load_path, map_location=self.device)

        # 检查特征维度是否匹配
        if 'metadata' in checkpoint and 'feature_dim' in checkpoint['metadata']:
            saved_feature_dim = checkpoint['metadata']['feature_dim']
            current_feature_dim = model.feature_dim

            if saved_feature_dim != current_feature_dim:
                error_msg = f"特征维度不匹配: 保存模型使用 {saved_feature_dim} 维特征，当前数据有 {current_feature_dim} 维特征"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

        # 加载生成器和判别器状态
        try:
            # 识别并获取正确的状态字典格式
            state_dict = checkpoint['model_state_dict'] if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint else checkpoint

            # 直接加载模型状态，不进行兼容性处理
            model.load_state_dict(state_dict)

            # 加载判别器状态
            if 'discriminator_state' in state_dict:
                discriminator_state = state_dict['discriminator_state']
                model.discriminator.load_state_dict(discriminator_state)

            self.logger.info(f"模型已从 {load_path} 加载")
        except Exception as e:
            self.logger.error(f"加载模型失败: {e!s}")
            raise

    def cleanup(self):
        """清理资源"""
        self.logger.info("开始清理资源")
        # 清理CUDA资源
        self.cuda_manager.clear_cache()
        self.logger.info("CUDA资源已清理")

        # 清理其他资源
        self.logger.info("资源管理器已清理")

        self.logger.info("资源清理完成")


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="时间序列预测系统")

    # 运行模式 (必需)
    parser.add_argument("--mode", type=str, required=True, choices=["train", "predict", "evaluate", "process"],
                        help="运行模式: train, predict, evaluate, process (必需)")

    # 数据处理相关参数
    parser.add_argument("--save_processed", action="store_true",
                        help="是否保存处理后的数据（用于数据处理模式）")
    parser.add_argument("--output_dir", type=str, required=True,
                        help="处理后数据的保存目录（用于数据处理模式）")

    # 配置文件 (必需)
    parser.add_argument("--config", type=str, required=True,
                        help="配置文件路径 (必需)")

    # 训练相关参数
    parser.add_argument("--resume", action="store_true",
                        help="是否从检查点恢复训练")
    parser.add_argument("--checkpoint", type=str,
                        help="检查点路径（用于恢复训练）")

    # 预测相关参数
    parser.add_argument("--input", type=str, required=True,
                        help="输入数据路径（用于预测模式）")
    parser.add_argument("--output", type=str, required=True,
                        help="输出结果路径（用于预测模式）")

    # 评估相关参数
    parser.add_argument("--model", type=str, required=True,
                        help="模型路径（用于预测和评估模式）")

    return parser.parse_args()


@handle_errors(error_message="主程序执行失败", log_level=logging.CRITICAL, reraise=True)
def main():
    """主函数"""
    # 设置全局异常处理器
    setup_global_exception_handlers()

    # 解析命令行参数
    args = parse_args()

    # 初始化流水线运行器
    runner = PipelineRunner(args.config)

    try:
        # 根据运行模式执行相应的流程
        if args.mode == "train":
            # 训练模式
            runner.train(resume=args.resume, checkpoint_path=args.checkpoint)
            print(f"训练完成，模型已保存到 {runner.config.paths.model_dir}")

        elif args.mode == "predict":
            # 预测模式
            # 检查必需参数
            if args.input is None:
                raise ValueError("预测模式必须提供--input参数")
            if args.model is None:
                raise ValueError("预测模式必须提供--model参数")
            if args.output is None:
                raise ValueError("预测模式必须提供--output参数")

            result = runner.predict(input_path=args.input, model_path=args.model, output_path=args.output)
            print(f"预测完成，结果已保存到 {result['output_path']}")

        elif args.mode == "evaluate":
            # 评估模式
            # 检查必需参数
            if args.model is None:
                raise ValueError("评估模式必须提供--model参数")

            metrics = runner.evaluate(model_path=args.model)
            print("\n评估结果:")
            for k, v in metrics.items():
                print(f"  {k}: {v:.4f}")

        elif args.mode == "process":
            # 数据处理模式
            # 检查必需参数
            if args.output_dir is None:
                raise ValueError("数据处理模式必须提供--output_dir参数")

            result = runner.process_data(save_processed=args.save_processed, output_dir=args.output_dir)
            print("\n数据处理完成:")
            print(f"- 原始数据形状: {result['original_shape']}")
            print(f"- 处理后特征形状: {result['features_shape']}")
            print(f"- 处理后目标形状: {result['targets_shape']}")
            print(f"- 窗口数量: {result['num_windows']}")
            if args.save_processed:
                print(f"- 处理后数据已保存到: {result['output_path']}")

        else:
            raise ValueError(f"不支持的运行模式: {args.mode}")

        return 0

    except Exception as e:
        # 使用LoggerFactory获取日志记录器
        logger = LoggerFactory().get_logger("main")
        logger.error(f"运行失败: {e!s}")
        print(f"错误: {e!s}")
        return 1

    finally:
        # 清理资源
        runner.cleanup()


if __name__ == "__main__":
    main()
