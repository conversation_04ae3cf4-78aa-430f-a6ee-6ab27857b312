"""特征选择系统 - 多阶段、聚焦领先信号的特征筛选

项目结构模块索引：
1. 基础设施模块:
   - src/utils/config_manager.py: 配置管理 (读取滞后相关性参数等)
   - src/utils/logger.py: 日志系统 (记录筛选过程)
   - src/utils/cuda_manager.py: (可选) GPU加速计算
   - src/utils/resource_manager.py: (可选) 内存监控

2. 共用模块:
   - src/data/data_loader.py: 数据加载 (提供数据来源)
   - src/data/time_series_window_dataset.py: (可能相关) 时间窗口处理
   - src/evaluation/metrics_calculator.py: (可能用于评估) 指标计算

3. 配置文件:
   - config.yaml:
     └── feature_selection:
         ├── lagged_corr:
         │   ├── min_abs_corr: 最小绝对滞后相关性阈值 (可配置)
         │   └── max_lag: 最大滞后步长 (可配置)
         ├── # 其他参数使用硬编码默认值 (根据用户选择)
         └── correlation_threshold: (旧配置，可能移除或保留用于兼容)
         └── noise_detection: # 噪声检测配置移至此处
             └── low_variance_threshold: 低方差阈值

4. 父类模块:
   - src/data/preprocessing/base_selector.py: BaseFeatureSelector，选择器基类

5. 同阶段数据处理模块:
   - src/data/data_pipeline.py: 调用本模块
   - src/data/feature_engineer.py: 特征工程 (本模块处理其输出)
   - src/data/standardization.py: 标准化 (本模块处理其输出)

核心功能：
1. 多阶段特征筛选:
   - 阶段一: 基础质量检查 (零方差, 高冗余)
   - 阶段二: 领先信号筛选 (滞后相关性, 模型重要性)
   - 阶段三: 最终审查与日志记录
2. 下游维度约束兼容: 确保输出特征数满足后续模型要求

使用规范：
- 输入为 Pandas DataFrame (包含特征和目标 'value15')
- 输出为选定特征的名称列表
- 依赖 ConfigManager 和 LoggerFactory
- 部分关键参数可通过 config.yaml 配置
"""

import time

import lightgbm as lgb  # 用于模型重要性评估
import numpy as np
import pandas as pd
from scipy.cluster.hierarchy import fcluster, linkage
from scipy.stats import spearmanr  # 用于计算等级相关性和p值

from src.utils.config_manager import ConfigManager
from src.utils.logger import LoggerFactory


class FeatureSelector:
    """
    特征选择类，实现多阶段、聚焦领先信号的特征筛选流程。
    采用动态执行与部分配置化策略。
    """
    # 移除默认值常量，所有参数必须从配置中读取

    def __init__(self, config: ConfigManager, logger_factory: LoggerFactory): # 明确要求 ConfigManager 和 LoggerFactory
        """初始化，加载配置并设置默认值"""
        # 规则 66: 依赖注入
        self.config = config
        self.logger = logger_factory.get_logger("FeatureSelector")

        # --- 配置读取与默认值设定 ---
        # 规则 73: 部分参数可配置
        self.target_col = 'value15' # 假设目标列名固定

        try:
            # 读取可配置参数 (属性访问) - 规则 44, 60: 强制配置存在
            if not hasattr(config, 'feature_selection') or config.feature_selection is None:
                raise AttributeError("配置中缺少 'feature_selection' 部分或其值为 None")

            fs_config = config.feature_selection

            if not hasattr(fs_config, 'lagged_corr') or fs_config.lagged_corr is None:
                raise AttributeError("配置 feature_selection 中缺少 'lagged_corr' 部分或其值为 None")

            lagged_corr_config = fs_config.lagged_corr
            if hasattr(lagged_corr_config, 'min_abs_corr'):
                self.lagged_corr_min_abs_corr = lagged_corr_config.min_abs_corr
                self.logger.info(f"从配置加载 feature_selection.lagged_corr.min_abs_corr: {self.lagged_corr_min_abs_corr}")
            else:
                raise AttributeError("配置 feature_selection.lagged_corr 中缺少 'min_abs_corr'")

            if hasattr(lagged_corr_config, 'max_lag'):
                self.lagged_corr_max_lag = lagged_corr_config.max_lag
                self.logger.info(f"从配置加载 feature_selection.lagged_corr.max_lag: {self.lagged_corr_max_lag}")
            else:
                raise AttributeError("配置 feature_selection.lagged_corr 中缺少 'max_lag'")

            # 读取模型头数 (用于下游维度约束)
            if hasattr(config, 'model') and hasattr(config.model, 'n_heads'):
                self.n_heads = config.model.n_heads
                self.logger.info(f"从配置加载 model.n_heads: {self.n_heads}")
            else:
                # 尝试从 model_config 读取 (兼容旧配置)
                if hasattr(config, 'model_config') and hasattr(config.model_config, 'n_heads'):
                    self.n_heads = config.model_config.n_heads
                    self.logger.info(f"从配置加载 model_config.n_heads: {self.n_heads}")
                else:
                    raise AttributeError("配置中缺少 'model.n_heads' 或 'model_config.n_heads'")

        except AttributeError as e:
            self.logger.error(f"配置读取失败: {e}")
            raise

    def _calculate_lagged_correlations(self, df: pd.DataFrame) -> dict[str, float]:
        """
        计算每个特征与目标的最大滞后相关性。

        Args:
            df: 包含特征和目标列的DataFrame。

        Returns:
            字典，键为特征名，值为最大滞后相关性。
        """
        self.logger.info(f"开始计算滞后相关性 (max_lag={self.lagged_corr_max_lag})...")

        # 获取目标列和特征列
        target = df[self.target_col]
        features = df.drop(columns=[self.target_col])

        # 初始化结果字典
        max_abs_corrs = {}

        # 对每个特征计算滞后相关性
        for feature_name in features.columns:
            feature = features[feature_name]
            max_abs_corr = 0.0

            # 计算不同滞后步长的相关性
            for lag in range(1, self.lagged_corr_max_lag + 1):
                # 对齐数据
                aligned_target = target[lag:]
                aligned_lagged_feature = feature[:-lag].reset_index(drop=True)
                aligned_target = aligned_target.reset_index(drop=True)

                # 确保数据长度一致
                min_len = min(len(aligned_target), len(aligned_lagged_feature))
                if min_len <= 5:  # 至少需要5个数据点才能计算有意义的相关性
                    continue

                aligned_target = aligned_target[:min_len]
                aligned_lagged_feature = aligned_lagged_feature[:min_len]

                # 计算斯皮尔曼等级相关系数
                try:
                    # spearmanr 返回 (correlation, p-value)
                    correlation_result = spearmanr(aligned_lagged_feature, aligned_target)
                    correlation_val = correlation_result[0]  # 只使用相关性值
                    # 修正类型处理: 确保 correlation_val 是 float 且非 NaN
                    if not isinstance(correlation_val, int | float) or np.isnan(correlation_val):
                        correlation_val = 0.0
                    # 对确认后的 float 取绝对值
                    abs_corr = abs(float(correlation_val))

                    # 更新最大绝对相关性
                    if abs_corr > max_abs_corr:
                        max_abs_corr = abs_corr

                except ValueError as e:
                    # 如果输入包含 NaN 或无穷大，spearmanr 会报错
                    self.logger.warning(f"计算特征 '{feature_name}' lag={lag} 的相关性时出错: {e}. 跳过此滞后。")
                    continue

            max_abs_corrs[feature_name] = max_abs_corr
            # self.logger.debug(f"特征 '{feature_name}': Max Abs Lagged Corr = {max_abs_corr:.4f} at lag {best_lag}")

        self.logger.info("滞后相关性计算完成。")
        return max_abs_corrs

    def _perform_multi_stage_selection(self, df: pd.DataFrame) -> list[str]:
        """
        执行多阶段特征选择流程。

        Args:
            df: 包含特征和目标列的 DataFrame。

        Returns:
            最终选定的特征名称列表。
        """
        if not isinstance(df, pd.DataFrame):
            raise TypeError("输入必须是 Pandas DataFrame")
        if self.target_col not in df.columns:
            raise ValueError(f"目标列 '{self.target_col}' 不在 DataFrame 中")

        initial_features = df.drop(columns=[self.target_col]).columns.tolist()
        self.logger.info("\n=== 多阶段特征选择开始 ===")
        self.logger.info(f"初始特征数: {len(initial_features)}")
        self.logger.info(f"输入数据形状: {df.shape}")

        selected_features = initial_features[:]  # 创建副本进行操作
        removal_log = {}  # 记录每步移除的特征
        importances: pd.Series | None = None  # 初始化为 None 并添加类型提示

        # --- 预计算滞后相关性 (用于阶段1和阶段2) ---
        max_lagged_corrs = self._calculate_lagged_correlations(df.copy())  # 使用副本避免修改原df

        # --- 阶段一：基础数据质量检查与静态过滤 ---
        self.logger.info("\n--- 阶段一：基础质量检查与静态过滤 ---")

        # 1. 缺失值过滤
        self.logger.info("开始缺失值过滤...")
        missing_ratio = df[selected_features].isnull().mean()
        features_to_remove_missing = missing_ratio[missing_ratio > 0.10].index.tolist()
        features_to_fill_missing = missing_ratio[(missing_ratio > 0) & (missing_ratio <= 0.10)].index.tolist()

        if features_to_remove_missing:
            removal_log['high_missing'] = features_to_remove_missing
            selected_features = [f for f in selected_features if f not in features_to_remove_missing]
            self.logger.info(f"移除缺失率 > 10% 的特征 ({len(features_to_remove_missing)}): {features_to_remove_missing}")
        else:
            self.logger.info("无特征因缺失率 > 10% 被移除。")

        if features_to_fill_missing:
            # 新逻辑：三级双向滚动均值填充，不使用0充底
            self.logger.info(f"对缺失率 <= 10% 的特征 ({len(features_to_fill_missing)}) 中的 NaN 值进行三级双向滚动均值填充 (window=7/28/42, forward/backward)")

            # 初始化计数器
            features_needing_larger_window = []  # 需要第二级窗口(28天)的特征
            features_needing_largest_window = []  # 需要第三级窗口(42天)的特征
            features_with_remaining_nans = []  # 三级填充后仍有NaN的特征

            # 第一级填充：7天窗口
            for feature in features_to_fill_missing:
                # 前向填充
                df[feature] = df[feature].rolling(window=7, min_periods=1).mean()
                # 检查是否仍有NaN
                nan_count = df[feature].isna().sum()
                if nan_count > 0:
                    features_needing_larger_window.append(feature)

            if features_needing_larger_window:
                self.logger.info(f"{len(features_needing_larger_window)} 个特征需要第二级窗口(28天)进行填充: {features_needing_larger_window}")

                # 第二级填充：28天窗口
                for feature in features_needing_larger_window:
                    # 前向填充
                    df[feature] = df[feature].rolling(window=28, min_periods=1).mean()
                    # 检查是否仍有NaN
                    nan_count = df[feature].isna().sum()
                    if nan_count > 0:
                        features_needing_largest_window.append(feature)

            if features_needing_largest_window:
                self.logger.info(f"{len(features_needing_largest_window)} 个特征需要第三级窗口(42天)进行填充: {features_needing_largest_window}")

                # 第三级填充：42天窗口
                for feature in features_needing_largest_window:
                    # 前向填充
                    df[feature] = df[feature].rolling(window=42, min_periods=1).mean()
                    # 检查是否仍有NaN
                    nan_count = df[feature].isna().sum()
                    if nan_count > 0:
                        features_with_remaining_nans.append((feature, nan_count))

            if features_with_remaining_nans:
                total_remaining_nans = sum(count for _, count in features_with_remaining_nans)
                error_msg = f"{len(features_with_remaining_nans)} 个特征在三级窗口填充后仍有共 {total_remaining_nans} 个 NaN 值，数据质量问题"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            else:
                self.logger.info("所有特征的 NaN 值已通过三级滚动均值填充策略成功处理。")
        self.logger.info(f"阶段一 (缺失值过滤后) 剩余特征数: {len(selected_features)}")

        # 2. 零或近零方差过滤
        # 从配置中获取低方差阈值
        if not hasattr(self.config, 'feature_selection') or self.config.feature_selection is None:
            raise AttributeError("FeatureSelector: 'feature_selection' 配置缺失或为 None")
        fs_config = self.config.feature_selection

        # 获取低方差阈值
        if hasattr(fs_config, 'noise_detection') and fs_config.noise_detection is not None:
            noise_config = fs_config.noise_detection
            if hasattr(noise_config, 'low_variance_threshold') and noise_config.low_variance_threshold is not None:
                low_var_threshold = float(noise_config.low_variance_threshold)
                self.logger.info(f"使用低方差阈值: {low_var_threshold}")
            else:
                error_msg = "配置 feature_selection.noise_detection 中缺少 'low_variance_threshold' 或其值为 None"
                self.logger.error(error_msg)
                raise AttributeError(error_msg)
        else:
            error_msg = "配置 feature_selection 中缺少 'noise_detection' 或其值为 None"
            self.logger.error(error_msg)
            raise AttributeError(error_msg)

        # 计算方差并过滤低方差特征
        self.logger.info("开始低方差特征过滤...")
        variances = df[selected_features].var()
        low_var_features = variances[variances < low_var_threshold].index.tolist()

        if low_var_features:
            removal_log['low_variance'] = low_var_features
            selected_features = [f for f in selected_features if f not in low_var_features]
            self.logger.info(f"移除方差 < {low_var_threshold} 的特征 ({len(low_var_features)}): {low_var_features}")
        else:
            self.logger.info(f"无特征因方差 < {low_var_threshold} 被移除。")
        self.logger.info(f"阶段一 (低方差过滤后) 剩余特征数: {len(selected_features)}")

        # 3. 高冗余特征过滤 (基于聚类)
        self.logger.info("开始基于聚类的高冗余特征过滤...")
        if len(selected_features) > 1:
            # 从配置中获取高冗余阈值
            if not hasattr(noise_config, 'high_correlation_threshold') or noise_config.high_correlation_threshold is None:
                error_msg = "配置 feature_selection.noise_detection 中缺少 'high_correlation_threshold' 或其值为 None"
                self.logger.error(error_msg)
                raise AttributeError(error_msg)

            # 确保高冗余阈值是数值类型
            try:
                redundancy_threshold = float(noise_config.high_correlation_threshold)
                self.logger.info(f"使用高冗余阈值: {redundancy_threshold}")
            except (ValueError, TypeError) as e:
                error_msg = f"配置中的 high_correlation_threshold ({noise_config.high_correlation_threshold}) 不是有效的数值"
                self.logger.error(error_msg)
                raise ValueError(error_msg) from e

            # --- 预过滤零标准差特征 ---
            self.logger.info("计算特征标准差以识别恒定列...")
            stds = df[selected_features].std(ddof=1)  # 使用 Pandas std，ddof=1 保持一致性
            std_threshold = 1e-9  # 定义一个小的阈值来判断标准差是否接近零
            constant_features = stds[stds < std_threshold].index.tolist()

            if constant_features:
                self.logger.warning(f"发现 {len(constant_features)} 个标准差接近零 (<{std_threshold:.1e}) 的特征，将从相关性计算和聚类中排除: {constant_features}")
                features_for_corr = [f for f in selected_features if f not in constant_features]
            else:
                self.logger.info("未发现标准差接近零的特征。")
                features_for_corr = selected_features[:]  # 使用所有特征

            # 仅在有足够特征进行相关性计算时继续
            if len(features_for_corr) <= 1:
                self.logger.warning("用于相关性计算的特征数不足 (<=1)，跳过基于聚类的冗余过滤。")
            else:
                self.logger.info(f"使用 {len(features_for_corr)} 个非恒定特征进行相关性计算和聚类。")

                # 在计算相关性之前，进一步清理数据
                df_for_corr = df[features_for_corr].copy()

                # 检查并处理无穷大值
                inf_mask = np.isinf(df_for_corr.values)
                if np.any(inf_mask):
                    inf_count = np.sum(inf_mask)
                    self.logger.warning(f"在特征数据中发现 {inf_count} 个无穷大值，将替换为NaN")
                    df_for_corr = df_for_corr.replace([np.inf, -np.inf], np.nan)

                # 计算相关性矩阵 (仅对非恒定特征)
                try:
                    corr_matrix = df_for_corr.corr(method='pearson', min_periods=5).abs()  # 至少需要5个有效数据点

                    # 检查相关性矩阵是否包含非有限值
                    if not np.all(np.isfinite(corr_matrix.values)):
                        self.logger.warning("相关性矩阵包含非有限值，正在进行修复...")
                        # 将对角线设为1.0（自相关）
                        np.fill_diagonal(corr_matrix.values, 1.0)
                        # 将其他NaN值替换为0.0（表示无相关性）
                        corr_matrix = corr_matrix.fillna(0.0)
                        self.logger.info("已修复相关性矩阵中的非有限值")

                except Exception as e:
                    error_msg = f"计算相关性矩阵时出错: {e}"
                    self.logger.error(error_msg)
                    # 移除跳过机制，直接抛出异常
                    raise RuntimeError(error_msg) from e

                # 计算距离矩阵 (1 - abs(correlation)) 的压缩形式
                # np.triu_indices_from 获取上三角（不含对角线）的索引
                distance_condensed = 1.0 - corr_matrix.values[np.triu_indices_from(corr_matrix.values, k=1)]
                # 处理可能的浮点精度问题，确保距离非负
                distance_condensed = np.clip(distance_condensed, 0, 1)

                # 检查并处理非有限值
                if not np.all(np.isfinite(distance_condensed)):
                    self.logger.warning("距离矩阵包含非有限值（NaN或Inf），正在进行清理...")
                    # 记录非有限值的数量和位置
                    nan_count = np.sum(np.isnan(distance_condensed))
                    inf_count = np.sum(np.isinf(distance_condensed))
                    self.logger.warning(f"发现 {nan_count} 个NaN值和 {inf_count} 个无穷大值")

                    # 将NaN和Inf替换为合理的默认值
                    # NaN替换为最大距离1.0（表示完全不相关）
                    # Inf替换为最大距离1.0
                    distance_condensed = np.nan_to_num(distance_condensed, nan=1.0, posinf=1.0, neginf=0.0)
                    self.logger.info("已将非有限值替换为合理的默认值")

                # 执行层次聚类 (average linkage)
                try:
                    linkage_matrix = linkage(distance_condensed, method='average')

                    # 根据距离阈值形成簇
                    # 距离阈值 = 1 - 相关性阈值
                    distance_threshold = 1.0 - redundancy_threshold
                    cluster_labels = fcluster(linkage_matrix, distance_threshold, criterion='distance')

                    # 将非恒定特征映射到簇
                    feature_clusters = pd.DataFrame({'feature': features_for_corr, 'cluster': cluster_labels})
                    clusters = feature_clusters.groupby('cluster')['feature'].apply(list)

                    features_to_remove_redundancy = set()
                    num_redundant_clusters = 0
                    self.logger.info(f"聚类完成，共形成 {len(clusters)} 个簇。开始筛选冗余簇代表...")

                    for cluster_id, features_in_cluster in clusters.items():
                        if len(features_in_cluster) > 1:  # 只处理包含多个特征的冗余簇
                            num_redundant_clusters += 1
                            # 寻找簇内滞后相关性最高的特征
                            best_feature = None
                            max_corr = -1.0  # 初始化为-1确保任何非负相关性都能覆盖
                            cluster_lagged_corrs = {}
                            for feature in features_in_cluster:
                                # 获取预计算的滞后相关性
                                lagged_corr = max_lagged_corrs.get(feature, 0.0)
                                cluster_lagged_corrs[feature] = lagged_corr
                                if lagged_corr > max_corr:
                                    max_corr = lagged_corr
                                    best_feature = feature

                            # 如果找到了最佳特征 (理论上除非所有相关性<=0，否则总能找到)
                            if best_feature:
                                # 将簇内除最佳特征外的所有特征标记为移除
                                for feature in features_in_cluster:
                                    if feature != best_feature:
                                        features_to_remove_redundancy.add(feature)
                                self.logger.debug(f"冗余簇 {cluster_id} ({len(features_in_cluster)} features: {features_in_cluster}): 保留 '{best_feature}' (最高滞后相关性 {max_corr:.4f})。簇内滞后相关性: {cluster_lagged_corrs}")
                            else:
                                # 极端情况处理：如果簇内所有特征滞后相关性都为0或负数
                                self.logger.warning(f"冗余簇 {cluster_id} ({features_in_cluster}) 中未找到滞后相关性 > 0 的特征。将随机保留第一个特征 '{features_in_cluster[0]}' 并移除其他特征。")
                                for feature in features_in_cluster[1:]:
                                    features_to_remove_redundancy.add(feature)

                    # 执行移除
                    if features_to_remove_redundancy:
                        removal_log['redundancy_cluster'] = list(features_to_remove_redundancy)
                        selected_features = [f for f in selected_features if f not in features_to_remove_redundancy]
                        self.logger.info(f"通过聚类发现 {num_redundant_clusters} 个冗余簇 (阈值>{redundancy_threshold})，基于滞后相关性移除冗余特征 ({len(features_to_remove_redundancy)}): {list(features_to_remove_redundancy)}")
                    else:
                        self.logger.info(f"未通过聚类发现需要移除的冗余特征 (阈值>{redundancy_threshold})。")

                except Exception as e:
                    error_msg = f"基于聚类的冗余过滤失败: {e}"
                    self.logger.error(error_msg)
                    # 移除跳过机制，直接抛出异常
                    raise RuntimeError(error_msg) from e
        else:
            self.logger.info("特征数不足 (<=1)，跳过基于聚类的冗余检查。")
        self.logger.info(f"阶段一 (聚类冗余过滤后) 剩余特征数: {len(selected_features)}")

        # --- 阶段二：领先指标筛选 ---
        self.logger.info("\n--- 阶段二：领先指标筛选 ---")

        # 1. 滞后相关性分析筛选
        features_passing_lagged_corr = []
        removed_by_lagged_corr = []
        # 确保比较的是数值类型
        min_abs_corr = self.lagged_corr_min_abs_corr  # 使用从配置加载的阈值
        for feature in selected_features:
            if max_lagged_corrs.get(feature, 0.0) >= min_abs_corr:
                features_passing_lagged_corr.append(feature)
            else:
                removed_by_lagged_corr.append(feature)

        if removed_by_lagged_corr:
            removal_log['lagged_corr'] = removed_by_lagged_corr
            self.logger.info(f"基于滞后相关性阈值 (<{min_abs_corr}) 移除特征 ({len(removed_by_lagged_corr)}): {removed_by_lagged_corr}")
        else:
            self.logger.info(f"所有剩余特征均满足滞后相关性阈值 (>{min_abs_corr})。")
        selected_features = features_passing_lagged_corr
        self.logger.info(f"阶段二 (滞后相关性后) 剩余特征数: {len(selected_features)}")

        # 2. 模型重要性评估 (使用LightGBM)
        if len(selected_features) >= 5:  # 至少需要5个特征才能进行有意义的重要性评估
            self.logger.info("开始基于模型重要性的特征筛选...")
            # 获取特征和目标数据，并删除包含NaN的行
            x_with_target = df[[*selected_features, self.target_col]].dropna()
            if len(x_with_target) < len(df) * 0.5:  # 如果删除NaN后数据减少超过50%，记录警告
                self.logger.warning(f"删除包含NaN的行后，数据量从 {len(df)} 减少到 {len(x_with_target)}，这可能影响模型重要性评估的准确性。")
            x = x_with_target[selected_features]
            y = x_with_target[self.target_col]

            # 使用全部数据训练
            x_train, y_train = x, y

            # 训练LightGBM模型，使用算法相关的固定参数
            # 这些是算法本身的参数，不是业务逻辑参数，可以硬编码
            lgbm_params = {
                'objective': 'regression_l1',  # MAE
                'metric': 'mae',
                'n_estimators': 100,
                'learning_rate': 0.05,
                'feature_fraction': 0.9,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1,
                'n_jobs': -1,
                'seed': 42
            }
            model = lgb.LGBMRegressor(**lgbm_params)
            model.fit(x_train, y_train)

            # 获取特征重要性
            feature_importances = pd.Series(model.feature_importances_, index=x.columns)
            feature_importances = feature_importances / feature_importances.sum()  # 归一化
            importances = feature_importances  # 保存到外部变量，用于后续约束处理

            # 按重要性排序 (仅用于日志记录)
            sorted_importances = feature_importances.sort_values(ascending=False)
            self.logger.debug(f"特征重要性排序: {sorted_importances}")

            # 获取重要性阈值
            if hasattr(fs_config, 'importance_threshold') and fs_config.importance_threshold is not None:
                importance_threshold = float(fs_config.importance_threshold)
                self.logger.info(f"使用特征重要性阈值: {importance_threshold}")

                # 基于阈值筛选特征
                features_to_remove_importance = []
                for feature, importance in feature_importances.items():
                    if importance < importance_threshold:
                        features_to_remove_importance.append(feature)

                if features_to_remove_importance:
                    removal_log['low_importance'] = features_to_remove_importance
                    selected_features = [f for f in selected_features if f not in features_to_remove_importance]
                    self.logger.info(f"基于模型重要性移除特征 ({len(features_to_remove_importance)}): {features_to_remove_importance}")
                else:
                    self.logger.info("无特征因模型重要性低而被移除。")
            else:
                self.logger.info("未配置特征重要性阈值，跳过基于重要性的筛选。")

        else:
            self.logger.error("特征数不足，无法进行模型重要性评估。")
            raise ValueError("特征数不足，无法进行模型重要性评估。至少需要5个特征。")
        self.logger.info(f"阶段二 (模型重要性后) 剩余特征数: {len(selected_features)}")

        # --- 下游维度约束检查 ---
        self.logger.info("\n--- 下游维度约束检查 ---")
        num_selected = len(selected_features)
        if num_selected == 0:
            self.logger.error("没有特征通过所有筛选阶段。")
            raise ValueError("特征选择后没有剩余特征。")

        # 寻找满足 (k+1) % n_heads == 0 的最大 k <= num_selected
        # 注意：这里的 k 是指特征的数量，目标列 value15 不参与此处的 k
        actual_k = 0
        # 添加断言确保 self.n_heads 是正整数
        assert isinstance(self.n_heads, int) and self.n_heads > 0, \
            f"self.n_heads 必须是正整数，但得到的是 {self.n_heads} (类型: {type(self.n_heads)})"
        for k_candidate in range(num_selected, 0, -1):
            # 确保 k+1 能被 n_heads 整除
            if (k_candidate + 1) % self.n_heads == 0:
                actual_k = k_candidate
                self.logger.info(f"找到满足下游维度要求的最大特征数 k = {actual_k} (k+1={actual_k+1} 可被 {self.n_heads} 整除)")
                break  # 找到最大的k后立即退出循环

        if actual_k == 0:
            self.logger.error(f"在 {num_selected} 个通过筛选的特征中，无法找到满足 (k+1)%{self.n_heads}==0 的特征数量 k。")
            # 规则 50: 开发阶段直接崩溃
            raise ValueError(f"无法选择满足下游维度要求的特征数量。通过筛选的特征数: {num_selected}")
        elif actual_k < num_selected:
            # 如果需要减少特征以满足约束，则按重要性（或滞后相关性）排序并截断
            self.logger.info(f"需要将特征数从 {num_selected} 减少到 {actual_k} 以满足下游约束。")
            # 使用模型重要性排序，不再回退到滞后相关性
            self.logger.info("使用模型重要性进行排序以满足约束。")
            # 确保 importances 只包含当前 selected_features
            valid_importances = importances.reindex(selected_features).dropna()
            if valid_importances.empty:
                self.logger.error("模型重要性 Series 为空或全为 NaN，无法进行特征排序。")
                raise ValueError("模型重要性计算结果无效，无法进行特征排序。")

            sorted_features = valid_importances.sort_values(ascending=False).index.tolist()

            removed_by_constraint = sorted_features[actual_k:]
            selected_features = sorted_features[:actual_k]
            removal_log['constraint'] = removed_by_constraint
            self.logger.info(f"为满足约束移除特征 ({len(removed_by_constraint)}): {removed_by_constraint}")
        else:
            self.logger.info("当前选定特征数已满足下游维度约束。")

        # --- 阶段三：最终审查与日志记录 ---
        self.logger.info("\n--- 阶段三：最终审查与日志记录 ---")

        # 记录每个阶段移除的特征数量
        total_removed = len(initial_features) - len(selected_features)
        self.logger.info("特征选择总结:")
        self.logger.info(f"- 初始特征数: {len(initial_features)}")
        self.logger.info(f"- 最终选择特征数: {len(selected_features)}")
        self.logger.info(f"- 总移除特征数: {total_removed} ({total_removed/len(initial_features)*100:.1f}%)")

        # 记录各阶段移除的特征数量
        for reason, features in removal_log.items():
            self.logger.info(f"- 因 {reason} 移除: {len(features)} 个特征")

        # 记录最终选择的特征
        self.logger.info(f"最终选择的特征 ({len(selected_features)}): {selected_features}")

        return selected_features

    def select(self, df: pd.DataFrame) -> list[str]:
        """
        执行特征选择流程并返回选定的特征名称列表。
        这是主要的公共接口方法。

        Args:
            df: 包含特征和目标列 'value15' 的 Pandas DataFrame。

        Returns:
            选中的特征名称列表。
        """
        selection_start = time.time()
        self.logger.info("调用 FeatureSelector.select()...")

        # 执行新的多阶段选择流程
        selected_feature_names = self._perform_multi_stage_selection(df)

        self.logger.info(f"特征选择完成: 选中 {len(selected_feature_names)} 个特征")
        self.logger.info(f"总耗时: {time.time() - selection_start:.3f}s")

        if not selected_feature_names:
            self.logger.error("最终没有选择任何特征！")
            raise ValueError("特征选择未能选出任何特征。")

        return selected_feature_names
