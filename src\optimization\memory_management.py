"""
内存管理与监控模块

本模块提供内存管理和监控相关功能，包括CUDA内存监控、清理和优化功能。
这些功能用于在超参数优化过程中避免内存溢出问题，并提供内存使用情况的监控和报告。

主要组件:
----------
1. 函数式接口:
   - cleanup_memory: 清理CUDA内存并报告内存使用情况
   - monitor_memory: 监控当前CUDA内存使用情况
   - get_memory_stats: 获取详细的内存统计信息
   - optimize_memory_usage: 优化PyTorch内存使用

2. 类式接口:
   - MemoryMonitoringManager: 内存监控管理器，负责CUDA内存监控的生命周期管理

使用方式:
----------
1. 函数式接口:
```python
from src.optimization.memory_management import cleanup_memory, monitor_memory, get_memory_stats

# 清理内存
cleanup_memory(trial)

# 监控内存
current_memory, peak_memory = monitor_memory()

# 获取内存统计信息
memory_stats = get_memory_stats()
```

2. 类式接口:
```python
from src.optimization.memory_management import MemoryMonitoringManager

# 创建内存监控管理器
memory_monitor = MemoryMonitoringManager(logger)

# 启动内存监控
memory_monitor.start_monitoring()

# 报告内存使用情况
memory_monitor.report_memory_stats()

# 停止内存监控
memory_monitor.stop_monitoring()
```

注意：
----------
OOM风险检查功能已移至parameter_exploration.py中，通过参数约束处理实现
"""


import optuna
import torch

from src.utils.logger import LoggerFactory

try:
    from src.utils.cuda import cuda_manager
except ImportError:
    cuda_manager = None

# 获取日志记录器
logger = LoggerFactory().get_logger("MemoryManager")

def cleanup_memory(trial: optuna.trial.Trial | None = None) -> dict[str, float]:
    """
    清理CUDA内存并报告内存使用情况。

    Args:
        trial: Optuna试验对象（可选，用于日志记录）

    Returns:
        Dict[str, float]: 包含内存使用情况的字典
    """
    trial_prefix = f"Trial #{trial.number}: " if trial is not None else ""
    memory_stats = {}

    if not torch.cuda.is_available():
        logger.info(f"{trial_prefix}CUDA不可用，跳过内存清理")
        return memory_stats

    try:
        # 记录清理前的内存状态
        before_clear = torch.cuda.memory_allocated() / (1024 * 1024)
        before_reserved = torch.cuda.memory_reserved() / (1024 * 1024)
        memory_stats['before_clear'] = before_clear
        memory_stats['before_reserved'] = before_reserved

        # 清理缓存
        torch.cuda.empty_cache()

        # 记录清理后的内存状态
        after_clear = torch.cuda.memory_allocated() / (1024 * 1024)
        after_reserved = torch.cuda.memory_reserved() / (1024 * 1024)
        memory_stats['after_clear'] = after_clear
        memory_stats['after_reserved'] = after_reserved

        # 记录峰值内存使用
        peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)
        peak_reserved = torch.cuda.max_memory_reserved() / (1024 * 1024)
        memory_stats['peak_memory'] = peak_memory
        memory_stats['peak_reserved'] = peak_reserved

        # 重置峰值统计
        torch.cuda.reset_peak_memory_stats()
        memory_stats['reset_peak'] = True

        logger.info(f"{trial_prefix}CUDA内存清理结果:")
        logger.info(f"{trial_prefix}- 清理前: 已分配={before_clear:.2f}MB, 已缓存={before_reserved:.2f}MB")
        logger.info(f"{trial_prefix}- 清理后: 已分配={after_clear:.2f}MB, 已缓存={after_reserved:.2f}MB")
        logger.info(f"{trial_prefix}- 峰值使用: 已分配={peak_memory:.2f}MB, 已缓存={peak_reserved:.2f}MB")
    except Exception as e:
        logger.warning(f"{trial_prefix}CUDA内存统计失败: {e}")
        memory_stats['error'] = str(e)

    return memory_stats

def monitor_memory() -> tuple[float, float]:
    """
    监控当前CUDA内存使用情况。

    Returns:
        Tuple[float, float]: (当前内存使用量(MB), 峰值内存使用量(MB))
    """
    if not torch.cuda.is_available():
        logger.info("CUDA不可用，无法监控内存")
        return 0.0, 0.0

    try:
        current_memory = torch.cuda.memory_allocated() / (1024 * 1024)
        peak_memory = torch.cuda.max_memory_allocated() / (1024 * 1024)
        logger.debug(f"CUDA内存使用: 当前={current_memory:.2f}MB, 峰值={peak_memory:.2f}MB")
        return current_memory, peak_memory
    except Exception as e:
        logger.warning(f"无法获取CUDA内存使用情况: {e}")
        return 0.0, 0.0

def get_memory_stats() -> dict[str, float]:
    """
    获取详细的内存统计信息。

    Returns:
        Dict[str, float]: 包含详细内存统计的字典
    """
    stats = {}

    if not torch.cuda.is_available():
        logger.info("CUDA不可用，无法获取内存统计")
        return stats

    try:
        # 基本内存统计
        stats['allocated'] = torch.cuda.memory_allocated() / (1024 * 1024)
        stats['reserved'] = torch.cuda.memory_reserved() / (1024 * 1024)
        stats['max_allocated'] = torch.cuda.max_memory_allocated() / (1024 * 1024)
        stats['max_reserved'] = torch.cuda.max_memory_reserved() / (1024 * 1024)

        # 计算可用内存
        device = torch.cuda.current_device()
        total_memory = torch.cuda.get_device_properties(device).total_memory / (1024 * 1024)
        stats['total'] = total_memory
        stats['available'] = total_memory - stats['reserved']
        stats['utilization'] = (stats['reserved'] / total_memory) * 100

        logger.debug(f"CUDA内存统计: 已分配={stats['allocated']:.2f}MB, "
                    f"已缓存={stats['reserved']:.2f}MB, "
                    f"总内存={stats['total']:.2f}MB, "
                    f"利用率={stats['utilization']:.2f}%")

        return stats
    except Exception as e:
        logger.warning(f"获取CUDA内存统计失败: {e}")
        stats['error'] = str(e)
        return stats

def optimize_memory_usage() -> None:
    """
    优化PyTorch内存使用，设置环境变量和内存分配策略。
    """
    if not torch.cuda.is_available():
        logger.info("CUDA不可用，跳过内存优化")
        return

    try:
        # 设置内存分配策略
        torch.cuda.set_per_process_memory_fraction(0.9)  # 限制使用90%的可用GPU内存
        torch.backends.cudnn.benchmark = True  # 启用cuDNN自动调优

        # 启用内存缓存
        torch.cuda.empty_cache()

        logger.info("已应用PyTorch内存优化设置")
    except Exception as e:
        logger.warning(f"应用内存优化设置失败: {e}")

class MemoryMonitoringManager:
    """内存监控管理器，负责CUDA内存监控的生命周期管理"""

    def __init__(self, logger):
        """
        初始化内存监控管理器

        Args:
            logger: 日志记录器
        """
        self.logger = logger
        self.is_monitoring = False

    def start_monitoring(self, interval=10, peak_interval=5, peak_window=60):
        """
        启动CUDA内存监控

        Args:
            interval: 内存监控间隔（秒）
            peak_interval: 峰值内存监控采样间隔（秒）
            peak_window: 峰值内存监控窗口大小（秒）
        """
        if torch.cuda.is_available() and cuda_manager is not None:
            try:
                cuda_manager.start_monitoring(interval=interval)
                cuda_manager.start_peak_memory_monitoring(interval=peak_interval, window_size=peak_window)
                self.is_monitoring = True
                self.logger.info("CUDA内存监控已启动")
            except Exception as e:
                self.logger.warning(f"启动CUDA内存监控失败: {e}")
        else:
            self.logger.info("CUDA不可用或cuda_manager未导入，跳过内存监控")

    def stop_monitoring(self):
        """停止CUDA内存监控"""
        if self.is_monitoring and torch.cuda.is_available() and cuda_manager is not None:
            try:
                cuda_manager.stop_monitoring()
                cuda_manager.stop_peak_memory_monitoring()
                self.logger.info("CUDA内存监控已停止")
                self.is_monitoring = False
            except Exception as e:
                self.logger.warning(f"停止CUDA内存监控失败: {e}")

    def report_memory_stats(self):
        """报告内存使用情况"""
        if not self.is_monitoring or not torch.cuda.is_available() or cuda_manager is None:
            return

        try:
            memory_stats = cuda_manager.get_memory_stats()
            peak_memory_info = cuda_manager.get_peak_memory_info()

            self.logger.info("内存使用统计:")
            self.logger.info(f"- 已分配内存: {memory_stats['allocated_mb']:.2f}MB")
            self.logger.info(f"- 已缓存内存: {memory_stats['reserved_mb']:.2f}MB")
            self.logger.info(f"- 峰值已分配: {memory_stats['max_allocated_mb']:.2f}MB")
            self.logger.info(f"- 峰值已缓存: {memory_stats['max_reserved_mb']:.2f}MB")

            if peak_memory_info:
                self.logger.info(f"- 峰值内存利用率: {peak_memory_info.utilization:.1%}")
                self.logger.info(f"- 峰值已用内存: {peak_memory_info.used_gb:.2f}GB")
        except Exception as e:
            self.logger.warning(f"获取内存统计信息失败: {e}")

    def clear_cache(self):
        """清理CUDA缓存"""
        if torch.cuda.is_available():
            try:
                if cuda_manager is not None:
                    cuda_manager.clear_cache()
                else:
                    torch.cuda.empty_cache()
                self.logger.info("已清理CUDA缓存")
            except Exception as e:
                self.logger.warning(f"清理CUDA缓存失败: {e}")

