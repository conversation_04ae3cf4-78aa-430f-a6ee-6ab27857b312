#!/usr/bin/env python
"""
参数来源检测测试运行脚本

本脚本用于运行参数来源检测测试，验证系统中的所有参数都完全来自配置文件，
而不是硬编码或默认值回退。

使用方法:
    python tests/run_parameter_source_tests.py

测试覆盖范围:
1. 核心参数来源检测（7个测试）
   - 配置加载过程中的硬编码默认值检测
   - 默认值回退机制检测
   - 参数可追溯性验证
   - 硬编码常量的合理性检查
   - 配置验证严格性测试
   - 参数探索默认值检测
   - ConfigManager默认配置方法禁用检测

2. 深度参数来源分析（4个测试）
   - 配置加载器回退逻辑检测
   - 基础配置类默认值检测
   - 配置数据类默认值检测
   - 环境变量回退机制检测

3. 特定模块参数来源检测（3个测试）
   - 特征选择器参数来源检测
   - 模型参数来源检测
   - 训练参数来源检测

4. 配置依赖关系和完整性（3个测试）
   - 配置交叉验证默认值检测
   - 嵌套配置参数来源检测
   - 配置类型一致性检测

5. 动态配置和运行时参数（3个测试）
   - 优化器管理器参数来源检测
   - 资源管理器参数来源检测
   - CUDA管理器参数来源检测

6. 数据管道参数来源（3个测试）
   - 数据加载器参数来源检测
   - 数据预处理器参数来源检测
   - 特征工程参数来源检测

7. 模型架构参数来源（3个测试）
   - 生成器参数来源检测
   - 判别器参数来源检测
   - 注意力机制参数来源检测

8. 配置完整性和边界条件（4个测试）
   - 配置边界值验证检测
   - 配置缺少必需部分检测
   - 配置循环依赖检测
   - 跨模块参数一致性检测

9. 配置安全性和敏感信息（3个测试）
   - 硬编码凭证检测
   - 配置文件权限安全性检测
   - 配置验证防注入机制检测
"""

import subprocess
import sys
from pathlib import Path


def run_parameter_source_tests():
    """运行参数来源检测测试"""
    print("=" * 80)
    print("参数来源检测测试")
    print("=" * 80)
    print()

    print("测试目标：确保所有参数完全来自配置文件，而不是硬编码或默认值回退")
    print()

    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    test_file = project_root / "tests" / "test_utils" / "test_config_parameter_source.py"

    if not test_file.exists():
        print(f"❌ 测试文件不存在: {test_file}")
        return False

    print(f"📁 测试文件: {test_file}")
    print()

    # 运行测试
    try:
        print("🚀 开始运行参数来源检测测试...")
        print("-" * 60)

        # 运行pytest命令
        cmd = [
            sys.executable, "-m", "pytest",
            str(test_file),
            "-v",
            "--tb=short",
            "--no-header"
        ]

        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )

        # 输出测试结果
        if result.stdout:
            print(result.stdout)

        if result.stderr:
            print("错误输出:")
            print(result.stderr)

        print("-" * 60)

        if result.returncode == 0:
            print("✅ 参数来源检测测试完成！")
            print()
            print("测试验证了以下9个方面共33个测试项:")
            print("  ✓ 核心参数来源检测 - 配置加载和默认值回退机制")
            print("  ✓ 深度参数来源分析 - 配置类和加载器的严格性")
            print("  ✓ 特定模块参数来源 - 各模块硬编码常量检测")
            print("  ✓ 配置依赖关系和完整性 - 交叉验证和类型一致性")
            print("  ✓ 动态配置和运行时参数 - 管理器参数来源检测")
            print("  ✓ 数据管道参数来源 - 数据处理模块参数检测")
            print("  ✓ 模型架构参数来源 - 模型组件参数检测")
            print("  ✓ 配置完整性和边界条件 - 边界值和循环依赖检测")
            print("  ✓ 配置安全性和敏感信息 - 安全性和防注入检测")
            print()
            print("🎉 系统参数管理全面符合最佳实践！")
            return True
        else:
            print("❌ 参数来源检测测试失败！")
            print()
            print("可能的问题:")
            print("  • 存在硬编码的默认值")
            print("  • 存在不当的回退机制")
            print("  • 参数无法追溯到配置文件")
            print("  • 存在不合理的硬编码常量")
            print()
            print("请检查测试输出并修复相关问题。")
            return False

    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")
        return False


def main():
    """主函数"""
    print("参数来源检测测试运行器")
    print("=" * 40)
    print()

    success = run_parameter_source_tests()

    print()
    print("=" * 80)

    if success:
        print("🎯 测试总结: 所有参数来源检测测试通过")
        print("💡 建议: 继续保持良好的配置管理实践")
        sys.exit(0)
    else:
        print("⚠️  测试总结: 发现参数来源问题")
        print("🔧 建议: 请修复配置管理中的问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
