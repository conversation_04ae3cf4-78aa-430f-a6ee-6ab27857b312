"""通用组件模块 - 提供模型中可复用的基础组件"""


import torch
from torch import nn

from src.models.base.base_module import BaseModule


class OutputProjection(BaseModule):
    """输出投影模块 - 将特征投影到输出空间"""

    def __init__(
        self,
        input_dim: int,
        output_dim: int,
        hidden_dim: int | None = None
    ):
        """初始化输出投影模块

        Args:
            input_dim: 输入维度
            output_dim: 输出维度
            hidden_dim: 隐藏维度，如果为None则使用input_dim
        """
        super().__init__("OutputProjection")

        # 设置隐藏维度
        if hidden_dim is None:
            hidden_dim = input_dim

        # 输出投影网络
        self.projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim, output_dim)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入特征 [batch_size, seq_length, input_dim]

        Returns:
            torch.Tensor: 输出特征 [batch_size, seq_length, output_dim]
        """
        # 强制使用FP32计算，避免混合精度训练中的数值不稳定性
        input_dtype = x.dtype
        x_fp32 = x.to(torch.float32)

        # 使用FP32进行计算
        output_fp32 = self.projection(x_fp32)

        # 检查数值稳定性
        if torch.isnan(output_fp32).any() or torch.isinf(output_fp32).any():
            self.logger.warning("OutputProjection检测到NaN/Inf输出，进行修正")
            # 替换NaN/Inf值为0
            output_fp32 = torch.where(torch.isfinite(output_fp32), output_fp32, torch.zeros_like(output_fp32))

        # 转回原始数据类型
        return output_fp32.to(input_dtype)
