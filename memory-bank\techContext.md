# 技术背景

此文件记录项目使用的技术、开发设置、技术约束、依赖关系和工具使用模式。

## 使用的技术

- **核心框架**：Python。
- **深度学习库**：PyTorch (包括 `nn.Module`, `torch.optim`, `torch.cuda.amp` for mixed precision, `torch.utils.data` for datasets/dataloaders, `torch.utils.checkpoint` for gradient checkpointing)。
- **模型架构**：生成对抗网络 (GAN)。
    - `GANModel` (`gan_model.py`): 协调器类，继承自 `BaseModel`。**已完成初步重构，样本生成方法已整合，冗余梯度裁剪已移除。**
    - `TimeSeriesGenerator` (`generator.py`): 作为生成器的协调类，继承自 `BaseModule`。它实例化并按顺序协调以下核心组件的工作：
        1. `FeatureEncoder` (`components/feature_encoder.py`): 对输入条件特征进行编码。
        2. `NoiseProcessor` (`components/noise_processor.py`): 对噪声输入进行编码。
        3. `DynamicFeatureFusion` (`dynamic_feature_fusion.py`): 将编码后的特征和编码后的噪声动态融合。
        4. `SequenceGenerator` (`components/sequence_generator.py`): 以融合后的特征为输入，生成最终的单维时序输出。
        它实现了动态特征维度适应逻辑（主要通过重新初始化 `FeatureEncoder`），并且为了优化参数量，其组件的部分内部维度被设置为较小值（如64）。
    - `TimeSeriesDiscriminator` (`discriminator.py`): 判别器，继承自 `BaseModule`。其复杂架构包括：
        - **独立评估分支**: 从 `src/models/gan/discriminator_branches.py` 导入并使用三个专用分支：
            - `TrendConsistencyBranch`: 基于 `Conv1d` 评估趋势。
            - `FeatureCorrelationBranch`: 基于 `Linear` 层评估特征相关性。
            - `TemporalPatternBranch`: 基于双向 `GRU` 评估时序模式。
        - **深度注意力流**: 输入数据在送入分支前，会经过一系列复杂的处理：
            1. `DynamicFeatureFusion` (`dynamic_feature_fusion.py`): 对目标序列和条件特征进行初步融合。
            2. 拼接融合后的目标序列与原始条件特征。
            3. 依次通过 `TemporalMultiHeadWrapper` (from `components/temporal.py`), `AdaptiveDilationAttention` (from `components/adaptive_attention.py`, 通过 `attention_factory.py` 创建), 和 `MultiScaleAttention` (from `attention.py`) 进行深度特征提取。
        - **动态权重融合**: 各分支的输出分数通过一个小型神经网络 (`weight_net`) 进行动态加权（使用 `Softmax`），得到最终判别分数。
        - **动态维度适应**: 能够根据输入条件特征维度的变化，重新初始化其内部组件（包括分支和注意力模块）。
    - `LossCalculator` (`loss_calculator.py`): 计算对抗损失, 梯度惩罚, 特征匹配损失 (依赖 `FeatureMatchingLoss`), 时序一致性损失。
    - `NoiseManager` (`noise_manager.py`): 管理噪声生成，支持基础噪声和结构化噪声。
    - `FeatureMatchingLoss` (`feature_matching.py`): 实现特征匹配损失，基于真实特征和生成特征的余弦相似度。文件中还包含 `PerceptualLoss` 和 `StyleLoss` (目前未直接使用)。
    - `GANTrainer` (`trainer.py`): GAN训练管理器，继承自 `BaseModel`，负责整个训练循环、优化器管理 (通过 `OptimizerManager`)、学习率调整 (通过 `GanLossRatioLrBalancer`)、检查点 (通过 `ModelSaver`)、早停 (通过 `ModelStateManager`)、动态批次大小 (通过 `BatchSizeOptimizer`)。**初步分析确认其职责划分清晰，与 `GANModel` 交互合理，未发现明显重复或不完整实现。**
    - `Predictor` (`src/predict.py`): 作为预测流程的简单API接口层，处理Numpy输入/输出，内部实例化并调用 `PredictionRunner`。
    - `PredictionRunner` (`src/predict.py`): 实现核心预测逻辑，包括调用 `GANModel.generate`、批量预测、置信区间计算（通过多次采样）、结果逆标准化（使用 `target_standardizer`）和预测缓存（通过 `ResourceManager`）。
- **模型基类 (`src/models/base/`)**:
    - `BaseModel` (`base_model.py`): 提供模型生命周期管理、训练流程抽象（部分已弃用）、优化器管理（部分已弃用）、混合精度初始化、错误处理、资源监控。
    - `BaseModule` (`base_module.py`): 提供参数初始化、参数统计、张量验证、日志记录、错误处理等基础功能。
- **注意力机制与特征提取 (`src/models/gan/`)**:
    - `attention_components.py`: 包含 `MultiHeadAttention` (标准多头注意力实现)。
    - `components/temporal.py`: 包含 `TemporalCoherence` (1D卷积实现时序平滑) 和 `TemporalMultiHeadWrapper` (封装 `nn.MultiheadAttention` 用于时序数据)。
    - `components/adaptive_attention.py`: 包含 `AdaptiveDilationAttention` (使用CUDA流，可学习扩张率，多 `nn.MultiheadAttention` 层) 和 `MultiLayerAttention` (堆叠 `nn.MultiheadAttention` 和FFN)。
    - `components/attention_factory.py`: 提供工厂函数 `create_adaptive_attention` 和 `create_multi_layer_attention`。
    - `attention.py`: 包含 `MultiScaleAttention` (基于扩张卷积和可学习尺度权重)。
    - `feature_extractor.py`: 包含 `MultiScaleFeatureExtractor` (多尺度1D卷积) 和 `TimeSeriesFeatureExtractor` (多层线性网络)。
- **通用组件 (`src/models/gan/components/common.py`)**: 包含 `OutputProjection` (两层线性网络)。
- **数值计算**：Numpy。
- **数据处理**：Pandas。
- **特征工程库**：`arch` (GARCH), `scikit-learn` (可选)。
- **并发与队列**：`threading`, `queue` (标准库)。
- **日期时间处理**: `dateutil.relativedelta`, `pathlib`, `math` (用于 `NoiseManager`), `datetime` (用于 `BaseModel` 和 `GANTrainer`)。
- **配置管理**：YAML (`config.yaml`)。
- **日志记录库**：标准库 `logging`, `psutil`, `traceback` (用于错误处理)。
- **CUDA**：GPU加速。

## 开发设置

- **版本控制**：Git。
- **配置文件**：YAML (`config.yaml`)，通过 `src.utils.config` 包进行模块化管理。关键配置节包括 `model` (GAN参数如 `noise_dim`, `hidden_dim`, `n_heads`, `dropout_rate`, `generator_type`, `num_layers`, `dimensions.base_dim`, `loss` weights, `noise` config), `data` (如 `feature_dim`, `window_size`), `training` (如 `optimizer`, `mixed_precision`, `checkpoint` settings, `save_dir`, `lambda_gp`, `early_stopping`, `lr_balancer`, `balance`, `batch_size_optimizer`), `prediction` (如 `batch_size`), `paths` (如 `model_dir`, `logs_dir`)。
- **日志系统** (`src.utils.logger.py`)。
- **数据加载与处理 (`src/data/`)**:
    - `TimeSeriesDataLoader` (`data_loader.py`): 负责从CSV加载原始数据，执行严格的时间过滤 (基于 `config.data.optimization_start_date`) 和加载时无缺失值检查。通过 `threading` 和 `torch.cuda.stream` 实现高效的CUDA流异步预取。依赖 `TimeSeriesWindowDataset` 进行后续的窗口化处理。
    - `TimeSeriesWindowDataset` (`windowed_time_series.py`): 将数据转换为滑动窗口格式，并进行训练/验证/测试集分割。
- **特征工程 (`src/data/feature_engineering/`)**:
    - `FeatureManager` (`feature_manager.py`): 作为特征工程的核心协调器。它实例化并管理一个独立的 `TimeFeatureGenerator` (用于时间特征预处理) 以及一系列可配置的层级特征生成器 (如 `DiffFeatureGenerator`, `LagFeatureGenerator`, `WindowFeatureGenerator`, `VolatilityFeatureGenerator`, `InteractionFeatureGenerator`)。通过 `enhance_features` 方法，根据 `config.feature_engineering.layers` 的配置，按层级顺序调用这些生成器。它还处理特征的组合与传递（依据 `keep_original_in_final` 和 `keep_input_features` 配置），并能将目标序列传递给需要的特定生成器。
- **模型实现 (`src/models/`)**:
    - 基类: `BaseModel`, `BaseModule`, `ModelStateManager`, `ModelSaver` (`src/models/base/`).
    - GAN核心: `GANModel` (协调器, 提供统一的 `generate` 接口用于样本生成), `TimeSeriesGenerator` (协调器), `TimeSeriesDiscriminator` (协调器), `LossCalculator`, `NoiseManager`, `FeatureMatchingLoss`, `GANTrainer`, `GANEvaluator`, `Predictor`, `PredictionRunner` (`src/models/gan/` 和 `src/predict.py`)。
    - GAN组件:
        - 生成器组件 (`TimeSeriesGenerator` 使用): `FeatureEncoder` (`components/feature_encoder.py`), `NoiseProcessor` (`components/noise_processor.py`, 内部使用 `NoiseEncoder` from `components/noise_encoder.py`), `DynamicFeatureFusion` (`dynamic_feature_fusion.py`), `SequenceGenerator` (`components/sequence_generator.py`, 内部使用 `OutputProjection` from `components/common.py` 和 `TemporalCoherence` from `components/temporal.py`)。
        - 判别器核心及分支: `TimeSeriesDiscriminator` (`discriminator.py`), `discriminator_branches.py` (包含 `TrendConsistencyBranch`, `FeatureCorrelationBranch`, `TemporalPatternBranch`)。
        - 通用注意力与特征提取: `attention_components.py` (`MultiHeadAttention`), `components/temporal.py` (`TemporalMultiHeadWrapper`), `components/adaptive_attention.py` (`AdaptiveDilationAttention`, `MultiLayerAttention`), `components/attention_factory.py`, `attention.py` (`MultiScaleAttention`), `feature_extractor.py` (`MultiScaleFeatureExtractor`, `TimeSeriesFeatureExtractor`)。
        - 其他核心模块: `DynamicFeatureFusion` (`dynamic_feature_fusion.py`) (也被判别器使用)。
- **训练管理 (`src/models/gan/trainer.py`)**: `GANTrainer` 负责完整的训练循环。**初步分析确认其职责划分清晰，与 `GANModel` 交互合理，未发现明显重复或不完整实现。**
- **预测管理 (`src/predict.py`)**: `Predictor` 和 `PredictionRunner` 负责预测流程。
- **混合精度训练**: 通过 `src/utils/amp_manager.py` 管理，`BaseModel` (因此 `GANModel` 和 `GANTrainer` 也) 初始化并使用 `amp_manager`。
- **梯度检查点**: `GANModel` 支持。
- **动态特征维度处理**: `GANModel`, `TimeSeriesGenerator`, `TimeSeriesDiscriminator` 支持。
- **监控与错误处理**: `ModelStatsMonitor` 和 `ErrorMonitor` (`src/utils/`) 用于训练过程监控。
- **测试框架**：Pytest。
- **开发环境**：Windows PowerShell。

## 技术约束

- **CUDA依赖**。
- **Python版本**：(待明确)。
- **特定库版本**：(待明确)。
- **数据格式**：CSV。
- **配置文件存在性与完整性**：`config.yaml` 及其中的关键配置项（如 `model.type`, `training.mixed_precision` 的完整字段, `model.noise_dim`, `training.save_dir`, `paths.model_dir`, `paths.logs_dir` 等）必须存在且有效。

## 依赖关系

- **核心依赖**：`torch`, `pandas`, `numpy`, `pyyaml`, `psutil`, `arch`, `python-dateutil`, `pathlib`, `threading`, `queue`, `math`, `logging`, `traceback`, `datetime`, `abc`, `collections.defaultdict` (标准库)。
- **可能/可选依赖**：`scikit-learn`。
- **开发/测试依赖**：`pytest`。

## 工具使用模式

- **配置驱动开发**。
- **模块化与组件化开发**。
- **面向对象编程 (OOP)**: 大量使用类和继承 (`BaseModel`, `BaseModule`, `GANTrainer`)。
- **抽象基类 (ABC)**: `BaseModel` 使用 `abc.abstractmethod`。
- **协议 (Interfaces/Protocols)**: 如 `TrainableModel`, `SizedDataset` (在 `GANTrainer` 中使用)。
- **标准化日志记录**: `BaseModule` 和 `BaseModel` 集成了日志功能。
- **性能优化技术**: CUDA流预取, 混合精度训练, 梯度检查点, 动态批次大小调整。
- **层级化设计**: 特征工程。
- **工厂模式**: `create_adaptive_attention`, `ConfigManager.from_yaml`, `get_batch_size_optimizer`。
- **单例模式**: `LoggerFactory`, `CUDAManager`, `ModelStateManager`。
- **装饰器与上下文管理器**。
- **Dataclasses**: `NoiseConfig`, `BatchSizeOptimizerConfig`。
- **健壮性设计**: `BaseModel`, `BaseModule`, `GANTrainer` 包含输入/输出验证、配置验证和详细的错误处理逻辑。
