"""特征通道系统测试模块

相关模块:
1. 被测试模块:
   - src/data/feature_channel.py: 特征通道系统实现
2. 依赖模块:
   - src/utils/logger.py: 日志系统
   - src/utils/cuda_manager.py: GPU资源管理
"""

import pytest
import torch

from src.data.feature_channel import FeatureChannel


@pytest.fixture
def sample_features():
    """创建测试特征数据"""
    return torch.randn(100, 20)  # [seq_len, feature_dim]

@pytest.fixture
def sample_batch_features():
    """创建批量测试特征数据"""
    return torch.randn(32, 50, 20)  # [batch_size, seq_len, feature_dim]

@pytest.mark.batch3  # 特征通道测试
class TestFeatureChannel:
    """测试单一特征通道"""

    def test_initialization(self):
        """测试特征通道初始化"""
        channel = FeatureChannel(
            channel_id=0,
            feature_index=1,
            name='test_channel'
        )

        assert channel.channel_id == 0
        assert channel.name == 'test_channel'
        assert channel.feature_index == 1
        assert channel.feature is None
        assert channel.processed_feature is None

    def test_feature_loading(self, sample_features):
        """测试特征加载"""
        channel = FeatureChannel(
            channel_id=0,
            feature_index=1,
            name='test_channel'
        )

        channel.load_features(sample_features)
        assert channel.feature is not None
        assert channel.feature.shape == (100, 1)  # 选择了1个特征

    def test_feature_processing(self, sample_features):
        """测试特征处理"""
        channel = FeatureChannel(
            channel_id=0,
            feature_index=1,
            name='test_channel'
        )

        channel.load_features(sample_features)
        processed_channel = channel.process()

        assert isinstance(channel.processed_feature, torch.Tensor), "processed_feature必须是Tensor类型"
        # Relax tolerance for floating point comparisons
        assert torch.allclose(channel.processed_feature.mean(), torch.tensor(0.0), atol=1e-5)
        assert torch.allclose(channel.processed_feature.std(unbiased=False), torch.tensor(1.0), atol=1e-5) # Use unbiased=False to match implementation
        assert processed_channel is channel  # 确认返回的是自身

    def test_custom_processor(self, sample_features):
        """测试自定义处理函数"""
        def custom_processor(x):
            return x * 2

        channel = FeatureChannel(
            channel_id=0,
            feature_index=1,
            name='test_channel'
        )

        channel.load_features(sample_features)
        processed_channel = channel.process(custom_processor)

        # 确保特征已正确处理且类型正确
        assert isinstance(channel.processed_feature, torch.Tensor), "processed_feature必须是Tensor类型"
        assert isinstance(channel.feature, torch.Tensor), "feature必须是Tensor类型"
        assert torch.allclose(channel.processed_feature, channel.feature * 2)
        assert processed_channel is channel  # 确认返回的是自身

    def test_error_handling(self):
        """测试错误处理"""
        channel = FeatureChannel(
            channel_id=0,
            feature_index=0,
            name='test_channel'
        )

        # 测试不支持的维度
        with pytest.raises(ValueError):
            channel.load_features(torch.randn(10, 5, 3, 2))  # 4维张量

        # 测试未加载特征
        with pytest.raises(ValueError):
            channel.process()
