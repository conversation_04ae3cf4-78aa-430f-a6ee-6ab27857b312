"""资源管理模块 - 提供系统资源管理和优化功能

本模块负责管理和优化系统资源，包括：
1. 内存管理 - 监控和限制内存使用
2. CPU资源管理 - 优化CPU资源分配
3. GPU资源管理 - 监控和管理GPU内存
4. 缓存管理 - 实现LRU缓存策略，优化内存使用
5. 文件资源管理 - 管理文件系统资源

主要类：
- ResourceManager: 资源管理器，提供资源监控和缓存管理功能

依赖模块：
- psutil: 系统资源监控
- torch: GPU资源管理
- collections.OrderedDict: LRU缓存实现
"""

import gc
import os
import threading
import time
from collections import OrderedDict

import psutil
import torch

from src.utils.config_manager import ConfigManager
from src.utils.logger import get_logger


class ResourceManager:
    """资源管理器 - 负责管理和优化系统资源"""

    def __init__(self, config: ConfigManager):
        """初始化资源管理器

        Args:
            config: 配置管理器
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = config

        # 初始化资源管理参数
        self.memory_limit = None  # 必须从配置加载
        self.cache_size = None  # 必须从配置加载
        self.enable_memory_cache = None  # 必须从配置加载

        # 初始化缓存（使用OrderedDict实现LRU缓存）
        self.prediction_cache = OrderedDict()
        self.feature_cache = OrderedDict()

        # 配置资源管理
        self._configure_resource_management()

        # 启动资源监控
        self._start_resource_monitoring()

        self.logger.info("资源管理器初始化完成")

    def _configure_resource_management(self):
        """配置资源管理"""
        if self.config is None:
            raise ValueError("配置管理器不能为None")

        try:
            # 获取系统配置
            system_config = getattr(self.config, 'system', None)
            if not system_config:
                raise ValueError("系统配置不能为空")

            # 加载内存配置
            try:
                memory_config = system_config.memory if hasattr(system_config, 'memory') else {}
                self.memory_limit = memory_config.get('memory_limit')
                if self.memory_limit is None:
                    raise ValueError("必须配置memory_limit参数")
            except Exception as e:
                raise ValueError(f"内存配置不完整: {e!s}") from e
            if not (0 < self.memory_limit <= 1):
                raise ValueError(f"内存限制必须在0-1之间，当前值: {self.memory_limit}")

            # 加载缓存配置
            try:
                cache_config = system_config.cache if hasattr(system_config, 'cache') else {}
                self.cache_size = cache_config.get('size')
                self.enable_memory_cache = cache_config.get('enable_memory_cache')
                if self.cache_size is None or self.enable_memory_cache is None:
                    raise ValueError("必须配置cache.size和cache.enable_memory_cache参数")
            except Exception as e:
                raise ValueError(f"缓存配置不完整: {e!s}") from e
            if self.cache_size is None or self.enable_memory_cache is None:
                raise ValueError("必须配置cache.size和cache.enable_memory_cache参数")

            self.logger.info(
                f"资源管理配置已加载:\n"
                f"- 内存使用上限: {self.memory_limit * 100}%\n"
                f"- 缓存大小: {self.cache_size}\n"
                f"- 启用内存缓存: {self.enable_memory_cache}"
            )
        except Exception as e:
            self.logger.error(f"加载资源管理配置失败: {e!s}")
            raise

    def _start_resource_monitoring(self):
        """启动资源监控"""
        # 创建监控线程
        self.monitoring_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitoring_thread.start()

    def _monitor_resources(self):
        """监控资源使用情况

        注意：该方法在单独的线程中运行，用于定期监控系统资源使用情况。
        当内存使用超过限制时，会自动清理缓存。

        Raises:
            Exception: 如果在测试模式下运行，异常将被重新抛出以便于测试
        """
        is_test_mode = os.environ.get('TESTING', '0') == '1'

        while True:
            try:
                # 检查内存使用情况
                memory_usage = psutil.virtual_memory().percent / 100

                # 内存监控逻辑 - 记录但不终止程序
                if self.memory_limit is not None and memory_usage > self.memory_limit:
                    mem_info = psutil.virtual_memory()
                    error_msg = (
                        f"内存使用率({memory_usage:.2%})超过限制({self.memory_limit:.2%})\n"
                        f"系统内存状态: 已用{mem_info.used/1024/1024:.2f}MB, "
                        f"可用{mem_info.available/1024/1024:.2f}MB, "
                        f"总量{mem_info.total/1024/1024:.2f}MB"
                    )
                    self.logger.warning(error_msg)
                    # 建议操作但不强制终止
                    self.logger.warning("建议: 1. 检查内存泄漏 2. 增加内存限制 3. 优化内存使用")

                # 每60秒检查一次
                if not is_test_mode:  # 测试模式下不等待
                    time.sleep(60)
                else:
                    break  # 测试模式下只运行一次

            except Exception as e:
                error_msg = f"资源监控失败: {e!s}"
                self.logger.error(error_msg)
                # 移除回退机制，任何异常都应该抛出
                raise RuntimeError(error_msg) from e

    def clear_cache(self):
        """清理缓存"""
        # 清理预测缓存
        self.prediction_cache.clear()

        # 清理特征缓存
        self.feature_cache.clear()

        # 强制垃圾回收
        gc.collect()

        # 清理CUDA缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        self.logger.info("缓存已清理")

    def get_prediction_if_cached(self, features, return_confidence, use_cache):
        """获取缓存的预测结果

        实现LRU缓存策略，当访问缓存项时将其移动到队列末尾，表示最近使用。

        Args:
            features: 输入特征
            return_confidence: 是否返回置信区间
            use_cache: 是否使用缓存

        Returns:
            缓存的预测结果，如果没有缓存则返回None
        """
        if not self.enable_memory_cache or not use_cache:
            return None

        # 计算特征的哈希值作为缓存键
        feature_hash = hash(features.cpu().numpy().tobytes())
        cache_key = (feature_hash, return_confidence)

        # 检查缓存
        if cache_key in self.prediction_cache:
            # 将访问的项移动到队列末尾（最近使用）
            result = self.prediction_cache[cache_key]
            self.prediction_cache.move_to_end(cache_key)
            return result

        return None

    def cache_prediction(self, features, result, return_confidence):
        """缓存预测结果

        实现LRU缓存策略，当缓存满时清除最久未使用的项。

        Args:
            features: 输入特征
            result: 预测结果
            return_confidence: 是否返回置信区间
        """
        if not self.enable_memory_cache:
            return

        # 计算特征的哈希值作为缓存键
        feature_hash = hash(features.cpu().numpy().tobytes())
        cache_key = (feature_hash, return_confidence)

        # 检查缓存大小
        if self.cache_size is not None and len(self.prediction_cache) >= self.cache_size and cache_key not in self.prediction_cache:
            # 如果缓存已满，清理最久未使用的缓存（OrderedDict的第一项）
            self.prediction_cache.popitem(last=False)
            self.logger.debug(f"预测缓存已满，清除最久未使用项，当前缓存大小: {len(self.prediction_cache)}")

        # 缓存预测结果（如果键已存在，将自动移动到队列末尾）
        self.prediction_cache[cache_key] = result

    def get_feature_if_cached(self, data_key):
        """获取缓存的特征

        实现LRU缓存策略，当访问缓存项时将其移动到队列末尾，表示最近使用。

        Args:
            data_key: 数据键

        Returns:
            缓存的特征，如果没有缓存则返回None
        """
        if not self.enable_memory_cache:
            return None

        # 检查缓存
        if data_key in self.feature_cache:
            # 将访问的项移动到队列末尾（最近使用）
            result = self.feature_cache[data_key]
            self.feature_cache.move_to_end(data_key)
            return result

        return None

    def cache_feature(self, data_key, feature):
        """缓存特征

        实现LRU缓存策略，当缓存满时清除最久未使用的项。

        Args:
            data_key: 数据键
            feature: 特征
        """
        if not self.enable_memory_cache:
            return

        # 检查缓存大小
        if self.cache_size is not None and len(self.feature_cache) >= self.cache_size and data_key not in self.feature_cache:
            # 如果缓存已满，清理最久未使用的缓存（OrderedDict的第一项）
            self.feature_cache.popitem(last=False)
            self.logger.debug(f"特征缓存已满，清除最久未使用项，当前缓存大小: {len(self.feature_cache)}")

        # 缓存特征（如果键已存在，将自动移动到队列末尾）
        self.feature_cache[data_key] = feature

    def get_memory_usage(self):
        """获取内存使用情况

        Returns:
            Dict[str, float]: 内存使用信息
        """
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()

        return {
            'rss': memory_info.rss / (1024 * 1024),  # RSS内存(MB)
            'vms': memory_info.vms / (1024 * 1024),  # 虚拟内存(MB)
            'percent': process.memory_percent()  # 内存使用百分比
        }

    def get_gpu_memory_usage(self):
        """获取GPU内存使用情况

        Returns:
            Dict[str, float]: GPU内存使用信息
        """
        if not torch.cuda.is_available():
            return {'available': False}

        # 移除异常处理的回退机制，如果获取失败应该抛出异常
        return {
            'available': True,
            'allocated': torch.cuda.memory_allocated() / (1024 * 1024),  # 已分配显存(MB)
            'reserved': torch.cuda.memory_reserved() / (1024 * 1024),  # 已保留显存(MB)
            'max_allocated': torch.cuda.max_memory_allocated() / (1024 * 1024)  # 最大已分配显存(MB)
        }
