"""特征选择器扩展测试模块

相关模块:
1. 被测试模块:
   - src/data/FeatureSelector.py: 特征选择器实现
2. 依赖模块:
   - src/utils/config_manager.py: 配置管理
   - src/utils/logger.py: 日志系统
"""

import time
from typing import Any  # Added
from unittest.mock import MagicMock, patch

import numpy as np
import pandas as pd
import pytest
import torch

from src.data.preprocessing.feature_selector import FeatureSelector  # Removed NoiseDetector import
from src.utils.config_manager import ConfigManager
from src.utils.logger import LoggerFactory  # Added


@pytest.fixture
def sample_config() -> dict[str, Any]:
    """创建测试配置 (添加 model.n_heads)"""
    return {
        'model': {
            'n_heads': 4 # Added required config
        },
        'features': {
            # Keep old keys for now, though selector uses lagged_corr internally
            'correlation_threshold': 0.5,
            'importance_threshold': 0.5,
            'noise_detection': {
                'low_variance_threshold': 0.1
            },
            'enable_selection': True # Added for consistency
        },
         # Add structure expected by FeatureSelector init for lagged corr defaults
        'feature_selection': {
             'lagged_corr': {
                 'min_abs_corr': 0.1, # Default will be used if not present
                 'max_lag': 24        # Default will be used if not present
             }
        }
    }

@pytest.fixture
def sample_data():
    """创建测试数据"""
    # 生成有相关性的测试数据
    np.random.seed(42)
    data = np.random.randn(100, 5)
    # 第0列与目标强相关
    target = data[:, 0] * 0.8 + np.random.randn(100) * 0.2
    return torch.tensor(data, dtype=torch.float32), torch.tensor(target, dtype=torch.float32).unsqueeze(1)

@pytest.fixture
def sample_dataframe():
    """创建测试DataFrame"""
    np.random.seed(42)
    df = pd.DataFrame({
        'feature1': np.random.randn(100),
        'feature2': np.random.randn(100),
        'feature3': np.random.randn(100),
        'feature4': np.random.randn(100),
        'feature5': np.random.randn(100),
        'target': np.random.randn(100)
    })
    # 使feature1与目标强相关
    df['target'] = df['feature1'] * 0.8 + np.random.randn(100) * 0.2
    return df

@pytest.mark.batch2  # 关键组件测试
class TestFeatureSelectorExtended:
    """测试特征选择器扩展功能"""

    @patch('lightgbm.LGBMRegressor') # Mock importance model
    @patch.object(FeatureSelector, '_calculate_lagged_correlations') # Mock lagged corr calc
    def test_integration_logic(self, mock_calc_lagged_corrs, mock_lgbm, sample_config: dict[str, Any], sample_dataframe: pd.DataFrame):
        """测试特征选择核心逻辑（模拟集成）"""
        # 1. Prepare Mocks and Instances
        # Use MagicMock for ConfigManager to avoid strict init issues
        mock_config = MagicMock(spec=ConfigManager)
        # Set necessary attributes, using sample_config as a base
        mock_config.feature_selection.lagged_corr.min_abs_corr = 0.1 # Example threshold
        mock_config.feature_selection.lagged_corr.max_lag = 5
        mock_config.model.n_heads = sample_config['model']['n_heads']

        logger_factory = LoggerFactory()
        selector = FeatureSelector(mock_config, logger_factory)

        # 2. Prepare DataFrame (rename target column)
        test_df = sample_dataframe.rename(columns={'target': selector.target_col})
        feature_names = test_df.drop(columns=[selector.target_col]).columns.tolist()
        num_features = len(feature_names)

        # 3. Setup Mocks
        # Assume all features have high lagged correlation
        mock_calc_lagged_corrs.return_value = dict.fromkeys(feature_names, 0.8)
        # Assume all features have high importance
        mock_lgbm_instance = mock_lgbm.return_value
        mock_lgbm_instance.fit.return_value = None
        mock_lgbm_instance.feature_importances_ = np.array([10] * num_features)

        # 4. Execute selection
        selected_names = selector.select(test_df)

        # 5. Assertions
        assert isinstance(selected_names, list), "应返回特征名称列表"
        assert len(selected_names) > 0, "应至少选择一个特征"
        assert len(selected_names) <= num_features, "选择的特征数不应超过原始特征数"
        # Check constraint: k=5, n_heads=4 -> (5+1)%4 != 0. Should reduce to k=3.
        # Need to know the sorting order (importance or lagged corr)
        # Let's assume importance is used for sorting when reducing for constraint
        # Mock importance to have a clear order
        mock_lgbm_instance.feature_importances_ = np.array([50, 40, 30, 20, 10])
        # Re-run selection
        selected_names = selector.select(test_df)
        expected_names = sorted(['feature1', 'feature2', 'feature3']) # Top 3 features
        assert len(selected_names) == 3, f"预期因约束选择3个特征, 实际 {len(selected_names)}"
        assert sorted(selected_names) == expected_names, f"预期特征 {expected_names}, 实际 {sorted(selected_names)}"

    @patch('lightgbm.LGBMRegressor') # Mock importance model
    @patch.object(FeatureSelector, '_calculate_lagged_correlations') # Mock lagged corr calc
    def test_dataframe_input_handling(self, mock_calc_lagged_corrs, mock_lgbm, sample_config: dict[str, Any]):
        """测试特征选择器正确处理DataFrame输入"""
        # 1. Setup Mocks and Instances
        mock_config = MagicMock(spec=ConfigManager)
        mock_config.feature_selection.lagged_corr.min_abs_corr = 0.1
        mock_config.feature_selection.lagged_corr.max_lag = 5
        mock_config.model.n_heads = 4
        logger_factory = LoggerFactory()
        selector = FeatureSelector(mock_config, logger_factory)

        # 2. Create test DataFrame
        num_samples = 50
        num_features = 10
        feature_names = [f'feat_{i}' for i in range(num_features)]
        test_df = pd.DataFrame(np.random.randn(num_samples, num_features), columns=feature_names)
        test_df[selector.target_col] = np.random.randn(num_samples)

        # 3. Setup Mocks for internal methods
        mock_calc_lagged_corrs.return_value = dict.fromkeys(feature_names, 0.8) # Assume all pass corr
        mock_lgbm_instance = mock_lgbm.return_value
        mock_lgbm_instance.fit.return_value = None
        mock_lgbm_instance.feature_importances_ = np.array([10] * num_features) # Assume all pass importance

        # 4. Call select method
        selected_names = selector.select(test_df)

        # 5. Assertions
        assert isinstance(selected_names, list)
        # Check constraint: k=10, n_heads=4 -> (10+1)%4 != 0. Should reduce to k=7.
        # Mock importance for clear sorting
        mock_lgbm_instance.feature_importances_ = np.array([100 - i*5 for i in range(num_features)])
        # Re-run selection
        selected_names = selector.select(test_df)
        expected_names = sorted([f'feat_{i}' for i in range(7)]) # Top 7 features
        assert len(selected_names) == 7, f"预期因约束选择7个特征, 实际 {len(selected_names)}"
        assert sorted(selected_names) == expected_names, f"预期特征 {expected_names}, 实际 {sorted(selected_names)}"

    @patch('lightgbm.LGBMRegressor') # Mock importance model
    @patch.object(FeatureSelector, '_calculate_lagged_correlations') # Mock lagged corr calc
    def test_selection_stability(self, mock_calc_lagged_corrs, mock_lgbm, sample_config: dict[str, Any], sample_data):
        """测试特征选择的稳定性（相同数据多次选择结果一致）"""
        # 1. Prepare Mocks and Instances
        mock_config = MagicMock(spec=ConfigManager)
        mock_config.feature_selection.lagged_corr.min_abs_corr = 0.1
        mock_config.feature_selection.lagged_corr.max_lag = 5
        mock_config.model.n_heads = 4 # Use n_heads=4 from sample_config
        logger_factory = LoggerFactory()
        selector = FeatureSelector(mock_config, logger_factory)

        # 2. Prepare DataFrame
        features_tensor, target_tensor = sample_data
        features_np = features_tensor.numpy()
        target_np = target_tensor.numpy()
        num_features = features_np.shape[1]
        feature_names = [f'f_{i}' for i in range(num_features)]
        test_df = pd.DataFrame(features_np, columns=feature_names)
        test_df[selector.target_col] = target_np

        # 3. Setup Mocks (consistent return values)
        mock_lagged_corrs = dict.fromkeys(feature_names, 0.8)
        mock_calc_lagged_corrs.return_value = mock_lagged_corrs
        mock_lgbm_instance = mock_lgbm.return_value
        mock_lgbm_instance.fit.return_value = None
        mock_lgbm_instance.feature_importances_ = np.array([50, 40, 30, 20, 10]) # Consistent importance

        # 4. Run selection multiple times
        selected_names_1 = selector.select(test_df.copy()) # Use copy to be safe
        selected_names_2 = selector.select(test_df.copy())
        selected_names_3 = selector.select(test_df.copy())

        # 5. Assertions
        assert isinstance(selected_names_1, list)
        assert selected_names_1 == selected_names_2, "第一次和第二次选择结果不一致"
        assert selected_names_2 == selected_names_3, "第二次和第三次选择结果不一致"

    @patch('lightgbm.LGBMRegressor') # Mock importance model
    @patch.object(FeatureSelector, '_calculate_lagged_correlations') # Mock lagged corr calc
    def test_outlier_handling(self, mock_calc_lagged_corrs, mock_lgbm, sample_config: dict[str, Any]):
        """测试特征选择对包含异常值/NaN/Inf的DataFrame的处理"""
        # 1. Setup Mocks and Instances
        mock_config = MagicMock(spec=ConfigManager)
        mock_config.feature_selection.lagged_corr.min_abs_corr = 0.1
        mock_config.feature_selection.lagged_corr.max_lag = 5
        mock_config.model.n_heads = 4
        logger_factory = LoggerFactory()
        selector = FeatureSelector(mock_config, logger_factory)

        # 2. Create DataFrame with outliers/NaN/Inf
        np.random.seed(42)
        num_samples = 100
        num_features = 5
        feature_names = [f'f_{i}' for i in range(num_features)]
        features_np = np.random.randn(num_samples, num_features)
        target_np = np.random.randn(num_samples)

        # Add outliers/invalid values
        features_np[0, 0] = 1000.0       # Extreme value
        features_np[1, 1] = np.nan       # NaN
        features_np[2, 2] = np.inf       # Inf
        features_np[3, 3] = -np.inf      # -Inf

        test_df = pd.DataFrame(features_np, columns=feature_names)
        test_df[selector.target_col] = target_np

        # 3. Setup Mocks (assume reasonable values for valid features)
        # Note: _calculate_lagged_correlations should handle NaN/Inf internally
        mock_calc_lagged_corrs.return_value = {name: 0.5 for name in feature_names if np.all(np.isfinite(test_df[name]))}
        mock_lgbm_instance = mock_lgbm.return_value
        mock_lgbm_instance.fit.return_value = None
        # Importance might be calculated only on features without NaN/Inf depending on LGBM handling
        # Assume importance is calculated for the 3 valid features remaining after potential NaN/Inf drops
        # (f_0, f_4 initially, maybe f_1, f_2, f_3 are dropped by internal checks or return 0 corr)
        # Let's assume f_0 and f_4 pass all checks
        mock_lgbm_instance.feature_importances_ = np.array([10, 10]) # Importance for f_0, f_4

        # 4. Execute selection - Expect it to run without crashing
        try:
            selected_names = selector.select(test_df)
            assert isinstance(selected_names, list), "应返回列表"
            # The exact output depends heavily on how NaN/Inf propagate through correlations/LGBM.
            # Main goal is that it doesn't crash and returns *some* valid features.
            assert len(selected_names) >= 0 # Allow for zero selection if all features become invalid
            print(f"Selected features with outliers: {selected_names}") # Log for info
        except Exception as e:
            pytest.fail(f"特征选择器在处理异常值时崩溃: {e!s}")


    @pytest.mark.slow # Mark as slow test
    def test_performance_large_data(self, sample_config: dict[str, Any]):
        """测试特征选择的性能（处理大规模数据）"""
        # 1. Setup Instances with Mock Config
        mock_config = MagicMock(spec=ConfigManager)
        mock_config.feature_selection.lagged_corr.min_abs_corr = 0.05 # Realistic threshold
        mock_config.feature_selection.lagged_corr.max_lag = 10 # Smaller lag for performance test
        mock_config.model.n_heads = 4
        logger_factory = LoggerFactory()
        # Reduce log level for performance test if possible, or use a NullLogger mock
        # logger_factory.set_level('WARNING') # Example if factory supports it
        selector = FeatureSelector(mock_config, logger_factory)

        # 2. Create large DataFrame
        n_samples = 1000 # Keep moderate for reasonable test time
        n_features = 50
        feature_names = [f'perf_feat_{i}' for i in range(n_features)]
        np.random.seed(42)
        features_np = np.random.randn(n_samples, n_features)
        target_np = np.random.randn(n_samples)
        test_df = pd.DataFrame(features_np, columns=feature_names)
        test_df[selector.target_col] = target_np

        # 3. Measure execution time
        start_time = time.time()
        try:
            selected_names = selector.select(test_df)
        except Exception as e:
             pytest.fail(f"Performance test failed during select: {e}")
        end_time = time.time()
        execution_time = end_time - start_time

        # 4. Assertions
        assert isinstance(selected_names, list)
        # We expect some features to be selected with a reasonable threshold
        assert len(selected_names) > 0, "在大规模数据上未选择任何特征"
        assert len(selected_names) <= n_features

        # Performance assertion (adjust threshold as needed)
        max_time_seconds = 15.0
        assert execution_time < max_time_seconds, f"特征选择耗时过长: {execution_time:.2f}秒 (阈值: {max_time_seconds}秒)"
        selector.logger.info(f"Performance test execution time: {execution_time:.2f}s") # Fixed: Use selector's logger

    # [Roo] Removed obsolete test_noise_detector_integration as NoiseDetector is no longer directly used
