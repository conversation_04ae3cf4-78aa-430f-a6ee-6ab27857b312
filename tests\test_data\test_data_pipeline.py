"""数据流水线测试模块

相关模块:
1. 被测试模块:
   - src/data/data_pipeline.py: 数据流水线实现
2. 依赖模块:
   - src/data/protocol.py: 数据集协议
   - src/utils/config_manager.py: 配置管理器
"""

from unittest.mock import patch

import pytest
import torch

from src.data.data_pipeline import DataPipeline


@pytest.mark.batch2
class TestDataPipeline:
    """测试数据流水线"""

    @pytest.fixture
    def sample_config(self):
        """创建测试配置"""
        config = {
            'data': {
                'data_path': 'data/raw/combined_data.csv',
                'target': 'value15',
                'window_size': 24,
                'stride': 1
            }
        }
        return config

    @patch('src.data.data_pipeline.DataPipeline.load_data')
    @patch('src.data.data_pipeline.DataPipeline.validate_data')
    @patch('src.data.data_pipeline.DataPipeline.clean_data')
    @patch('src.data.data_pipeline.DataPipeline.encode_features')
    @patch('src.data.data_pipeline.DataPipeline.engineer_features')
    @patch('src.data.data_pipeline.DataPipeline.select_features')
    @patch('src.data.data_pipeline.DataPipeline.create_windows')
    @patch('src.data.data_pipeline.DataPipeline.prepare_for_model')
    def test_run_pipeline(self, mock_prepare, mock_windows, mock_select, mock_engineer,
                         mock_encode, mock_clean, mock_validate, mock_load, sample_config):
        """测试完整流水线执行"""
        # 设置模拟返回值
        mock_prepare.return_value = {
            'features': torch.randn(10, 24, 5),
            'targets': torch.randn(10, 1)
        }

        # 设置特征和目标数据
        features = torch.randn(100, 5)
        targets = torch.randn(100, 1)

        # 创建数据流水线
        pipeline = DataPipeline('data/raw/combined_data.csv', sample_config)
        pipeline._features = features
        pipeline._targets = targets

        # 模拟窗口创建
        def side_effect(window_size):
            feature_window = features[:window_size]
            target_window = targets[window_size-1]
            pipeline.windows = [(feature_window, target_window)]
            pipeline.window_size = window_size
            pipeline.stride = 1
            pipeline.feature_dim = features.shape[1]

        mock_windows.side_effect = side_effect

        # 执行流水线
        result = pipeline.run_pipeline(window_size=24)

        # 验证所有步骤都被调用
        mock_load.assert_called_once()
        mock_validate.assert_called_once()
        mock_clean.assert_called_once()
        mock_encode.assert_called_once()
        mock_engineer.assert_called_once()
        mock_select.assert_called_once()
        mock_windows.assert_called_once_with(24)
        mock_prepare.assert_called_once()

        # 验证结果
        assert 'features' in result
        assert 'targets' in result
        assert result['features'].shape == (10, 24, 5)
        assert result['targets'].shape == (10, 1)

    @patch('src.data.data_loader.TimeSeriesDataLoader')
    def test_load_data(self, mock_loader_class, sample_config):
        """测试数据加载"""
        # 设置模拟返回值
        features = torch.randn(100, 5)
        targets = torch.randn(100, 1)

        # 创建模拟对象
        mock_loader = mock_loader_class.return_value
        mock_loader.load_data.return_value = (features, targets)

        # 创建数据流水线
        pipeline = DataPipeline('data/raw/combined_data.csv', sample_config)

        # 执行数据加载
        pipeline.load_data()

        # 验证数据加载器被调用
        mock_loader.load_data.assert_called_once()

        # 验证数据被正确设置
        assert pipeline._features is not None and pipeline._targets is not None
        assert torch.equal(pipeline._features, features)
        assert torch.equal(pipeline._targets, targets)

    def test_data_protocol_implementation(self, sample_config):
        """测试协议实现"""
        # 创建数据流水线
        pipeline = DataPipeline('data/raw/combined_data.csv', sample_config)

        # 验证实现了TimeSeriesDatasetProtocol
        assert hasattr(pipeline, '__len__')
        assert hasattr(pipeline, '__getitem__')
        assert hasattr(pipeline, 'get_all_data')

    def test_clean_data(self, sample_config):
        """测试数据清洗 (NaN 和 Inf 处理)"""

        # 创建数据流水线
        pipeline = DataPipeline('data/raw/combined_data.csv', sample_config)

        # 设置特征数据（包含一些缺失值和异常值）
        features = torch.tensor([
            [1.0, 2.0, 3.0],
            [4.0, float('nan'), 6.0],  # 包含NaN
            [7.0, 8.0, 9.0],
            [float('inf'), 11.0, 12.0]  # 包含Inf
        ])
        pipeline._features = features

        # 执行数据清洗
        pipeline.clean_data()

        # 验证数据清洗结果
        assert pipeline._features is not None
        assert not torch.isnan(pipeline._features).any(), "清洗后的数据不应包含NaN"
        assert not torch.isinf(pipeline._features).any(), "清洗后的数据不应包含Inf"
