"""时序数据集测试模块

相关模块:
1. 被测试模块:
   - src/data/base_time_series.py: 基础时序数据集实现
2. 依赖模块:
   - src/data/protocol.py: 数据集协议
"""

from unittest.mock import MagicMock

import pytest
import torch

from src.data.protocol import TimeSeriesDatasetProtocol
from src.utils.config_manager import ConfigManager


@pytest.fixture
def sample_data():
    """创建测试数据"""
    # 创建特征数据
    features = torch.randn(100, 5)  # [seq_len, feature_dim]
    # 创建目标数据
    targets = torch.randn(100)  # [seq_len]
    return features, targets

@pytest.fixture
def sample_config():
    """创建测试配置"""
    config = ConfigManager.from_yaml("tests/test_config.yaml")
    config.update({
        'batch_size': 32,
        'shuffle': True
    })
    return config

@pytest.mark.batch1
class TestTimeSeriesDatasetProtocol:
    """测试时序数据集协议"""

    def test_protocol_interface(self):
        """测试协议接口"""
        # 创建模拟对象
        dataset = MagicMock(spec=TimeSeriesDatasetProtocol)

        # 设置模拟属性
        dataset.features = torch.randn(10, 5)
        dataset.targets = torch.randn(10)
        dataset.feature_components = {
            'trend': torch.randn(10, 5),
            'seasonal': torch.randn(10, 5)
        }

        # 测试属性访问
        assert isinstance(dataset.features, torch.Tensor)
        assert isinstance(dataset.targets, torch.Tensor)
        assert isinstance(dataset.feature_components, dict)

        # 测试协议方法
        dataset.__getitem__.return_value = (torch.randn(5), torch.randn(1))
        item = dataset[0]
        assert len(item) == 2
        assert isinstance(item[0], torch.Tensor)
        assert isinstance(item[1], torch.Tensor)

    def test_protocol_implementation(self, sample_data):
        """测试协议实现"""
        # 创建实现协议的模拟对象
        class MockDataset:
            def __init__(self, features, targets):
                self._features = features
                self._targets = targets
                self._feature_components = {}

            @property
            def features(self):
                return self._features

            @property
            def targets(self):
                return self._targets

            @property
            def feature_components(self):
                return self._feature_components

            def __getitem__(self, idx):
                return {'features': self._features[idx], 'target': self._targets[idx]}

            def get_all_data(self):
                return {'features': self._features, 'targets': self._targets}

            def __len__(self):
                return len(self._features)

        features, targets = sample_data
        dataset = MockDataset(features, targets)

        # 验证协议实现
        assert isinstance(dataset, TimeSeriesDatasetProtocol)
        assert torch.equal(dataset.features, features)
        assert torch.equal(dataset.targets, targets)
        assert isinstance(dataset.feature_components, dict)
