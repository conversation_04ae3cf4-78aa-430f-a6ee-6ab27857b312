"""预处理模块 - 提供数据预处理和特征工程功能

本模块包含数据预处理相关的功能，包括：
1. 数据验证 (DataValidator) - 专注于数据质量检查和验证
2. 数据清洗 (DataCleaner) - 负责可以修正的数据缺陷的检测和修正
3. 特征选择 (FeatureSelector) - 多阶段特征筛选
4. 特征选择基类 (BaseFeatureSelector) - 特征选择器的基本接口

注意：
- 特征编码功能已迁移到feature_engineer.py
- 每个模块都专注于单一职责，以提高代码的可维护性
"""

from .base_selector import BaseFeatureSelector
from .data_cleaner import DataCleaner
from .data_validator import DataValidator
from .feature_selector import FeatureSelector

__all__ = [
    'BaseFeatureSelector',
    'DataCleaner',
    'DataValidator',
    'FeatureSelector'
]
