"""自适应CUDA流管理器测试脚本

此脚本用于测试自适应CUDA流管理功能，通过创建多个流并监控GPU利用率，
验证流数量是否会根据GPU利用率动态调整。
"""

import sys
import threading
import time

import torch

from src.utils.cuda import cuda_manager
from src.utils.logger import get_logger

# 设置输出不缓冲
with open(sys.stdout.fileno(), mode='w', buffering=1) as f:
    sys.stdout = f

# 初始化日志
logger = get_logger("test_adaptive_stream")

# 直接打印到控制台
def print_log(message):
    print(f"[TEST] {message}")
    sys.stdout.flush()

def test_adaptive_stream_manager():
    """测试自适应流管理器"""
    print_log("开始测试自适应流管理器")

    # 确保CUDA可用
    if not torch.cuda.is_available():
        print_log("CUDA不可用，无法测试自适应流管理器")
        logger.error("CUDA不可用，无法测试自适应流管理器")
        return

    print_log(f"CUDA可用: {torch.cuda.get_device_name()}")

    # 配置CUDA管理器
    print_log("加载配置文件...")
    import yaml
    with open("config.yaml", encoding='utf-8') as f:
        config = yaml.safe_load(f)

    # 确保自适应流管理已启用
    adaptive_enabled = config.get('system', {}).get('cuda', {}).get('streams', {}).get('adaptive', {}).get('enabled', False)
    print_log(f"自适应流管理启用状态: {adaptive_enabled}")

    if not adaptive_enabled:
        print_log("自适应流管理未启用，请在配置文件中启用")
        logger.error("自适应流管理未启用，请在配置文件中启用")
        return

    # 配置CUDA管理器
    print_log("配置CUDA管理器...")
    cuda_manager.configure(config['system']['cuda'])
    print_log("配置CUDA管理器完成")

    # 创建多个流
    streams = []
    try:
        print_log("开始创建流...")
        # 创建10个流
        for i in range(10):
            stream_name = f"test_stream_{i}"
            print_log(f"创建流 {stream_name}...")
            stream = cuda_manager.create_stream(stream_name)
            streams.append((stream_name, stream))
            logger.info(f"创建流 {stream_name}")

            # 获取流统计信息
            stats = cuda_manager.get_stream_stats()
            print_log(f"流统计信息: {stats}")
            logger.info(f"流统计信息: {stats}")

            # 等待一段时间，让自适应管理器有时间调整
            print_log("等待自适应管理器调整...")
            time.sleep(1)

        # 模拟高负载
        print_log("模拟高GPU负载...")
        logger.info("模拟高GPU负载...")
        high_load_threads = []
        for i in range(5):
            print_log(f"启动高负载线程 {i}...")
            thread = threading.Thread(
                target=simulate_high_load,
                args=(f"high_load_{i}", 10),  # 10秒高负载
                daemon=True
            )
            thread.start()
            high_load_threads.append(thread)

        # 等待高负载完成
        print_log("等待高负载线程完成...")
        for thread in high_load_threads:
            thread.join()

        # 获取流统计信息
        print_log("获取高负载后流统计信息...")
        stats = cuda_manager.get_stream_stats()
        print_log(f"高负载后流统计信息: {stats}")
        logger.info(f"高负载后流统计信息: {stats}")

        # 等待一段时间，让自适应管理器有时间调整
        print_log("等待自适应管理器调整...")
        logger.info("等待自适应管理器调整...")
        for i in range(15):
            print_log(f"等待调整... {i+1}/15 秒")
            time.sleep(1)

        # 再次获取流统计信息
        print_log("获取调整后流统计信息...")
        stats = cuda_manager.get_stream_stats()
        print_log(f"调整后流统计信息: {stats}")
        logger.info(f"调整后流统计信息: {stats}")

        # 模拟低负载
        print_log("模拟低GPU负载...")
        logger.info("模拟低GPU负载...")
        for i in range(15):
            print_log(f"低负载模拟... {i+1}/15 秒")
            time.sleep(1)  # 只是等待，不执行任何GPU操作

        # 获取流统计信息
        print_log("获取低负载后流统计信息...")
        stats = cuda_manager.get_stream_stats()
        print_log(f"低负载后流统计信息: {stats}")
        logger.info(f"低负载后流统计信息: {stats}")

    finally:
        # 释放所有流
        print_log("释放所有流...")
        for stream_name, _ in streams:
            try:
                print_log(f"释放流 {stream_name}...")
                cuda_manager.release_stream(stream_name)
                logger.info(f"释放流 {stream_name}")
            except Exception as e:
                print_log(f"释放流 {stream_name} 失败: {e!s}")
                logger.error(f"释放流 {stream_name} 失败: {e!s}")

        # 最终统计
        print_log("获取最终流统计信息...")
        stats = cuda_manager.get_stream_stats()
        print_log(f"最终流统计信息: {stats}")
        logger.info(f"最终流统计信息: {stats}")

        print_log("测试完成!")

def simulate_high_load(name: str, duration: int):
    """模拟高GPU负载

    Args:
        name: 任务名称
        duration: 持续时间（秒）
    """
    print_log(f"开始高负载任务 {name}，持续 {duration} 秒")
    logger.info(f"开始高负载任务 {name}，持续 {duration} 秒")

    # 创建一个大矩阵并进行计算密集型操作
    try:
        # 创建流
        stream_name = f"high_load_stream_{name}"
        print_log(f"创建高负载流 {stream_name}...")
        stream = cuda_manager.create_stream(stream_name)

        # 创建大矩阵
        print_log("创建大矩阵...")
        size = 5000
        with torch.cuda.stream(stream):
            a = torch.randn(size, size, device='cuda')
            b = torch.randn(size, size, device='cuda')

            # 执行计算密集型操作
            print_log("开始执行计算密集型操作...")
            start_time = time.time()
            while time.time() - start_time < duration:
                # 矩阵乘法
                _ = torch.matmul(a, b)  # 使用下划线表示我们不使用这个结果
                # 确保计算完成
                torch.cuda.synchronize()

                # 记录GPU利用率
                memory_info = cuda_manager.get_memory_info()
                if memory_info:
                    utilization = memory_info.utilization * 100
                    print_log(f"任务 {name} GPU内存利用率: {utilization:.1f}%")
                    logger.debug(f"任务 {name} GPU内存利用率: {utilization:.1f}%")
                else:
                    print_log(f"任务 {name} 无法获取GPU内存信息")
                    logger.debug(f"任务 {name} 无法获取GPU内存信息")

                # 短暂休息，避免过度打印日志
                time.sleep(0.5)

        # 释放流
        print_log(f"释放高负载流 {stream_name}...")
        cuda_manager.release_stream(stream_name)
        print_log(f"高负载任务 {name} 完成")
        logger.info(f"高负载任务 {name} 完成")

    except Exception as e:
        print_log(f"高负载任务 {name} 异常: {e!s}")
        logger.error(f"高负载任务 {name} 异常: {e!s}")

if __name__ == "__main__":
    test_adaptive_stream_manager()
