"""main.py的集成测试

测试项目主入口程序的功能和集成流程
"""

import shutil
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest
import yaml

# 测试用的临时目录
TEST_DIR = Path(tempfile.mkdtemp())

# 测试用的配置文件内容(仿照真实config.yaml结构)
TEST_CONFIG = {
    'version': '1.0.0',
    'system': {
        'device': 'cuda',
        'memory': {
            'memory_limit': 0.8,
            'monitoring_interval': 60
        },
        'cuda': {
            'memory_fraction': 0.8
        },
        'cache': {
            'enable_memory_cache': True,
            'size': 1000
        }
    },
    'paths': {
        'data_dir': str(TEST_DIR),
        'raw_data': str(TEST_DIR / 'test_data.csv'),
        'model_dir': str(TEST_DIR / 'models'),
        'results_dir': str(TEST_DIR / 'results'),
        'logs_dir': str(TEST_DIR / 'logs')
    },
    'data': {
        'data_path': str(TEST_DIR / 'test_data.csv'),
        'target': 'value15',
        'window_size': 16,
        'stride': 8,
        'train_ratio': 0.7,
        'val_ratio': 0.15,
        'test_ratio': 0.15,
        'preprocessing': {
            'normalization_method': 'standardize',
            'padding_mode': 'zeros'
        }
    },
    'training': {
        'batch_size': 32,
        'num_workers': 0,
        'num_epochs': 10,
        'optimizer': {
            'type': 'adam',
            'learning_rate': 0.001
        }
    },
    'logging': {
        'default_level': 'INFO',
        'module_levels': {
            'src.data.data_pipeline': 'INFO'
        }
    }
}

@pytest.fixture(scope="module")
def test_config_file():
    """创建测试用的配置文件和测试数据"""
    # 创建测试数据文件
    data_path = TEST_DIR / "test_data.csv"
    num_rows = 1000
    num_cols = 20

    # 生成随机测试数据
    import numpy as np
    data = np.random.rand(num_rows, num_cols)
    columns = ['date'] + [f"value{i}" for i in range(1, num_cols+1)]

    # 添加日期列和目标列
    df = pd.DataFrame(data, columns=columns[1:])  # 先创建数值列
    df.insert(0, 'date', pd.date_range('2020-01-01', periods=num_rows))  # 添加日期列
    target_col = "value15"
    df[target_col] = df[target_col] * 2  # 使目标列与其他列有相关性

    # 保存测试数据
    df.to_csv(data_path, index=False)

    # 更新配置中的data_path
    config = TEST_CONFIG.copy()
    config['data']['data_path'] = str(data_path)
    config['data']['target'] = target_col

    # 创建配置文件
    config_path = TEST_DIR / "config_test.yaml"
    with open(config_path, 'w') as f:
        yaml.dump(config, f)
    return config_path

@pytest.fixture
def mock_data_pipeline():
    """模拟DataPipeline"""
    with patch('src.data.data_pipeline.DataPipeline') as mock:
        mock_instance = mock.return_value
        mock_instance.run_pipeline.return_value = {
            'features': MagicMock(shape=(100, 16, 19)),
            'targets': MagicMock(shape=(100, 16, 1))
        }
        yield mock

@pytest.fixture
def mock_gan_model(test_config_file):
    """模拟GANModel"""
    # 创建模型目录和测试模型文件
    model_dir = Path(test_config_file).parent / "models"
    model_dir.mkdir(exist_ok=True)
    model_path = model_dir / "generator.pth"
    model_path.touch()  # 创建空模型文件

    with patch('src.models.gan.gan_model.GANModel') as mock:
        mock_instance = mock.return_value
        mock_instance.feature_dim = 19
        mock_instance.window_size = 16
        mock_instance.save_model.return_value = str(model_path)
        yield mock

@pytest.fixture
def mock_gan_trainer():
    """模拟GANTrainer"""
    with patch('src.models.gan.trainer.GANTrainer') as mock:
        mock_instance = mock.return_value
        mock_instance.fit.return_value = {'loss': [0.1, 0.05]}
        yield mock

@pytest.fixture
def mock_gan_evaluator():
    """模拟GANEvaluator"""
    with patch('src.models.gan.gan_evaluator.GANEvaluator') as mock:
        mock_instance = mock.return_value
        mock_instance.evaluate.return_value = {'mse': 0.1, 'mae': 0.2}
        yield mock

@pytest.fixture
def mock_predictor():
    """模拟Predictor"""
    with patch('src.predict.Predictor') as mock:
        mock_instance = mock.return_value
        mock_instance.predict.return_value = MagicMock(shape=(10, 16, 1))
        yield mock

def test_pipeline_runner_init(test_config_file):
    """测试PipelineRunner初始化"""
    from main import PipelineRunner

    runner = PipelineRunner(str(test_config_file))
    assert runner.config.version == '1.0.0'
    assert runner.device.type in {'cuda', 'cpu'}

def test_pipeline_runner_train(test_config_file, mock_data_pipeline, mock_gan_model, mock_gan_trainer):
    """测试训练流程"""
    from main import PipelineRunner

    runner = PipelineRunner(str(test_config_file))
    result = runner.train()

    assert 'history' in result
    assert 'model' in result
    assert result['feature_dim'] == 19
    assert result['window_size'] == 16

def test_pipeline_runner_evaluate(test_config_file, mock_data_pipeline, mock_gan_model, mock_gan_evaluator):
    """测试评估流程"""
    from main import PipelineRunner

    # 模拟资源监控
    with patch('src.utils.resource_manager.ResourceManager._monitor_resources') as mock_monitor:
        mock_monitor.return_value = {'memory_usage': 0.75}  # 模拟正常内存使用

        runner = PipelineRunner(str(test_config_file))
        # 创建模型路径
        model_path = Path(test_config_file).parent / "models" / "generator.pth"
        metrics = runner.evaluate(model_path=str(model_path))

        assert 'mse' in metrics
        assert 'mae' in metrics

def test_pipeline_runner_predict(test_config_file, mock_data_pipeline, mock_gan_model, mock_predictor):
    """测试预测流程"""
    from main import PipelineRunner

    # 模拟资源监控
    with patch('src.utils.resource_manager.ResourceManager._monitor_resources') as mock_monitor:
        mock_monitor.return_value = {'memory_usage': 0.75}  # 模拟正常内存使用

        runner = PipelineRunner(str(test_config_file))
        # 创建模型路径和输出路径
        model_path = Path(test_config_file).parent / "models" / "generator.pth"
        output_path = Path(test_config_file).parent / "results" / "predictions"
        result = runner.predict(input_path="test_input.csv", model_path=str(model_path), output_path=str(output_path))

        assert 'predictions' in result
        assert 'output_path' in result

def test_pipeline_runner_process_data(test_config_file, mock_data_pipeline):
    """测试数据处理流程"""
    from main import PipelineRunner

    # 模拟资源监控
    with patch('src.utils.resource_manager.ResourceManager._monitor_resources') as mock_monitor:
        mock_monitor.return_value = {'memory_usage': 0.75}  # 模拟正常内存使用

        runner = PipelineRunner(str(test_config_file))
        # 创建输出目录
        output_dir = Path(test_config_file).parent / "results" / "processed_data"
        result = runner.process_data(save_processed=True, output_dir=str(output_dir))

        assert 'original_shape' in result
        assert 'features_shape' in result
        assert result['features_shape'] == (100, 16, 19)

def test_main_train_mode(test_config_file, mock_data_pipeline, mock_gan_model, mock_gan_trainer):
    """测试main函数训练模式"""
    from main import main

    # 模拟资源监控
    with patch('src.utils.resource_manager.ResourceManager._monitor_resources') as mock_monitor:
        mock_monitor.return_value = {'memory_usage': 0.75}  # 模拟正常内存使用

        with patch('main.parse_args') as mock_args:
            mock_args.return_value = MagicMock(
                mode='train',
                config=str(test_config_file),
                resume=False,
                checkpoint=None
            )
            ret = main()
            assert ret == 0

def test_main_predict_mode(test_config_file, mock_data_pipeline, mock_gan_model, mock_predictor):
    """测试main函数预测模式"""
    from main import main

    # 模拟资源监控
    with patch('src.utils.resource_manager.ResourceManager._monitor_resources') as mock_monitor:
        mock_monitor.return_value = {'memory_usage': 0.75}  # 模拟正常内存使用

        with patch('main.parse_args') as mock_args:
            mock_args.return_value = MagicMock(
                mode='predict',
                config=str(test_config_file),
                input="test_input.csv",
                output=None,
                model=None
            )
            ret = main()
            assert ret == 0

def test_main_evaluate_mode(test_config_file, mock_data_pipeline, mock_gan_model, mock_gan_evaluator):
    """测试main函数评估模式"""
    from main import main

    # 模拟资源监控
    with patch('src.utils.resource_manager.ResourceManager._monitor_resources') as mock_monitor:
        mock_monitor.return_value = {'memory_usage': 0.75}  # 模拟正常内存使用

        with patch('main.parse_args') as mock_args:
            mock_args.return_value = MagicMock(
                mode='evaluate',
                config=str(test_config_file),
                model=None
            )
            ret = main()
            assert ret == 0

def test_main_process_mode(test_config_file, mock_data_pipeline):
    """测试main函数数据处理模式"""
    from main import main

    # 模拟资源监控
    with patch('src.utils.resource_manager.ResourceManager._monitor_resources') as mock_monitor:
        mock_monitor.return_value = {'memory_usage': 0.75}  # 模拟正常内存使用

        with patch('main.parse_args') as mock_args:
            mock_args.return_value = MagicMock(
                mode='process',
                config=str(test_config_file),
                save_processed=True,
                output_dir=None
            )
            ret = main()
            assert ret == 0

def test_main_error_handling(test_config_file):
    """测试main函数错误处理"""
    from main import main

    with patch('main.PipelineRunner') as mock_runner:
        mock_runner.side_effect = Exception("Test error")
        with patch('main.parse_args') as mock_args:
            mock_args.return_value = MagicMock(
                mode='train',
                config=str(test_config_file)
            )
            ret = main()
            assert ret == 1

def test_required_arguments(test_config_file):
    """测试必需参数检查"""
    from main import parse_args

    # 测试缺少--mode参数
    with patch('sys.argv', ['main.py', '--config', str(test_config_file)]), pytest.raises(SystemExit):
        parse_args()

    # 测试缺少--config参数
    with patch('sys.argv', ['main.py', '--mode', 'train']), pytest.raises(SystemExit):
        parse_args()

    # 测试预测模式缺少--input参数
    with patch('sys.argv', ['main.py', '--mode', 'predict', '--config', str(test_config_file)]), pytest.raises(SystemExit):
        parse_args()

    # 测试数据处理模式缺少--output_dir参数
    with patch('sys.argv', ['main.py', '--mode', 'process', '--config', str(test_config_file), '--save_processed']), pytest.raises(SystemExit):
        parse_args()

def cleanup_test_dir():
    """清理测试目录"""
    if TEST_DIR.exists():
        shutil.rmtree(TEST_DIR)

# 注册清理函数
pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    request.addfinalizer(cleanup_test_dir)
