"""模拟判别器模块 - 用于测试

这个模块提供了一个简化的判别器实现，用于测试目的。
它模拟了实际判别器的接口，但内部实现被简化。
"""

from unittest.mock import MagicMock

import torch
from torch import nn


class MockTimeSeriesDiscriminator(nn.Module):
    """模拟时序判别器类"""

    def __init__(self, target_dim, condition_feature_dim, hidden_dim, config):
        """初始化模拟判别器

        Args:
            target_dim: 目标维度
            condition_feature_dim: 条件特征维度
            hidden_dim: 隐藏维度
            config: 配置对象
        """
        super().__init__()
        self.target_dim = target_dim
        self.condition_feature_dim = condition_feature_dim
        self.hidden_dim = hidden_dim
        self._config = config

        # 计算总输入维度 (用于日志或调试)
        self.total_input_dim = target_dim + condition_feature_dim

        # 创建模拟分支
        self.trend_branch = MagicMock()
        self.trend_branch.return_value = torch.rand(32, 1)

        self.feature_branch = MagicMock()
        self.feature_branch.return_value = torch.rand(32, 1)

        self.temporal_branch = MagicMock()
        self.temporal_branch.return_value = torch.rand(32, 1)

        # 创建权重网络
        self.weight_net = MagicMock()
        self.weight_net.return_value = torch.softmax(torch.rand(32, 3), dim=1)

    def forward(self, target_sequence, condition_features):
        """前向传播

        Args:
            target_sequence: 目标序列 [batch_size, seq_len, target_dim]
            condition_features: 条件特征 [batch_size, seq_len, condition_feature_dim]

        Returns:
            torch.Tensor: 判别概率 [batch_size, 1]
        """
        batch_size = target_sequence.shape[0]

        # 模拟分支输出
        batch_size = target_sequence.shape[0]

        # 创建固定形状的输出
        trend_score = torch.rand(batch_size, 1, requires_grad=True, device=target_sequence.device)
        feature_score = torch.rand(batch_size, 1, requires_grad=True, device=target_sequence.device)
        temporal_score = torch.rand(batch_size, 1, requires_grad=True, device=target_sequence.device)

        # 拼接各维度得分
        combined_scores = torch.cat([
            trend_score,
            feature_score,
            temporal_score
        ], dim=1)  # [batch_size, 3]

        # 动态权重融合
        weights = torch.softmax(torch.rand(batch_size, 3, requires_grad=True, device=target_sequence.device), dim=1)

        # 加权平均
        final_score = (weights * combined_scores).sum(dim=1, keepdim=True)
        return torch.sigmoid(final_score)  # 转换为概率
