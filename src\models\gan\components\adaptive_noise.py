"""自适应噪声注入模块 - 提供条件噪声生成和分层噪声注入
模块路径: src/models/gan/components/adaptive_noise.py

优化特性:
- 条件噪声生成：根据输入特征统计特性生成自适应噪声
- 分层噪声注入：在不同生成阶段注入不同类型的噪声
- 预测不确定性建模：将噪声与预测不确定性关联
"""


import torch
from torch import nn

from src.models.base.base_module import BaseModule


class ConditionalNoiseGenerator(BaseModule):
    """条件噪声生成器 - 根据输入特征统计特性生成自适应噪声"""

    def __init__(self, feature_dim: int, noise_dim: int, condition_dim: int | None = None):
        super().__init__("ConditionalNoiseGenerator")
        self.feature_dim = feature_dim
        self.noise_dim = noise_dim
        self.condition_dim = condition_dim or feature_dim

        # 特征统计分析网络
        self.stats_analyzer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, self.condition_dim),
            nn.Tanh()
        )

        # 噪声参数生成网络
        # 生成噪声的均值和标准差参数
        self.noise_param_net = nn.Sequential(
            nn.Linear(self.condition_dim, noise_dim * 2),  # 均值和标准差
            nn.ReLU(),
            nn.Linear(noise_dim * 2, noise_dim * 2)
        )

        # 噪声相关性矩阵生成网络 - 生成下三角矩阵参数
        # 下三角矩阵元素数量：n*(n+1)/2
        n_tril_elements = noise_dim * (noise_dim + 1) // 2
        self.correlation_net = nn.Sequential(
            nn.Linear(self.condition_dim, n_tril_elements),
            nn.Tanh()
        )

    def forward(self, features: torch.Tensor, base_noise: torch.Tensor | None = None) -> torch.Tensor:
        """
        Args:
            features: [batch_size, seq_len, feature_dim] - 条件特征
            base_noise: [batch_size, seq_len, noise_dim] - 可选的基础噪声
        Returns:
            conditional_noise: [batch_size, seq_len, noise_dim]
        """
        batch_size, seq_len, _ = features.shape

        # 分析特征统计特性
        feature_stats = self.stats_analyzer(features)  # [batch, seq_len, condition_dim]

        # 生成噪声参数
        noise_params = self.noise_param_net(feature_stats)  # [batch, seq_len, noise_dim * 2]

        # 分离均值和标准差
        noise_mean = noise_params[..., :self.noise_dim]  # [batch, seq_len, noise_dim]
        noise_std = torch.sigmoid(noise_params[..., self.noise_dim:]) + 1e-6  # [batch, seq_len, noise_dim]

        # 生成基础随机噪声（如果未提供）
        if base_noise is None:
            base_noise = torch.randn(batch_size, seq_len, self.noise_dim, device=features.device)

        # 生成相关性矩阵 - 使用更稳健的方法确保正定性
        correlation_params = self.correlation_net(feature_stats)  # [batch, seq_len, n_tril_elements]

        # 重塑为下三角矩阵参数，确保正定性
        # 使用Cholesky参数化：L * L^T = Σ，其中L是下三角矩阵
        tril_indices = torch.tril_indices(self.noise_dim, self.noise_dim, device=features.device)

        # 构造下三角矩阵L
        l_matrix = torch.zeros(batch_size, seq_len, self.noise_dim, self.noise_dim, device=features.device)
        l_matrix[..., tril_indices[0], tril_indices[1]] = correlation_params

        # 确保对角线元素为正（使用softplus确保正值）
        diag_indices = torch.arange(self.noise_dim, device=features.device)
        l_matrix[..., diag_indices, diag_indices] = torch.nn.functional.softplus(l_matrix[..., diag_indices, diag_indices]) + 1e-6

        # 现在l_matrix是下三角矩阵且对角线为正，l_matrix * l_matrix^T 必然正定
        # 无需Cholesky分解，l_matrix本身就是我们需要的分解结果

        # 应用相关性变换
        correlated_noise = torch.matmul(l_matrix, base_noise.unsqueeze(-1)).squeeze(-1)

        # 应用条件均值和标准差
        conditional_noise = noise_mean + noise_std * correlated_noise

        return conditional_noise


class LayeredNoiseInjector(BaseModule):
    """分层噪声注入器 - 在不同生成阶段注入不同类型的噪声"""

    def __init__(self, feature_dim: int, noise_levels: list[str] | None = None):
        super().__init__("LayeredNoiseInjector")
        self.feature_dim = feature_dim
        self.noise_levels = noise_levels or ['feature', 'temporal', 'output']

        # 为每个噪声层级创建注入网络
        self.noise_injectors = nn.ModuleDict()

        for level in self.noise_levels:
            self.noise_injectors[level] = nn.Sequential(
                nn.Linear(feature_dim * 2, feature_dim),  # 特征 + 噪声
                nn.ReLU(),
                nn.Linear(feature_dim, feature_dim),
                nn.Tanh()
            )

        # 噪声强度控制网络
        self.intensity_controller = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, len(self.noise_levels)),
            nn.Sigmoid()  # 输出每个层级的噪声强度
        )

    def inject_noise(self, features: torch.Tensor, noise: torch.Tensor, level: str) -> torch.Tensor:
        """
        在指定层级注入噪声

        Args:
            features: [batch_size, seq_len, feature_dim]
            noise: [batch_size, seq_len, feature_dim]
            level: 噪声层级 ('feature', 'temporal', 'output')
        Returns:
            noisy_features: [batch_size, seq_len, feature_dim]
        """
        if level not in self.noise_levels:
            return features

        # 计算噪声强度
        intensities = self.intensity_controller(features)  # [batch, seq_len, num_levels]
        level_idx = self.noise_levels.index(level)
        intensity = intensities[..., level_idx:level_idx+1]  # [batch, seq_len, 1]

        # 调整噪声强度
        scaled_noise = noise * intensity

        # 拼接特征和噪声
        combined = torch.cat([features, scaled_noise], dim=-1)

        # 应用噪声注入网络
        noisy_features = self.noise_injectors[level](combined)

        return noisy_features

    def forward(self, features: torch.Tensor, noise_dict: dict[str, torch.Tensor]) -> torch.Tensor:
        """
        应用多层噪声注入

        Args:
            features: [batch_size, seq_len, feature_dim]
            noise_dict: 包含不同层级噪声的字典
        Returns:
            enhanced_features: [batch_size, seq_len, feature_dim]
        """
        current_features = features

        for level in self.noise_levels:
            if level in noise_dict:
                current_features = self.inject_noise(current_features, noise_dict[level], level)

        return current_features


class UncertaintyAwareNoise(BaseModule):
    """不确定性感知噪声 - 将噪声与预测不确定性关联"""

    def __init__(self, feature_dim: int, noise_dim: int):
        super().__init__("UncertaintyAwareNoise")
        self.feature_dim = feature_dim
        self.noise_dim = noise_dim

        # 不确定性估计网络
        self.uncertainty_estimator = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 1),
            nn.Sigmoid()  # 输出0-1的不确定性分数
        )

        # 噪声调制网络
        self.noise_modulator = nn.Sequential(
            nn.Linear(1, noise_dim),
            nn.ReLU(),
            nn.Linear(noise_dim, noise_dim),
            nn.Softplus()  # 确保正值
        )

        # 置信区间生成网络
        self.confidence_net = nn.Sequential(
            nn.Linear(feature_dim + 1, feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, 2),  # 上下界
            nn.Tanh()
        )

    def forward(self, features: torch.Tensor, base_noise: torch.Tensor | None = None) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Args:
            features: [batch_size, seq_len, feature_dim]
            base_noise: [batch_size, seq_len, noise_dim] - 可选的基础噪声
        Returns:
            uncertainty_noise: [batch_size, seq_len, noise_dim]
            uncertainty_scores: [batch_size, seq_len, 1]
            confidence_intervals: [batch_size, seq_len, 2] - 置信区间上下界
        """
        batch_size, seq_len, _ = features.shape

        # 估计预测不确定性
        uncertainty_scores = self.uncertainty_estimator(features)  # [batch, seq_len, 1]

        # 生成基础噪声（如果未提供）
        if base_noise is None:
            base_noise = torch.randn(batch_size, seq_len, self.noise_dim, device=features.device)

        # 根据不确定性调制噪声
        noise_scale = self.noise_modulator(uncertainty_scores)  # [batch, seq_len, noise_dim]
        uncertainty_noise = base_noise * noise_scale

        # 生成置信区间
        uncertainty_input = torch.cat([features, uncertainty_scores], dim=-1)
        confidence_intervals = self.confidence_net(uncertainty_input)  # [batch, seq_len, 2]

        return uncertainty_noise, uncertainty_scores, confidence_intervals


class AdaptiveNoiseModule(BaseModule):
    """自适应噪声模块 - 整合所有噪声优化组件"""

    def __init__(self, feature_dim: int, noise_dim: int, noise_levels: list[str] | None = None):
        super().__init__("AdaptiveNoiseModule")
        self.feature_dim = feature_dim
        self.noise_dim = noise_dim
        self.noise_levels = noise_levels or ['feature', 'temporal', 'output']

        # 条件噪声生成器
        self.conditional_generator = ConditionalNoiseGenerator(feature_dim, noise_dim)

        # 分层噪声注入器
        self.layered_injector = LayeredNoiseInjector(feature_dim, noise_levels)

        # 不确定性感知噪声
        self.uncertainty_noise = UncertaintyAwareNoise(feature_dim, noise_dim)

    def generate_adaptive_noise(self, features: torch.Tensor, base_noise: torch.Tensor | None = None) -> dict[str, torch.Tensor]:
        """
        生成自适应噪声

        Args:
            features: [batch_size, seq_len, feature_dim]
            base_noise: [batch_size, seq_len, noise_dim] - 可选的基础噪声
        Returns:
            noise_dict: 包含不同类型噪声的字典
        """
        # 生成条件噪声
        conditional_noise = self.conditional_generator(features, base_noise)

        # 生成不确定性感知噪声
        uncertainty_noise, uncertainty_scores, confidence_intervals = self.uncertainty_noise(features, base_noise)

        # 为不同层级生成噪声
        noise_dict = {}
        for level in self.noise_levels:
            # 混合条件噪声和不确定性噪声
            level_noise = 0.7 * conditional_noise + 0.3 * uncertainty_noise

            # 调整噪声维度以匹配特征维度
            if level_noise.size(-1) != self.feature_dim:
                projection = nn.Linear(level_noise.size(-1), self.feature_dim).to(features.device)
                level_noise = projection(level_noise)

            noise_dict[level] = level_noise

        # 添加元信息
        noise_dict['uncertainty_scores'] = uncertainty_scores
        noise_dict['confidence_intervals'] = confidence_intervals

        return noise_dict

    def forward(self, features: torch.Tensor, base_noise: torch.Tensor | None = None) -> tuple[torch.Tensor, dict[str, torch.Tensor]]:
        """
        Args:
            features: [batch_size, seq_len, feature_dim]
            base_noise: [batch_size, seq_len, noise_dim] - 可选的基础噪声
        Returns:
            enhanced_features: [batch_size, seq_len, feature_dim]
            noise_info: 包含噪声信息的字典
        """
        # 生成自适应噪声
        noise_dict = self.generate_adaptive_noise(features, base_noise)

        # 应用分层噪声注入
        enhanced_features = self.layered_injector(features, noise_dict)

        return enhanced_features, noise_dict
