"""
自适应梯度惩罚权重配置工厂

提供从ConfigManager创建AdaptiveLambdaGPConfig的工厂函数。
"""
import logging
from logging import Logger
from typing import Any

from src.utils.adaptive_lambda_gp_config import AdaptiveLambdaGPConfig


def create_adaptive_lambda_gp_config(
    config_manager: Any,
    logger: Logger | None = None
) -> AdaptiveLambdaGPConfig:
    """
    从ConfigManager创建AdaptiveLambdaGPConfig

    Args:
        config_manager: 配置管理器实例
        logger: 日志记录器，如果为None则创建新的记录器

    Returns:
        AdaptiveLambdaGPConfig: 创建的配置对象
    """
    logger = logger or logging.getLogger("AdaptiveLambdaGPFactory")

    # 初始化空配置字典，所有参数必须从配置文件获取
    config_dict = {}

    # 严格从配置管理器获取自适应梯度惩罚权重配置，不提供回退
    try:
        # 必须存在training.adaptive_lambda_gp配置
        if not hasattr(config_manager, 'training'):
            raise ValueError("配置中缺少training部分")

        if not hasattr(config_manager.training, 'adaptive_lambda_gp'):
            raise ValueError("配置中缺少training.adaptive_lambda_gp部分")

        adaptive_config = config_manager.training.adaptive_lambda_gp

        # 如果是字典，直接更新
        if isinstance(adaptive_config, dict):
            config_dict.update(adaptive_config)
            logger.info("从配置字典加载自适应梯度惩罚权重配置")
        # 如果是对象，转换为字典
        elif hasattr(adaptive_config, '__dict__'):
            obj_dict = {k: v for k, v in adaptive_config.__dict__.items()
                       if not k.startswith('_')}
            config_dict.update(obj_dict)
            logger.info("从配置对象加载自适应梯度惩罚权重配置")
        else:
            raise ValueError(f"无法识别的adaptive_lambda_gp配置类型: {type(adaptive_config)}")

    except Exception as e:
        error_msg = f"从配置管理器创建自适应梯度惩罚权重配置时出错: {e!s}"
        logger.error(error_msg)
        raise ValueError(error_msg) from e

    # 创建配置对象
    config = AdaptiveLambdaGPConfig.from_dict(config_dict)

    # 记录配置信息
    logger.info(f"创建的自适应梯度惩罚权重配置: {config}")

    return config
