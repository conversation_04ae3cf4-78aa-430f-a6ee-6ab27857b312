"""动态特征融合模块 - 根据上下文动态调整特征权重"""

import torch
from torch import nn

# import torch.nn.functional as F  # 当前未使用
from src.models.base.base_module import BaseModule

# 已使用BaseModule中的logger，无需额外导入

class DynamicFeatureFusion(BaseModule):
    """
    根据上下文信息动态地对输入特征进行加权融合。

    使用一个门控网络 (gating network) 根据上下文向量为每个特征通道生成权重。
    """
    def __init__(self, feature_dim: int, context_dim: int, hidden_dim: int):
        """
        初始化动态特征融合模块。

        Args:
            feature_dim (int): 输入特征的维度。
            context_dim (int): 上下文向量的维度。
            hidden_dim (int): 门控网络中间层的隐藏维度。
                              必须显式指定，不能为None。

        Raises:
            ValueError: 如果任何维度参数小于等于0
        """
        super().__init__("DynamicFeatureFusion")

        # 验证参数
        if feature_dim <= 0:
            error_msg = f"feature_dim必须为正数，当前值: {feature_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if context_dim <= 0:
            error_msg = f"context_dim必须为正数，当前值: {context_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if hidden_dim <= 0:
            error_msg = f"hidden_dim必须为正数，当前值: {hidden_dim}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.feature_dim = feature_dim
        self.context_dim = context_dim
        self.hidden_dim = hidden_dim

        # 门控网络: 输入上下文，输出特征权重 (使用Sigmoid确保正权重)
        self.gating_network = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.LeakyReLU(0.2),
            nn.Linear(hidden_dim, feature_dim),
            nn.Sigmoid()  # 输出范围[0,1]确保正权重
        )

        # 初始化最后一层的偏置，使得初始融合权重均值接近0.5
        # 这样可以确保初始状态下特征被均衡融合
        with torch.no_grad():
            if isinstance(self.gating_network[-2], nn.Linear):
                nn.init.zeros_(self.gating_network[-2].bias)

        # 可选：对输入特征进行线性变换
        # self.feature_transform = nn.Linear(feature_dim, feature_dim)
        self.feature_transform = nn.Identity() # 默认不转换

        # 应用父类的初始化逻辑到所有子模块
        self.apply(self._init_weights)
        self.logger.info(f"DynamicFeatureFusion initialized: feature_dim={feature_dim}, context_dim={context_dim}, hidden_dim={hidden_dim}")

    # 保持与 BaseModule._init_weights 签名一致
    def _init_weights(self, module):
        """初始化模块权重 (遵循 BaseModule 逻辑)"""
        # 调用父类的初始化方法来处理不同类型的层
        super()._init_weights(module)
        # 可以选择性地在这里添加针对 DynamicFeatureFusion 特定层的额外初始化逻辑
        # (当前不需要)
        pass

    def forward(self, features: torch.Tensor, context: torch.Tensor) -> torch.Tensor:
        """
        执行动态特征融合。

        Args:
            features (torch.Tensor): 输入特征张量，形状为 [batch_size, seq_length, feature_dim]。
            context (torch.Tensor): 上下文信息张量，形状为 [batch_size, seq_length, context_dim]。

        Returns:
            torch.Tensor: 融合后的特征张量，形状为 [batch_size, seq_length, feature_dim]。
        """
        import time
        from datetime import datetime

        start_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 动态特征融合开始 - 特征形状: {features.shape}, 上下文形状: {context.shape}")

        # 验证输入维度
        validation_start = time.time()
        # 验证输入维度 (断言检查，防止错误扩散 - Rule 70)
        assert features.shape[-1] == self.feature_dim, \
            f"Input features dim ({features.shape[-1]}) != expected feature_dim ({self.feature_dim})"
        assert context.shape[-1] == self.context_dim, \
            f"Input context dim ({context.shape[-1]}) != expected context_dim ({self.context_dim})"
        assert features.shape[:2] == context.shape[:2], \
            f"Batch/Seq mismatch: features ({features.shape[:2]}) vs context ({context.shape[:2]})"
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 输入验证完成 - 耗时: {time.time() - validation_start:.2f}秒")

        # 1. 计算动态门控权重
        gating_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始计算门控权重")
        # context: [batch, seq_len, context_dim] -> gating_network -> weights: [batch, seq_len, feature_dim]
        fusion_weights = self.gating_network(context)
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 门控权重计算完成 - 形状: {fusion_weights.shape}, 耗时: {time.time() - gating_start:.2f}秒")

        # 2. (可选) 转换输入特征
        transform_start = time.time()
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始特征转换")
        transformed_features = self.feature_transform(features)
        self.logger.debug(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 特征转换完成 - 耗时: {time.time() - transform_start:.2f}秒")

        # 3. 应用权重进行融合 (逐元素相乘)
        fusion_start = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始特征融合")
        fused_features = transformed_features * fusion_weights
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 特征融合完成 - 形状: {fused_features.shape}, 耗时: {time.time() - fusion_start:.2f}秒")

        # 记录融合权重的统计信息
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 融合权重统计: min={fusion_weights.min().item():.3f}, max={fusion_weights.max().item():.3f}, mean={fusion_weights.mean().item():.3f}")

        # 检查内存使用情况
        if torch.cuda.is_available():
            mem_allocated = torch.cuda.memory_allocated() / (1024 ** 2)
            mem_reserved = torch.cuda.memory_reserved() / (1024 ** 2)
            self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] CUDA内存使用情况 - 已分配: {mem_allocated:.2f}MB, 已保留: {mem_reserved:.2f}MB")

        end_time = time.time()
        self.logger.info(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 动态特征融合完成 - 总耗时: {end_time - start_time:.2f}秒")

        return fused_features
