import time
from typing import Protocol, runtime_checkable

import numpy as np
import torch

from src.models.base.base_module import BaseModule


@runtime_checkable
class StandardizationProtocol(Protocol):
    """标准化协议"""

    @property
    def mean(self) -> torch.Tensor:
        """获取均值"""
        ...

    @property
    def std(self) -> torch.Tensor:
        """获取标准差"""
        ...

    def transform(self, data: torch.Tensor) -> torch.Tensor:
        """标准化数据

        Args:
            data: 输入数据

        Returns:
            torch.Tensor: 标准化后的数据
        """
        ...

    def inverse_transform(self, data: torch.Tensor) -> torch.Tensor:
        """逆标准化数据

        Args:
            data: 标准化后的数据

        Returns:
            torch.Tensor: 原始数据
        """
        ...

class Standardizer(BaseModule):
    """标准化处理器，封装标准化参数计算和特征转换逻辑"""

    def __init__(self):
        super().__init__()
        self.scaler = None

    @property
    def mean(self) -> torch.Tensor:
        """获取均值"""
        if self.scaler is None:
            raise RuntimeError("请先调用fit方法计算标准化参数")
        return self.scaler['mean']

    @property
    def std(self) -> torch.Tensor:
        """获取标准差"""
        if self.scaler is None:
            raise RuntimeError("请先调用fit方法计算标准化参数")
        return self.scaler['std']

    def fit(self, data: torch.Tensor | np.ndarray) -> None:
        """计算标准化参数"""
        start_time = time.time()
        if isinstance(data, np.ndarray):
            data = torch.from_numpy(data).float()
        if data.dim() not in (2, 3):
            raise ValueError(f"输入特征维度应为2D或3D，实际维度：{data.dim()}")

        # 计算每个特征的均值和标准差
        if data.dim() == 2:  # [N, features]
            # 对每个特征单独计算统计量
            mean = torch.zeros(data.shape[1])
            std = torch.ones(data.shape[1])
            for i in range(data.shape[1]):
                feature = data[:, i]
                mean[i] = feature.mean()
                std[i] = feature.std(unbiased=True)

                # 处理特殊特征
                if std[i] < 1e-6:  # 标准差接近0
                    if torch.allclose(feature, torch.zeros_like(feature)):
                        # 全0特征保持原样
                        mean[i] = 0.0
                        std[i] = 1.0
                    else:
                        # 非常量特征但标准差为0，重置为1.0并中心化
                        std[i] = 1.0
                        mean[i] = 0.0

                # 移除自动处理极端值的逻辑，仅记录警告
                if abs(mean[i]) > 1e6 or std[i] > 1e6: # 使用更宽松的阈值仅用于警告
                    self.logger.warning(
                        f"特征 #{i} 可能包含极端值或尺度差异过大:\n"
                        f"- 均值: {mean[i]:.2e}\n"
                        f"- 标准差: {std[i]:.2e}\n"
                        f"- 前5个值: {feature[:5].tolist()}\n"
                        f"注意：不再自动应用对数变换或稳健缩放，请在数据清洗阶段处理极端值。"
                    )
                # 确保标准差不为零 (保留基础处理)
                std[i] = torch.max(std[i], torch.tensor(1e-6))

            mean = mean.reshape(1, -1)  # 转换为[1, features]形状
            std = std.reshape(1, -1) # [1, features]
        else:  # [batch, seq, features]
            # 展平batch和sequence维度，对每个特征单独计算
            flat_data = data.reshape(-1, data.shape[-1])
            mean = flat_data.mean(dim=0, keepdim=True) # [1, features]
            std = flat_data.std(dim=0, unbiased=True, keepdim=True) # [1, features]
            # 确保标准差不为零
            std = torch.where(std < 1e-6, torch.ones_like(std), std)
            # 扩展为 [1, 1, features] 以匹配 3D 数据
            mean = mean.unsqueeze(1)
            std = std.unsqueeze(1)

        # 验证统计量
        if torch.any(torch.isnan(mean)) or torch.any(torch.isnan(std)):
            raise ValueError("统计量计算出现NaN值")
        if torch.any(std <= 0):
             raise ValueError("统计量计算出现非正标准差")

        self.scaler = {
            'mean': mean,
            'std': std
        }
        self.logger.info(
            f"\n=== 标准化参数计算 ===\n"
            f"- 输入形状: {data.shape}\n"
            f"- 均值形状: {self.scaler['mean'].shape}\n"
            f"- 标准差形状: {self.scaler['std'].shape}\n"
            f"- 计算耗时: {time.time()-start_time:.3f}s"
        )

    def transform(self, data: torch.Tensor | np.ndarray, already_normalized: bool = False) -> torch.Tensor:
        """应用标准化转换，确保输出满足均值为0、标准差为1的要求

        Args:
            data: 输入数据
            already_normalized: 数据是否已经标准化过

        Returns:
            标准化后的数据
        """
        if self.scaler is None:
            raise RuntimeError("请先调用fit方法计算标准化参数")

        if isinstance(data, np.ndarray):
            data = torch.from_numpy(data).float()

        # 不再支持跳过标准化处理
        if already_normalized:
            error_msg = "不再支持跳过标准化处理，请确保输入数据未经标准化"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 确保mean和std与数据维度匹配
        mean = self.scaler['mean']
        std = self.scaler['std']

        # 如果数据是3D而统计量是2D，扩展统计量维度
        if data.dim() == 3 and mean.dim() == 2:
            mean = mean.unsqueeze(1)  # 添加序列维度
            std = std.unsqueeze(1)

        # 添加数值稳定性检查，确保标准差不为零
        std = torch.where(std < 1e-6, torch.ones_like(std), std)

        # 标准化转换
        transformed = (data - mean) / std

        # 检查数值稳定性
        if torch.any(torch.isnan(transformed)) or torch.any(torch.isinf(transformed)):
            # 记录详细信息以帮助调试
            nan_count = torch.isnan(transformed).sum().item()
            inf_count = torch.isinf(transformed).sum().item()
            self.logger.error(
                f"标准化转换产生了无效值:\n"
                f"- NaN数量: {nan_count}\n"
                f"- Inf数量: {inf_count}\n"
                f"- 输入数据形状: {data.shape}\n"
                f"- 均值形状: {mean.shape}\n"
                f"- 标准差形状: {std.shape}\n"
                f"- 最小标准差: {std.min().item()}"
            )
            raise ValueError("标准化转换产生了无效值(NaN/Inf)")

        return transformed

    def inverse_transform(self, data: torch.Tensor, is_normalized: bool = True) -> torch.Tensor:
        """逆向标准化转换

        Args:
            data: 输入数据
            is_normalized: 数据是否已经标准化过，默认为True

        Returns:
            反标准化后的数据
        """
        if self.scaler is None:
            raise RuntimeError("请先调用fit方法计算标准化参数")

        # 不再支持跳过反标准化处理
        if not is_normalized:
            error_msg = "不再支持跳过反标准化处理，请确保输入数据已经标准化"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 获取目标设备
        target_device = data.device
        # 将 scaler 的均值和标准差移动到目标设备
        mean = self.scaler['mean'].to(target_device)
        std = self.scaler['std'].to(target_device)

        # 如果数据是3D而统计量是2D，扩展统计量维度
        if data.dim() == 3 and mean.dim() == 2:
            mean = mean.unsqueeze(1)  # 添加序列维度
            std = std.unsqueeze(1)

        # 执行反标准化计算
        return data * std + mean
