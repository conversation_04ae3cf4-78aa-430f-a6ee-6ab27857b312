"""窗口时序数据集模块 - 提供时间序列窗口处理功能

本模块实现滑动窗口处理功能，专注窗口切分和访问。

依赖模块：
- src/data/protocol.py: 数据集协议定义和基础时序数据集实现
- src/utils/logger.py: 日志系统
"""


import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset as TorchDataset

from src.data.protocol import TimeSeriesDataset, WindowDataset
from src.data.standardization import StandardizationProtocol
from src.utils.logger import get_logger


class TimeSeriesWindowDataset(TimeSeriesDataset, WindowDataset, TorchDataset):
    """时序窗口数据集 - 提供滑动窗口处理功能

    专注窗口切分和访问，不包含数据处理逻辑。
    """

    def __init__(
        self,
        config,
        split: str = 'train',
        data: pd.DataFrame | np.ndarray | torch.Tensor | None = None,
        window_size: int | None = None,
        target_column: str | None = None,
        feature_columns: list[str] | None = None,
        stride: int | None = None,
        device: torch.device | None = None,
        target_standardizer: StandardizationProtocol | None = None,
        target_scale_factor: float = 1.0
    ):
        """初始化时序窗口数据集

        Args:
            config: 配置对象
            split: 数据分割，可选值：train, val, test
            data: 输入数据，如果为None则从配置中加载
            window_size: 窗口大小，如果为None则从配置中加载
            target_column: 目标列名，如果为None则从配置中加载
            feature_columns: 特征列名列表，如果为None则从配置中加载
            stride: 滑动步长，如果为None则从配置中加载
            standardize: 是否标准化数据，如果为None则从配置中加载
            device: 设备
        """
        super().__init__()
        self.logger = get_logger(__name__)
        self.config = config
        self.split = split
        self.windows = []

        # 从配置中获取参数
        # 处理配置对象的不同类型
        if isinstance(config, dict):
            config_data = config.get('data', {})
            has_window_size = 'window_size' in config_data
            has_stride = 'stride' in config_data
            self._window_size_from_config = lambda: config_data['window_size']
            self._stride_from_config = lambda: config_data['stride']
            self._target_from_config = lambda: config_data.get('target', None)
            self._columns_from_config = lambda: config_data.get('columns', {}).get('numeric') if isinstance(config_data.get('columns'), dict) else None
        else:
            has_window_size = hasattr(config, 'data') and hasattr(config.data, 'window_size')
            has_stride = hasattr(config, 'data') and hasattr(config.data, 'stride')
            self._window_size_from_config = lambda: config.data.window_size
            self._stride_from_config = lambda: config.data.stride
            self._target_from_config = lambda: config.data.target if hasattr(config.data, 'target') else None
            self._columns_from_config = lambda: config.data.columns.get('numeric') if hasattr(config.data, 'columns') and isinstance(config.data.columns, dict) else None

        # 设置窗口大小
        if window_size is not None:
            self.window_size = window_size
        elif has_window_size:
            self.window_size = self._window_size_from_config()
        else:
            # 强制要求配置中存在window_size
            error_msg = "配置中缺少window_size参数"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 设置滑动步长
        if stride is not None:
            self.stride = stride
        elif has_stride:
            self.stride = self._stride_from_config()
        else:
            # 强制要求配置中存在stride
            error_msg = "配置中缺少stride参数"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 设置目标列和特征列
        if target_column is not None:
            self.target_column = target_column
        else:
            target = self._target_from_config()
            if target is None:
                error_msg = "配置中缺少target参数"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            self.target_column = target

        if feature_columns is not None:
            self.feature_columns = feature_columns
        else:
            columns = self._columns_from_config()
            if columns is None:
                error_msg = "配置中缺少feature_columns参数"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            self.feature_columns = columns

        self.device = device if device is not None else torch.device('cpu')

        # 存储标准化相关参数（暂时未使用，但保留接口兼容性）
        self._target_standardizer = target_standardizer
        self._target_scale_factor = target_scale_factor
        if target_standardizer is not None:
            self.logger.debug(f"目标标准化器已设置: {type(target_standardizer).__name__}")
        if target_scale_factor != 1.0:
            self.logger.debug(f"目标缩放因子已设置: {target_scale_factor}")

        # 加载数据
        if data is None:
            data = self._load_data_from_config()

        # 处理数据
        self._process_data(data, target_column, feature_columns)

        # 创建窗口
        self._create_windows()

        self.logger.info(
            f"窗口数据集初始化完成:\n"
            f"- 分割: {self.split}\n"
            f"- 窗口大小: {self.window_size}\n"
            f"- 滑动步长: {self.stride}\n"
            f"- 窗口数量: {len(self.windows)}\n"
            f"- 特征维度: {self.feature_dim}"
        )

    def _load_data_from_config(self) -> pd.DataFrame:
        """从配置中加载数据

        Returns:
            pd.DataFrame: 加载的数据
        """
        try:
            # 获取数据路径
            data_path = self.config.data.data_path
            if not data_path:
                raise ValueError("数据路径为空")

            # 加载数据
            data = pd.read_csv(data_path)

            # 分割数据 - 强制要求配置中存在 train_ratio 和 val_ratio
            if not hasattr(self.config.data, 'train_ratio'):
                raise ValueError("配置中缺少 data.train_ratio")
            train_ratio = self.config.data.train_ratio
            if not hasattr(self.config.data, 'val_ratio'):
                raise ValueError("配置中缺少 data.val_ratio")
            val_ratio = self.config.data.val_ratio
            if not (0 < train_ratio < 1 and 0 < val_ratio < 1 and train_ratio + val_ratio < 1):
                 raise ValueError(f"train_ratio ({train_ratio}) 和 val_ratio ({val_ratio}) 必须在 (0, 1) 之间且总和小于 1")
            # 测试集比例由剩余部分确定

            # 计算分割索引
            n_samples = len(data)
            train_size = int(n_samples * train_ratio)
            val_size = int(n_samples * val_ratio)

            # 根据分割返回相应的数据
            if self.split == 'train':
                return data.iloc[:train_size]
            elif self.split == 'val':
                return data.iloc[train_size:train_size+val_size]
            elif self.split == 'test':
                return data.iloc[train_size+val_size:]
            else:
                raise ValueError(f"不支持的数据分割: {self.split}")

        except Exception as e:
            self.logger.error(f"加载数据失败: {e!s}")
            raise

    def _process_data(self, data: pd.DataFrame | np.ndarray | torch.Tensor, target_column: str | None = None, feature_columns: list[str] | None = None):
        """处理数据

        Args:
            data: 输入数据
            target_column: 目标列名
            feature_columns: 特征列名列表
        """
        try:
            # 处理目标列
            target_col = target_column or self.target_column

            # 处理特征列
            if feature_columns:
                self.feature_columns = feature_columns
            elif self.feature_columns is None:
                # 不再自动推断特征列，必须明确指定
                error_msg = "必须明确指定特征列，不再支持自动推断"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            # 提取特征和目标
            if isinstance(data, pd.DataFrame):
                # 从DataFrame提取
                features = data[self.feature_columns].values
                targets = data[target_col].values
            elif isinstance(data, np.ndarray):
                # 从NumPy数组提取
                features = data[:, :-1]  # 假设最后一列是目标
                targets = data[:, -1]
            elif isinstance(data, torch.Tensor):
                # 从PyTorch张量提取
                features = data[:, :-1]
                targets = data[:, -1]
            else:
                raise TypeError(f"不支持的数据类型: {type(data)}")

            # 转换为张量
            self._features = torch.tensor(features, dtype=torch.float32)
            self._targets = torch.tensor(targets, dtype=torch.float32).unsqueeze(-1)

            # 记录特征维度
            self.feature_dim = self._features.shape[1]

            self.logger.info(
                f"数据处理完成:\n"
                f"- 特征形状: {self._features.shape}\n"
                f"- 目标形状: {self._targets.shape}\n"
                f"- 特征列: {self.feature_columns}\n"
                f"- 目标列: {target_col}"
            )

        except Exception as e:
            self.logger.error(f"处理数据失败: {e!s}")
            raise

    def _create_windows(self):
        """创建滑动窗口"""
        try:
            if self._features is None or self._targets is None:
                raise ValueError("特征或目标数据未初始化")

            # 清空现有窗口
            self.windows = []

            # 获取数据长度
            n_samples = len(self._features)

            # 检查窗口创建可行性
            if n_samples < self.window_size:
                raise ValueError(
                    f"无法创建窗口 - 数据长度({n_samples})小于窗口大小({self.window_size}). "
                    f"需要至少{self.window_size}个样本"
                )

            # 创建窗口
            total_possible = (n_samples - self.window_size) // self.stride + 1
            self.logger.info(f"可创建窗口数: {total_possible} (数据长度={n_samples}, 窗口大小={self.window_size}, 步长={self.stride})")

            for i in range(0, n_samples - self.window_size + 1, self.stride):
                # 特征窗口
                feature_window = self._features[i:i+self.window_size]

                # 目标窗口 (保持与特征窗口相同长度)
                target_window = self._targets[i:i+self.window_size]

                # 添加到窗口列表
                self.windows.append((feature_window, target_window))

            self.logger.info(f"创建窗口完成: {len(self.windows)}个窗口")

        except Exception as e:
            self.logger.error(f"创建窗口失败: {e!s}")
            raise

    def __len__(self) -> int:
        """返回数据集长度"""
        return len(self.windows)

    def __getitem__(self, index: int) -> dict[str, torch.Tensor]:
        """获取指定索引的数据样本

        Args:
            index: 样本索引

        Returns:
            Dict[str, torch.Tensor]: 包含特征和目标的字典
        """
        if index >= len(self.windows):
            raise IndexError(f"索引{index}超出范围[0, {len(self.windows)-1}]")

        feature_window, target_window = self.windows[index]

        return {
            'features': feature_window.to(self.device),
            'target': target_window.to(self.device)
        }

    def get_all_data(self) -> dict[str, torch.Tensor]:
        """获取全部数据

        Returns:
            Dict[str, torch.Tensor]: 包含所有数据的字典
        """
        # 将所有窗口的特征和目标合并
        all_features = []
        all_targets = []

        for feature_window, target_window in self.windows:
            all_features.append(feature_window)
            if target_window is not None:
                all_targets.append(target_window)

        # 合并成单个张量
        result = {
            'features': torch.cat(all_features) if all_features else torch.tensor([])
        }

        if all_targets:
            result['targets'] = torch.cat(all_targets)

        return result

    @property
    def feature_dim(self) -> int:
        """获取特征维度"""
        if not hasattr(self, '_feature_dim'):
            if self.windows:
                self._feature_dim = self.windows[0][0].shape[1]
            else:
                self._feature_dim = 0
        return self._feature_dim

    @feature_dim.setter
    def feature_dim(self, value: int):
        """设置特征维度"""
        self._feature_dim = value

__all__ = ['TimeSeriesWindowDataset']
