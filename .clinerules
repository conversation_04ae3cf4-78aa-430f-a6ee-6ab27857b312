一、 架构与设计原则 (Architecture & Design Principles)

模块化与接口设计 (Modularity & Interface Design):

保持接口设计简单，将计算逻辑封装在类中，方便调用者使用。 (源规则 2)

功能按执行阶段归集于少数模块，便于维护管理。 (源规则 4)

架构层面优先关注模块接口协调性和数据流转合理性，避免局部优化导致架构失衡。 (源规则 12)

严格遵循SOLID原则，保持模块职责单一化，专注核心功能，禁止引入无关逻辑。 (源规则 12)

模块间应直接通信，禁止第三方中转。 (源规则 18)

控制逻辑应封装，使用策略模式替代布尔标志。 (源规则 18)

定期检查模块耦合度，通过类图和时序图维护架构可见性。 (源规则 18)

发现设计原则违反应立即重构，架构评审应先于功能开发。 (源规则 18)

强制实施绝对导入模块规范，避免使用相对导入。 (源规则 30)

遇到跨模块协调和对接问题时，应优先遵守架构设计，而非直接迎合上下游。 (源规则 32)

高内聚低耦合：禁止传递具体类或实现细节，通过抽象接口交互；禁止全局变量/单例共享状态，采用显式传参；最小化上下文传递；使用结构化数据；策略模式封装行为；DAO抽象资源访问。 (源规则 41)

对象的依赖项应通过构造函数传递，禁止内部实例化依赖。 (源规则 47)

分析问题时应跳出局部修复，分析架构设计是否符合文档要求。 (源规则 55)

代码质量与维护性 (Code Quality & Maintainability)

保持编程风格一致，优先面向对象，使用规范易读的设计。 (源规则 3)

禁止重复实现；新增代码前搜索确认；发现重复代码提取为公共模块。 (源规则 13)

代码质量优先级：业务逻辑正确性 > 配置文件内容。 (源规则 13)

移除非数据处理相关的过度设计代码。 (源规则 13, 22)

修改代码时保持入口程序简单性。 (源规则 13)

修改前检查依赖链（导入、调用、命名）。 (源规则 13)

代码编写风格应高效简洁，避免兼容、妥协设计掩盖问题和增加复杂度，但允许必要检查。 (源规则 34)

关注模块功能正确性和接口协作，避免在非核心功能上过度投入。 (源规则 37)

在保证代码风格规范易读前提下，尽量减小改动范围。 (源规则 38)

充分利用父类功能，避免子类重复实现。 (源规则 52)

移除回退设计、默认值回退、错误静默处理和硬编码关键参数。 (源规则 60)

二、 错误处理与调试 (Error Handling & Debugging)

错误暴露与根源修复 (Error Exposure & Root Cause Fixation):

面对错误时，大胆猜测并广泛分析，但修改时谨慎。 (源规则 7)

不迎合和匹配过度复杂化的辅助功能设计，只修复其错误根源，避免增加设计复杂度。 (源规则 9)

在开发阶段，禁止错误恢复和降级机制，应充分暴露问题；直接崩溃优于静默处理。 (源规则 15, 43, 44)

强烈禁止设置默认值、回退机制、兼容机制导致问题无法充分暴露和代码复杂化。 (源规则 35, 63)

必须修复问题根源，不添加额外的检查、预防、修正代码。若无法确定根源，可添加检验或日志辅助定位。 (源规则 13, 36)

问题分析与定位 (Problem Analysis & Localization):

分析问题前，必须查看出错模块及所有相关模块代码。 (源规则 10, 46, 48)

使用多维定位机制（五维度检查：时间序列、影响范围、版本变更、环境差异、数据特征）和三重因果验证（最小复现、边界测试、埋点验证）。 (源规则 14, 40)

追溯三级因果链（鱼骨图、故障传播图、5Why分析）。 (源规则 14, 40)

优先通过日志定位根源而非添加调试代码。 (源规则 14)

在理解问题前不要急于提出解决方案；完全理解问题才开始修复。 (源规则 20)

优先检查导入、依赖、命名、调用问题及可能的重复和冗余。 (源规则 25)

注意代码作者可能过分专注局部而忽略整体架构和模块协作。 (源规则 29)

在代码关键步骤设置断言检查，防止错误扩散。 (源规则 51)

三、 配置与日志管理 (Configuration & Logging Management)

配置管理规范 (Configuration Management Standards):

统一配置类型，仅使用ConfigManager对象作为配置来源，移除字典配置支持。 (源规则 11)

配置访问方式统一为属性访问。 (源规则 31)

配置文件仅作辅助，逻辑优先级高于配置。 (源规则 15)

ConfigManager模块只专注配置管理，不含配置内容或提供默认值；动态读取config.yaml。 (源规则 39, 54)

移除默认值回退逻辑，改用配置读取模式，避免掩盖配置读取错误。 (源规则 63)

日志规范 (Logging Standards):

在代码关键位置添加完善日志和注释。模块开头注释应包括相关模块路径和简介。 (源规则 5)

确保LoggerFactory在所有模块中被正确使用，采用UTF-8编码和覆写模式。 (源规则 14)

日志功能完全依赖于logger.py模块，避免重复日志管理。 (源规则 39)

四、 开发流程与协作 (Development Process & Collaboration)

代码修改与审查 (Code Modification & Review):

修复前查找出错位置和所有上游的重复代码。 (源规则 22)

添加代码前必须仔细查找是否存在类似实现，避免重复造轮子。 (源规则 22, 33)

不准添加任何代码结构除非经过认真讨论和确认无现有实现。 (源规则 22, 33)

得到初步方案后，应从不同角度否定方案，直到找到无法否定的方案。 (源规则 23)

如果修复存在多个方向，应让用户决定；仅在唯一方案时可直接实施。 (源规则 24)

需求理解与沟通 (Requirement Understanding & Communication):

修改前必须明确设计需求，优先询问关键问题。 (源规则 16, 28)

提出方案后需自我否定并寻找反例验证。 (源规则 16)

存在多个方案时必须由人工决策。 (源规则 16)

复述用户需求确保理解一致，无法理解时主动申请添加调试日志。 (源规则 16, 45, 56)

积极使用导图作为表达方法。 (源规则 26)

积极指出用户发言中的自相矛盾和不清晰之处。 (源规则 27)

提出问题时，必须同时给出几个描述详细的预设选项。 (源规则 42)

在提供修改方案时，回复中必须包含修改概述、具体变更、影响范围和修改原因。 (源规则 57)

测试与验证 (Testing & Verification):

遵守修复验证原则，编写针对性测试用例，执行回归测试。 (源规则 14)

积极使用python -c命令或交互式Python代码进行测试和状态验证。 (源规则 19, 50)

每个模块都应有相应的单元测试。 (源规则 58)

环境与工具 (Environment & Tooling):

使用Get-ChildItem -Recurse | Where-Object { $_.FullName -notlike "*\.git*" -and $_.FullName -notlike "*__pycache__*" }了解目录结构。 (源规则 6)

模糊搜索相关代码时，在整个代码库中搜索关键字，使用正则表达式匹配。 (源规则 8)

做好依赖管理，安装前检测已有依赖。 (源规则 15)

开发环境为Windows PowerShell，应适应其特性。 (源规则 53)

五、 特定约束与应用规则 (Specific Constraints & Application Rules)

数据处理特定要求 (Specific Data Handling Requirements):

原始数据路径为data\raw\combined_data.csv。模块加载后应在日志中输出前三行和后三行。combined_data.csv有21列（日期+20数值列），value15是目标列，实际特征列19列。 (源规则 1)

从数据流水线获取特征维度并传递。 (源规则 59)

硬件与性能 (Hardware & Performance):

CUDA操作必须通过CUDAManager统一管理，禁止降级到CPU，不允许多GPU支持。 (源规则 12)

CUDA设备规格：1152 unified shaders、3GB显存、192.2 GB/s带宽。 (源规则 17)

开发阶段性能要求禁止鲁棒性设计，优先暴露性能瓶颈。 (源规则 17)

特定工具使用 (Specific Tool Usage):

使用filesystem_server MCP工具时，文件路径必须使用绝对路径、反斜杠（\）作为路径分隔符。 (源规则 61)

使用filesystem_server的read_multiple_files工具一次性读取所有Memory Bank核心文件。 (源规则 62)

六、 进度报告 (Progress Reporting)

未完成进度通报 (Reporting Unfinished Progress):

在回复结尾必须报告未完成的进度，以便下次继续。 (源规则 21)
