"""
测试参数探索中的快速模式配置

此测试模块验证在参数探索过程中：
1. 快速模式正确设置训练轮数为1
2. 快速模式正确设置data.batch_size为32
3. 快速模式正确设置training.batch_size为32（确保一致性）
4. 快速模式正确禁用动态批次大小
"""

import logging
import unittest

import optuna

from src.optimization.hyperparameter_optimizer import _configure_fast_mode
from src.utils.config_manager import ConfigManager

# 设置日志级别和格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 确保日志输出到控制台
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
logger.addHandler(console_handler)

class TestFastModeConfig(unittest.TestCase):
    """测试参数探索中的快速模式配置"""

    def setUp(self):
        """测试前的准备工作"""
        # 创建一个简单的Study和Trial
        self.study = optuna.create_study(direction="minimize")
        self.trial = self.study.ask()

        # 加载配置 - 使用相对路径
        import os
        config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'config.yaml')
        self.config = ConfigManager.from_yaml(config_path)

        # 记录原始配置
        self.original_epochs = getattr(self.config.training, 'num_epochs', 'N/A')
        self.original_data_batch_size = getattr(self.config.data, 'batch_size', 'N/A')
        self.original_training_batch_size = getattr(self.config.training, 'batch_size', 'N/A')
        self.original_dynamic_batch_size = getattr(self.config.training, 'dynamic_batch_size', 'N/A')

        batch_size_optimizer = getattr(self.config.training, 'batch_size_optimizer', None)
        self.original_optimizer_enabled = getattr(batch_size_optimizer, 'enabled', 'N/A') if batch_size_optimizer else 'N/A'

        logger.info("原始配置:")
        logger.info(f"- 训练轮数: {self.original_epochs}")
        logger.info(f"- 批次大小(data): {self.original_data_batch_size}")
        logger.info(f"- 批次大小(training): {self.original_training_batch_size}")
        logger.info(f"- 动态批次大小: {self.original_dynamic_batch_size}")
        logger.info(f"- batch_size_optimizer.enabled: {self.original_optimizer_enabled}")

    def test_fast_mode_config(self):
        """测试快速模式配置是否正确应用"""
        # 应用快速模式配置
        _configure_fast_mode(self.trial, self.config)

        # 记录修改后的配置
        modified_epochs = getattr(self.config.training, 'num_epochs', 'N/A')
        modified_data_batch_size = getattr(self.config.data, 'batch_size', 'N/A')
        modified_training_batch_size = getattr(self.config.training, 'batch_size', 'N/A')
        modified_dynamic_batch_size = getattr(self.config.training, 'dynamic_batch_size', 'N/A')

        batch_size_optimizer = getattr(self.config.training, 'batch_size_optimizer', None)
        modified_optimizer_enabled = getattr(batch_size_optimizer, 'enabled', 'N/A') if batch_size_optimizer else 'N/A'

        logger.info("修改后的配置:")
        logger.info(f"- 训练轮数: {modified_epochs}")
        logger.info(f"- 批次大小(data): {modified_data_batch_size}")
        logger.info(f"- 批次大小(training): {modified_training_batch_size}")
        logger.info(f"- 动态批次大小: {modified_dynamic_batch_size}")
        logger.info(f"- batch_size_optimizer.enabled: {modified_optimizer_enabled}")

        # 验证配置是否正确修改
        self.assertEqual(modified_epochs, 1, f"训练轮数应为1，实际为{modified_epochs}")
        self.assertEqual(modified_data_batch_size, 32, f"data.batch_size应为32，实际为{modified_data_batch_size}")
        self.assertEqual(modified_training_batch_size, 32, f"training.batch_size应为32，实际为{modified_training_batch_size}")
        self.assertFalse(modified_dynamic_batch_size, f"动态批次大小应为False，实际为{modified_dynamic_batch_size}")
        self.assertFalse(modified_optimizer_enabled, f"batch_size_optimizer.enabled应为False，实际为{modified_optimizer_enabled}")

        logger.info("配置验证通过!")

if __name__ == "__main__":
    unittest.main()
