"""特征工程模块 - 提供高维度特征构建

此模块提供了一套模块化的特征工程系统，包括：
- 基础特征生成器接口
- 各类特征生成器实现
- 特征管理器

使用方法：
```python
from src.data.feature_engineering import FeatureManager
from src.utils.config.manager import ConfigManager

# 加载配置
config = ConfigManager.from_yaml('config.yaml')

# 创建特征管理器
feature_manager = FeatureManager(config)

# 处理数据
features_df = feature_manager.process(input_data)
```
"""

from src.data.feature_engineering.base import BaseFeatureGenerator
from src.data.feature_engineering.diff_features import DiffFeatureGenerator
from src.data.feature_engineering.feature_manager import FeatureManager
from src.data.feature_engineering.interaction_features import (
    InteractionFeatureGenerator,
)
from src.data.feature_engineering.lag_features import LagFeatureGenerator
from src.data.feature_engineering.time_features import TimeFeatureGenerator
from src.data.feature_engineering.volatility_features import VolatilityFeatureGenerator
from src.data.feature_engineering.window_features import WindowFeatureGenerator

__all__ = [
    'BaseFeatureGenerator',
    'DiffFeatureGenerator',
    'FeatureManager',
    'InteractionFeatureGenerator',
    'LagFeatureGenerator',
    'TimeFeatureGenerator',
    'VolatilityFeatureGenerator',
    'WindowFeatureGenerator'
]
