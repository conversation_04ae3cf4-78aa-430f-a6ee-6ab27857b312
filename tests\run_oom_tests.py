"""
OOM处理测试运行脚本

此脚本用于运行OOM处理相关的测试，包括：
1. 批次大小优化器OOM处理测试
2. 训练器OOM处理测试

使用方法：
python tests/run_oom_tests.py
"""

import argparse
import os
import subprocess
import sys

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

def run_test(test_file_path, verbose=True):
    """运行指定的测试文件"""
    print(f"运行测试文件: {test_file_path}")

    # 确保当前工作目录是项目根目录
    os.chdir(project_root)
    print(f"当前工作目录: {os.getcwd()}")

    # 构建完整的测试文件路径
    full_path = os.path.join(project_root, test_file_path)

    # 检查测试文件是否存在
    if not os.path.exists(full_path):
        print(f"错误: 测试文件不存在: {full_path}")
        return False

    # 使用pytest运行测试
    cmd = [sys.executable, "-m", "pytest", full_path]
    if verbose:
        cmd.append("-v")

    # 设置环境变量，指定编码
    env = os.environ.copy()
    env['PYTHONIOENCODING'] = 'utf-8'

    # 运行测试
    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', env=env, check=False)

    # 打印运行结果
    print(f"运行结果代码: {result.returncode}")
    if result.stdout:
        print("\n标准输出:")
        print(result.stdout)
    if result.stderr:
        print("\n标准错误:")
        print(result.stderr)

    return result.returncode == 0

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行OOM处理测试")
    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细输出")
    parser.add_argument("--test", "-t", choices=["all", "batch_size", "trainer"], default="all", help="指定要运行的测试")
    args = parser.parse_args()

    # 测试文件路径
    test_files = {
        "batch_size": "tests/test_utils/test_batch_size_optimizer_oom.py",
        "trainer": "tests/test_models/test_gan/test_trainer_oom_handling.py"
    }

    # 运行测试
    success = True
    if args.test == "all":
        for name, path in test_files.items():
            print(f"\n=== 运行 {name} 测试 ===\n")
            if not run_test(path, args.verbose):
                success = False
    else:
        path = test_files[args.test]
        if not run_test(path, args.verbose):
            success = False

    # 返回结果
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
