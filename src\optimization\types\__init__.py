"""
参数优化相关的类型定义。
"""

# 从主要配置模块导入
from src.utils.config.training import BalanceConfig, LossConfig, OptimizerConfig
from src.utils.config.training import LrBalancerConfig as MainLrBalancerConfig

from .config_types import (
    AdaptiveAttentionConfig,
    AttentionConfig,
    DataConfig,
    ModelConfig,
    ParameterOptimizationConfig,
    TrainingConfig,
)

__all__ = [
    'AdaptiveAttentionConfig',
    'AttentionConfig',
    'BalanceConfig',
    'DataConfig',
    'LossConfig',
    'MainLrBalancerConfig',  # 使用导入的LrBalancerConfig的别名
    'ModelConfig',
    'OptimizerConfig',
    'ParameterOptimizationConfig',  # 重命名后的优化配置类
    'TrainingConfig'
    # Removed LoggingConfig export as it's not defined here
]
