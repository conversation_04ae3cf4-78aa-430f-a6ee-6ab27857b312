"""测试 ConfigManager 对 batch_size 的处理"""

import tempfile
from pathlib import Path

import pytest

from src.utils.config import ConfigManager, TrainingConfig

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

@pytest.fixture
def test_config_path() -> Path:
    """返回测试配置文件的路径"""
    return PROJECT_ROOT / "config_test.yaml"

@pytest.fixture
def main_config_path() -> Path:
    """返回主配置文件的路径"""
    return PROJECT_ROOT / "config.yaml"

def test_load_batch_size_from_test_config(test_config_path):
    """测试: 确认从 config_test.yaml 加载 batch_size"""
    # 预期值来自 config_test.yaml
    expected_batch_size = 32

    # 加载测试配置
    config_manager = ConfigManager.from_yaml(test_config_path)

    # 断言 batch_size 正确加载
    assert isinstance(config_manager.training, TrainingConfig), "config_manager.training 不是 TrainingConfig 类型"
    assert hasattr(config_manager.training, 'batch_size'), "TrainingConfig 实例缺少 batch_size 属性"
    assert config_manager.training.batch_size == expected_batch_size, \
        f"从 config_test.yaml 加载的 batch_size 应为 {expected_batch_size}，实际为 {config_manager.training.batch_size}"

def test_load_batch_size_from_main_config(main_config_path):
    """测试: 确认从 config.yaml 加载 batch_size"""
    # 预期值来自 config.yaml
    expected_batch_size = 64

    # 加载主配置
    config_manager = ConfigManager.from_yaml(main_config_path)

    # 断言 batch_size 正确加载
    assert isinstance(config_manager.training, TrainingConfig), "config_manager.training 不是 TrainingConfig 类型"
    assert hasattr(config_manager.training, 'batch_size'), "TrainingConfig 实例缺少 batch_size 属性"
    assert config_manager.training.batch_size == expected_batch_size, \
        f"从 config.yaml 加载的 batch_size 应为 {expected_batch_size}，实际为 {config_manager.training.batch_size}"


def test_dynamic_batch_size_override():
    """测试: 确认动态修改 batch_size 配置有效"""
    new_batch_size = 16
    # 创建一个临时的 YAML 内容
    temp_config_content = f"""
version: "test"
paths: {{}}
system: {{}}
logging: {{}}
data: {{}}
model: # 添加必需的 model 字段
  type: "base"
  noise_dim: 64
  dimensions: {{}}
feature_engineering: {{}}
evaluation: {{}}
training:
  batch_size: {new_batch_size}
  # 其他 training 字段如果需要，也应在此处添加
"""

    # 使用临时文件写入和读取，并指定编码
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix=".yaml", delete=False) as tmp_file:
        tmp_file.write(temp_config_content)
        tmp_file_path = tmp_file.name

    try:
        # 从临时文件加载配置
        config_manager = ConfigManager.from_yaml(tmp_file_path)

        # 断言 batch_size 被正确覆盖
        assert isinstance(config_manager.training, TrainingConfig), "config_manager.training 不是 TrainingConfig 类型"
        assert hasattr(config_manager.training, 'batch_size'), "TrainingConfig 实例缺少 batch_size 属性"
        assert config_manager.training.batch_size == new_batch_size, \
            f"动态加载的 batch_size 应为 {new_batch_size}，实际为 {config_manager.training.batch_size}"

    finally:
        # 清理临时文件
        Path(tmp_file_path).unlink()

def test_missing_batch_size_raises_error():
    """测试: 确认缺少 batch_size 配置时会引发错误 (因为已移除默认值)"""
    # 创建一个缺少 batch_size 的临时 YAML 内容
    temp_config_content = """
version: "test"
paths: {}
system: {}
logging: {}
data: {}
model: # 添加必需的 model 字段
  type: "base"
  noise_dim: 64
  dimensions: {}
feature_engineering: {}
evaluation: {}
training:
  num_epochs: 10 # 包含 training 部分，但没有 batch_size
"""

    # 使用临时文件写入和读取，并指定编码
    with tempfile.NamedTemporaryFile(mode='w', encoding='utf-8', suffix=".yaml", delete=False) as tmp_file:
        tmp_file.write(temp_config_content)
        tmp_file_path = tmp_file.name

    try:
        # 尝试从缺少 batch_size 的文件加载配置
        # 预期会因为 TrainingConfig 初始化缺少必需参数而引发 TypeError
        with pytest.raises(TypeError, match=r".*__init__\(\) missing 1 required positional argument: 'batch_size'.*"):
             ConfigManager.from_yaml(tmp_file_path)

    finally:
        # 清理临时文件
        Path(tmp_file_path).unlink()
