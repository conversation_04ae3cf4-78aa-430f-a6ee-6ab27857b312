"""
参数优化模块的自定义异常类。

定义了一系列专门用于参数优化过程中的异常，以提供更明确的错误信息和更好的错误处理机制。
主要包括配置验证错误和优化过程错误两大类。
"""

class OptimizationError(Exception):
    """
    参数优化过程中错误的基类。
    所有优化相关的自定义异常都应该继承自这个类。
    """
    pass


class ConfigurationError(OptimizationError):
    """配置相关错误的基类"""
    pass


class MissingConfigError(ConfigurationError):
    """
    必需的配置项缺失时抛出此异常。

    示例:
        >>> if not hasattr(config, 'training'):
        ...     raise MissingConfigError("缺少training配置部分")
    """
    pass


class InvalidConfigValueError(ConfigurationError):
    """
    配置值无效时抛出此异常。
    用于验证配置值的类型和范围。

    示例:
        >>> if config.training.lr_balancer.min_lr <= 0:
        ...     raise InvalidConfigValueError("lr_balancer.min_lr必须大于0")
    """
    pass


class MetricError(OptimizationError):
    """
    指标计算或验证相关的错误。
    用于处理训练过程中的指标异常。
    """
    pass


class ResourceError(OptimizationError):
    """
    资源相关错误。
    用于处理GPU内存、文件等资源问题。
    """
    pass
