"""
测试模块路径：tests/test_utils/test_optimizer_manager.py
测试目标：验证src/utils/optimizer_manager.py的优化器管理功能

测试要点：
1. 优化器创建功能
2. 学习率调度器创建功能
3. 学习率更新逻辑
4. 梯度裁剪功能
5. 使用规范验证
"""

import unittest
from unittest.mock import MagicMock, patch

import pytest
import torch
from torch import nn, optim

from src.utils.config_manager import ConfigManager

# 不再需要导入 CyclicLR
# from torch.optim.lr_scheduler import CyclicLR
from src.utils.optimizer_manager import OptimizerManager


# Helper function to create nested mocks with specific values
def create_nested_mock_config(optimizer_config_dict, scheduler_config_dict):
    config = MagicMock(spec=ConfigManager)
    config.training = MagicMock()

    # Optimizer Mock Setup
    optimizer_mock = MagicMock()
    for key, value in optimizer_config_dict.items():
        setattr(optimizer_mock, key, value)
    config.training.optimizer = optimizer_mock
    # Ensure direct access also works if needed by the code under test
    # Mock the .get('training.optimizer') behavior - assuming it might be used
    # If .get is not used, this won't hurt. If it is, it helps.
    # Check if 'get' needs to be mocked on the top-level config or nested training mock
    # Assuming OptimizerManager calls config.get('training.optimizer')
    # If it calls config.training.get('optimizer'), adjust mock accordingly.
    # For simplicity, mocking top-level get first.
    config.get = MagicMock(return_value=optimizer_mock)


    # Scheduler Mock Setup
    scheduler_mock = MagicMock()
    for key, value in scheduler_config_dict.items():
        setattr(scheduler_mock, key, value)
    config.training.scheduler = scheduler_mock

    return config

@pytest.mark.batch3  # 优化器管理器测试
class TestOptimizerManager(unittest.TestCase):
    def setUp(self):
        """测试前准备 - 使用修正后的模拟配置"""
        optimizer_config = {
            "type": "adam",
            "generator_lr": 1e-4,
            "discriminator_lr": 1e-4,
            "weight_decay": 0.0,
            "beta1": 0.9,
            "beta2": 0.999,
            "momentum": 0.0,
            "eps": 1e-8,
        }
        scheduler_config = {
            "type": "cyclic",
            "base_lr": 0.001,
            "max_lr": 0.1,
            "step_size_up": 2000,
            "step_size_down": 2000,
            "mode_cyclic": "triangular",
            "cycle_momentum": True,
            "base_momentum": 0.8,
            "max_momentum": 0.9,
            "gamma": 1.0,
            "min_lr": 0.00001
        }
        self.config = create_nested_mock_config(optimizer_config, scheduler_config)
        self.manager = OptimizerManager(self.config)
        self.model = nn.Linear(10, 1)

    def test_create_optimizer(self):
        """测试优化器创建"""
        optimizer = self.manager.create_optimizer(self.model, model_type='generator')
        self.assertIsInstance(optimizer, optim.Adam) # Check specific type
        self.assertAlmostEqual(optimizer.param_groups[0]['lr'], 1e-4)

        sgd_optimizer = self.manager.create_optimizer(self.model, optimizer_type="sgd", model_type='generator')
        self.assertIsInstance(sgd_optimizer, optim.SGD)

        rms_optimizer = self.manager.create_optimizer(self.model, optimizer_type="rmsprop", model_type='generator')
        self.assertIsInstance(rms_optimizer, optim.RMSprop)

    # 移除测试 create_scheduler 的方法，因为它已被删除
    # def test_create_scheduler(self): ...

    # 移除测试 update_learning_rate 的方法，因为它已被删除
    # def test_update_learning_rate(self): ...

    def test_clip_gradients(self):
        """测试梯度裁剪"""
        self.manager.create_optimizer(self.model, model_type='generator')

        output = self.model(torch.randn(1, 10))
        loss = output.sum()
        loss.backward()

        self.manager.clip_gradients(self.model, max_norm=1.0) # Use default max_norm

        for param in self.model.parameters():
            if param.grad is not None:
                self.assertLessEqual(param.grad.norm().item(), 1.0 + 1e-6)


@pytest.mark.batch2  # 优化器使用规范测试
class TestOptimizerManagerUsagePatterns(unittest.TestCase):
    """测试优化器管理模块的使用规范（新增）"""

    def setUp(self):
        """测试前准备 - 使用修正后的模拟配置"""
        optimizer_config = {
            "type": "adam",
            "generator_lr": 1e-4,
            "discriminator_lr": 1e-4,
            "weight_decay": 0.0,
            "beta1": 0.9,
            "beta2": 0.999,
            "momentum": 0.0,
            "eps": 1e-8,
        }
        # Provide minimal scheduler config needed for manager init when focusing on optimizer usage
        scheduler_config = {
            "type": "plateau", # Or any valid type
            # Add other defaults accessed by _configure_optimizer if needed
            "mode": "min", "factor": 0.5, "patience": 5, "threshold": 1e-4,
            "min_lr": 1e-6, "cooldown": 0, "verbose": False, "monitor": "val_loss"
        }
        self.config = create_nested_mock_config(optimizer_config, scheduler_config)
        self.manager = OptimizerManager(self.config)
        self.model = nn.Linear(10, 1)

    def test_optimizer_access_pattern(self):
        """测试优化器访问规范（规则56）"""
        optimizer = self.manager.create_optimizer(self.model, model_type='generator')
        self.assertIsInstance(optimizer, optim.Optimizer)

        with self.assertRaises(TypeError):
            _ = self.manager['create_optimizer'](self.model, model_type='generator') # type: ignore[misc]

    def test_no_fallback_mechanism(self):
        """测试禁止降级机制（规则44）"""
        with patch.object(self.manager, 'create_optimizer',
                         side_effect=Exception("Optimizer error")):
            with self.assertRaises(Exception) as cm:
                # Need to pass model_type even when mocking
                self.manager.create_optimizer(self.model, model_type='generator')
            self.assertEqual(str(cm.exception), "Optimizer error")

    def test_interface_isolation(self):
        """测试接口隔离原则（规则14）"""
        with self.assertRaises(AttributeError):
            _ = self.manager._optimizer_type # type: ignore[attr-defined]


# --- Test Class for Dynamic LR ---
@pytest.mark.batch4 # Assigning to a new batch for clarity
class TestOptimizerManagerDynamicLR(unittest.TestCase):
    """测试OptimizerManager的动态学习率调整功能 (Plateau)"""

    def setUp(self):
        """测试前准备，模拟config.yaml中的设置 - 使用修正后的模拟配置"""
        # Base configs used in multiple tests
        self.base_optimizer_config = {
            "type": "adam", "generator_lr": 0.001, "discriminator_lr": 0.001,
            "weight_decay": 0.0001, "beta1": 0.9, "beta2": 0.999,
            "momentum": 0.0, "eps": 1e-8,
        }
        self.base_scheduler_config = {
            "type": "cyclic",
            "base_lr": 0.001,
            "max_lr": 0.1,
            "step_size_up": 10,
            "step_size_down": 10,
            "mode_cyclic": "triangular",
            "cycle_momentum": True,
            "base_momentum": 0.8,
            "max_momentum": 0.9,
            "gamma": 1.0,
            "min_lr": 0.0001,
            "verbose": False
        }

        self.model = nn.Linear(10, 1)



    def test_create_optimizer_uses_correct_lr(self):
        """验证create_optimizer根据model_type使用正确的学习率"""
        # Use a minimal scheduler config for this optimizer test
        scheduler_config = {"type": "cyclic", "base_lr": 0.001, "max_lr": 0.1} # Use cyclic type
        config = create_nested_mock_config(self.base_optimizer_config, scheduler_config)
        manager = OptimizerManager(config)

        # Generator
        g_optimizer = manager.create_optimizer(self.model, model_type='generator')
        self.assertAlmostEqual(g_optimizer.param_groups[0]['lr'], self.base_optimizer_config["generator_lr"], places=7)

        # Discriminator
        d_optimizer = manager.create_optimizer(self.model, model_type='discriminator')
        self.assertAlmostEqual(d_optimizer.param_groups[0]['lr'], self.base_optimizer_config["discriminator_lr"], places=7)

        # Invalid type
        with self.assertRaises(ValueError):
            manager.create_optimizer(self.model, model_type='invalid_type')

    # --- 移除所有与旧调度器相关的测试 ---
    # def test_cyclic_lr_changes_on_step(self): ...
    # def test_cyclic_lr_respects_base_and_max_lr(self): ...
    # def test_cyclic_lr_with_different_modes(self): ...





if __name__ == '__main__':
    unittest.main()
