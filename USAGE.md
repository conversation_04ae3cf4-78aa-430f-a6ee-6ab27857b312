# 时序预测系统使用说明

## 运行模式简介

本系统提供四种运行模式，必须通过 `--mode` 参数显式指定：

### 1. 数据处理模式 (process)

仅执行数据处理流程，不进行训练或预测。适用于数据探索和特征工程实验。

```bash
# 执行数据处理并保存结果到指定目录
python main.py --mode process --config config.yaml --output_dir my_processed_data --save_processed
```

### 2. 训练模式 (train)

训练GAN模型用于时间序列预测。

```bash
# 使用指定配置文件训练模型
python main.py --mode train --config custom_config.yaml

# 从检查点恢复训练
python main.py --mode train --config custom_config.yaml --resume --checkpoint outputs/models/GAN_epoch_5.pt
```

### 3. 评估模式 (evaluate)

评估已训练模型的性能。

```bash
# 评估指定模型
python main.py --mode evaluate --config config.yaml --model outputs/models/GAN_epoch_10.pt
```

### 4. 预测模式 (predict)

使用训练好的模型进行预测。

```bash
# 对指定输入数据进行预测并保存到指定位置
python main.py --mode predict --config config.yaml --model outputs/models/GAN_epoch_10.pt --input data/test.csv --output outputs/predictions
```

## 必需参数说明

### 通用参数

- `--config`: 指定配置文件路径 (必需)
- `--mode`: 指定运行模式 (必需)，可选值为 `process`, `train`, `evaluate`, `predict`

### 数据处理模式参数

- `--save_processed`: 是否保存处理后的数据
- `--output_dir`: 处理后数据的保存目录
- `--skip_cleaning`: 跳过数据清洗步骤（仅用于调试）
- `--clean_method`: 指定清洗方法（覆盖配置文件设置）

数据清洗配置项（config.yaml）:
```yaml
data:
  cleaning:
    outlier_threshold: 3.0  # 异常值阈值(基于MAD)
    missing_value_strategy: "median"  # 缺失值处理策略(median/mean/drop)
```

### 训练模式参数

- `--resume`: 是否从检查点恢复训练
- `--checkpoint`: 检查点路径（用于恢复训练）

### 评估和预测模式参数

- `--model`: 模型路径
- `--input`: 输入数据路径（用于预测模式）
- `--output`: 输出结果路径（用于预测模式）

## 更多信息

详细的使用指南请参考 [USER_GUIDE.md](USER_GUIDE.md)。
