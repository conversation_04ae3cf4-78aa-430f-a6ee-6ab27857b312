"""特征工程基础模块 - 定义特征生成器接口

此模块定义了所有特征生成器必须实现的基础接口，确保特征生成器的一致性和可扩展性。
"""

from abc import ABC, abstractmethod

import torch


class BaseFeatureGenerator(ABC):
    """特征生成器基类

    所有特征生成器必须实现此接口，以确保一致的API和行为。
    """

    @abstractmethod
    def generate(self, data: torch.Tensor, **kwargs) -> torch.Tensor:
        """生成特征

        Args:
            data: 输入数据张量 [n_samples, n_features]
            **kwargs: 其他参数，如日期序列等

        Returns:
            torch.Tensor: 生成的特征张量 [n_samples, n_generated_features]
        """
        pass

    @abstractmethod
    def get_feature_names(self) -> list[str]:
        """获取生成的特征名称列表

        Returns:
            List[str]: 特征名称列表
        """
        pass

    @property
    @abstractmethod
    def feature_count(self) -> int:
        """获取生成的特征数量

        Returns:
            int: 特征数量
        """
        pass

    @property
    @abstractmethod
    def is_enabled(self) -> bool:
        """检查特征生成器是否启用

        Returns:
            bool: 是否启用
        """
        pass
