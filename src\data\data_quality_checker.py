"""
数据质量检查器 - 负责在数据标准化后进行深入质量检查

执行时机：
- 在数据标准化后执行
- 在窗口创建和模型输入准备之前执行
- 作为数据处理流水线的第七步（...→标准化→质量检查→窗口→准备）

功能：
1. 检查标准化后的数据是否包含NaN或Inf值
2. 检查数据范围是否在合理区间内（最大绝对值检查）
3. 检查数据变异性是否足够（方差检查）
4. 确保数据质量满足模型训练要求

处理原则：
- 任何一项检查不通过就停止执行，抛出异常
- 不尝试修复问题，而是明确报告问题
- 所有检查参数必须从配置中明确指定，不使用默认值

与其他模块的区别：
- 与DataValidator的区别：本模块在数据标准化后执行，而DataValidator在数据加载后立即执行
- 与DataCleaner的区别：本模块只检测问题，不修复问题
- 本模块专注于张量数据（Tensor），而非DataFrame

相关模块：
- src/data/data_pipeline.py: 调用本模块进行数据质量检查
- src/data/preprocessing/data_validator.py: 负责数据加载后的基本验证
- src/data/preprocessing/data_cleaner.py: 负责数据清洗
"""

import torch
from torch import Tensor

from src.utils.logger import LoggerFactory

# 获取日志记录器
logger = LoggerFactory().get_logger(__name__)

class DataQualityError(ValueError):
    """自定义异常，表示数据质量检查失败"""
    pass

class DataQualityChecker:
    """
    执行数据质量检查的类。

    包含检查 NaN/Inf、数据范围和数据变异性的方法。
    """

    @staticmethod
    def check_nan_inf(tensor: Tensor, tensor_name: str = "Tensor") -> None:
        """
        检查张量中是否包含 NaN 或 Inf 值。

        Args:
            tensor: 需要检查的张量。
            tensor_name: 张量的名称，用于错误消息。

        Raises:
            DataQualityError: 如果张量包含 NaN 或 Inf 值。
        """
        if torch.isnan(tensor).any():
            error_msg = f"数据质量检查失败: {tensor_name} 包含 NaN 值。"
            logger.error(error_msg)
            raise DataQualityError(error_msg)
        if torch.isinf(tensor).any():
            error_msg = f"数据质量检查失败: {tensor_name} 包含 Inf 值。"
            logger.error(error_msg)
            raise DataQualityError(error_msg)
        logger.debug(f"{tensor_name} 的 NaN/Inf 检查通过。")

    @staticmethod
    def check_range(tensor: Tensor, max_abs_value: float, tensor_name: str = "Tensor") -> None:
        """
        检查张量中的值是否超出指定的最大绝对值。

        Args:
            tensor: 需要检查的张量。
            max_abs_value: 允许的最大绝对值。
            tensor_name: 张量的名称，用于错误消息。

        Raises:
            DataQualityError: 如果张量中的任何值的绝对值超过 max_abs_value。
        """
        if (torch.abs(tensor) > max_abs_value).any():
            out_of_range_values = tensor[torch.abs(tensor) > max_abs_value]
            error_msg = (
                f"数据质量检查失败: {tensor_name} 包含超出范围的值 (绝对值 > {max_abs_value}). "
                f"例如: {out_of_range_values.flatten()[0:5].tolist()}..." # 显示前5个违规值
            )
            logger.error(error_msg)
            raise DataQualityError(error_msg)
        logger.debug(f"{tensor_name} 的数据范围检查 (最大绝对值={max_abs_value}) 通过。")

        # 增加: 检查非常小的值(可能导致除法不稳定)
        if (torch.abs(tensor) < 1e-6).any() and (tensor != 0).any():
            error_msg = f"数据质量检查失败: {tensor_name} 包含过小的非零值 (绝对值 < 1e-6)"
            logger.error(error_msg)
            raise DataQualityError(error_msg)

    @staticmethod
    def check_variance(tensor: Tensor, min_variance: float, tensor_name: str = "Tensor", check_dim: int = -1) -> None:
        """
        检查张量在指定维度上的方差是否低于最小值。
        通常用于检查每个特征或每个窗口的变异性。

        Args:
            tensor: 需要检查的张量 (例如, 形状为 [batch, window, features])。
            min_variance: 允许的最小方差。
            tensor_name: 张量的名称，用于错误消息。
            check_dim: 计算方差的维度。例如，对于形状 [batch, window, features] 的特征张量：
                       - dim=1: 检查每个特征在窗口内的方差。
                       - dim=0: 检查每个时间步在批次间的方差。
                       - dim=-1 (或 2): 检查每个窗口内所有特征的整体方差（可能意义不大）。
                       通常建议检查每个特征在窗口内的方差 (dim=1)。

        Raises:
            DataQualityError: 如果任何计算出的方差低于 min_variance。
        """
        if tensor.numel() == 0:
             logger.info(f"跳过 {tensor_name} 的方差检查，因为张量为空。")
             return
        if tensor.shape[check_dim] <= 1:
             logger.info(f"跳过 {tensor_name} 的方差检查，因为维度 {check_dim} 的大小 <= 1，无法计算方差。")
             return

        # 增加: 更严格的方差阈值
        min_variance = max(min_variance, 1e-4)  # 确保最小方差不会太小


        # 计算指定维度上的方差，保持维度以便广播比较
        variances = torch.var(tensor, dim=check_dim, unbiased=False, keepdim=True) # 使用总体方差

        # 检查是否有方差低于阈值
        low_variance_mask = variances < min_variance

        if low_variance_mask.any():
            # 找出第一个方差过低的索引（为了简化日志）
            # 注意：这可能需要根据 check_dim 调整以获得更有意义的索引
            low_var_indices = torch.where(low_variance_mask)
            first_low_var_index = tuple(idx[0].item() for idx in low_var_indices) # 获取第一个违规元素的完整索引
            first_low_variance = variances[low_variance_mask].flatten()[0].item()

            error_msg = (
                f"数据质量检查失败: {tensor_name} 在维度 {check_dim} 上存在方差过低的情况 (< {min_variance}). "
                f"第一个检测到的低方差索引: {first_low_var_index}, 方差值: {first_low_variance:.2e}"
            )
            logger.error(error_msg)
            raise DataQualityError(error_msg)
        logger.debug(f"{tensor_name} 在维度 {check_dim} 上的方差检查 (最小方差={min_variance}) 通过。")

    @classmethod
    def check_data(cls, features: Tensor, targets: Tensor, max_abs_value: float, min_variance: float) -> None:
        """
        对特征和目标张量执行所有数据质量检查。

        Args:
            features: 特征张量 (通常形状为 [batch, window, features])。
            targets: 目标张量 (通常形状为 [batch, window, target_dim])。
            max_abs_value: 允许的最大绝对值。
            min_variance: 允许的最小方差。

        Raises:
            DataQualityError: 如果任何检查失败。
        """
        logger.info("开始执行标准化后数据质量检查...")

        # 检查参数
        if max_abs_value is None:
            error_msg = "max_abs_value参数不能为None"
            logger.error(error_msg)
            raise ValueError(error_msg)

        if min_variance is None:
            error_msg = "min_variance参数不能为None"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 1. 检查 NaN / Inf
        cls.check_nan_inf(features, "特征(Features)")
        cls.check_nan_inf(targets, "目标(Targets)")

        # 2. 检查数据范围
        cls.check_range(features, max_abs_value, "特征(Features)")
        cls.check_range(targets, max_abs_value, "目标(Targets)") # 对目标也应用范围检查

        # 3. 检查数据变异性
        # 检查每个特征在每个窗口内的方差 (dim=1)
        if features.ndim >= 2: # 至少需要 batch 和 window 维度
             cls.check_variance(features, min_variance, "特征(Features)", check_dim=1)
        else:
             logger.info("特征张量维度不足2，跳过窗口内方差检查。")

        # 检查每个目标在每个窗口内的方差 (dim=1)
        if targets.ndim >= 2: # 至少需要 batch 和 window 维度
             cls.check_variance(targets, min_variance, "目标(Targets)", check_dim=1)
        else:
             logger.info("目标张量维度不足2，跳过窗口内方差检查。")

        logger.info("数据质量检查全部通过。")

__all__ = ['DataQualityChecker', 'DataQualityError']
