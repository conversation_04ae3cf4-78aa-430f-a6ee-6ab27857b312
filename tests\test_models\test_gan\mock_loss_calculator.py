"""
Mock Loss Calculator for testing purposes.
Mimics the interface of the actual LossCalculator.
"""
import torch


class MockLossCalculator:
    """模拟损失计算器，用于测试"""

    def __init__(self, config):
        """初始化模拟损失计算器"""
        self.config = config
        # 从配置中提取权重（与测试文件中的断言匹配）
        loss_config = config.model.loss
        self.adversarial_weight = loss_config.adversarial_weight
        self.feature_matching_weight = loss_config.feature_matching_weight
        self.temporal_consistency_weight = loss_config.temporal_consistency_weight
        self.trend_weight = loss_config.trend_weight
        # 添加一个简单的日志记录器模拟，如果需要的话
        self.logger = type('MockLogger', (), {'info': print, 'debug': print, 'warning': print, 'error': print})()

    def adversarial_loss(self, validity, is_generator=False, is_real=False):
        """模拟对抗损失计算"""
        # 返回一个标量张量作为模拟损失
        return torch.tensor(0.5, requires_grad=True)

    def feature_matching_loss(self, real_features, fake_features):
        """模拟特征匹配损失"""
        # 返回一个标量张量作为模拟损失
        return torch.tensor(1.0, requires_grad=True)

    def temporal_consistency_loss(self, real_data, fake_data):
        """模拟时序一致性损失"""
        # 返回一个标量张量作为模拟损失
        return torch.tensor(0.2, requires_grad=True)

    def trend_loss(self, real_data, fake_data):
        """模拟趋势损失计算"""
        # 返回一个标量张量作为模拟损失
        return torch.tensor(0.1, requires_grad=True)

    def generator_loss(self, disc_output_fake, disc_output_real, real_data, fake_data):
        """模拟生成器组合损失计算"""
        # 返回包含模拟损失的字典
        return {
            'total': torch.tensor(1.8, requires_grad=True),
            'adversarial': torch.tensor(0.5, requires_grad=True),
            'feature_matching': torch.tensor(1.0, requires_grad=True),
            'temporal_consistency': torch.tensor(0.2, requires_grad=True),
            'trend': torch.tensor(0.1, requires_grad=True)
        }

    def discriminator_loss(self, disc_output_real, disc_output_fake):
        """模拟判别器组合损失计算"""
        # 返回包含模拟损失的字典
        return {
            'total': torch.tensor(1.0, requires_grad=True),
            'real': torch.tensor(0.5, requires_grad=True),
            'fake': torch.tensor(0.5, requires_grad=True)
        }
