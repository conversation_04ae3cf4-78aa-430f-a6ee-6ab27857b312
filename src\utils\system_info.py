"""
系统信息收集模块

本模块提供系统环境信息的收集功能，包括操作系统、Python版本、PyTorch版本、
CUDA设备信息、系统内存等。将系统信息收集相关的功能封装在一个模块中，
使入口模块更加专注。

主要组件:
----------
1. collect_system_info: 收集系统信息并返回格式化字符串列表
2. log_system_info: 将系统信息记录到日志

使用方式:
----------
```python
from src.utils.system_info import collect_system_info, log_system_info

# 获取系统信息
info_lines = collect_system_info()
for line in info_lines:
    print(line)

# 或者直接记录到日志
log_system_info(logger)
```
"""

import os
import platform

import psutil
import torch


def collect_system_info():
    """
    收集系统信息

    Returns:
        List[str]: 系统信息的格式化字符串列表
    """
    info_lines = ["系统信息:"]
    info_lines.append(f"- 操作系统: {platform.system()} {platform.version()}")
    info_lines.append(f"- Python版本: {platform.python_version()}")
    info_lines.append(f"- PyTorch版本: {torch.__version__}")

    # CUDA信息
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        info_lines.append(f"- CUDA可用: 是 (设备数: {device_count})")
        for i in range(device_count):
            device_name = torch.cuda.get_device_name(i)
            device_cap = torch.cuda.get_device_capability(i)
            info_lines.append(f"  - GPU {i}: {device_name} (CUDA能力: {device_cap[0]}.{device_cap[1]})")
    else:
        info_lines.append("- CUDA可用: 否")

    # 内存信息
    mem = psutil.virtual_memory()
    info_lines.append(f"- 系统内存: 总计 {mem.total / (1024**3):.1f}GB, 可用 {mem.available / (1024**3):.1f}GB ({100 - mem.percent:.1f}% 空闲)")

    # 当前工作目录
    info_lines.append(f"- 工作目录: {os.getcwd()}")

    return info_lines


def log_system_info(logger):
    """
    将系统信息记录到日志

    Args:
        logger: 日志记录器
    """
    logger.info("=" * 50)
    for line in collect_system_info():
        logger.info(line)
    logger.info("=" * 50)
