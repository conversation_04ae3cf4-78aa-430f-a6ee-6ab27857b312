[tool.ruff]
# 排除一些目录
exclude = [
    ".git",
    ".roo",
    "__pycache__",
    "venv",
    ".venv",
    "env",
    ".env",
    "build",
    "dist",
]

# 行长度限制
line-length = 120

# 目标Python版本
target-version = "py311"

[tool.ruff.lint]
# 启用所有规则
select = ["E", "F", "W", "I", "N", "UP", "B", "C4", "SIM", "RUF"]
# 忽略一些规则
ignore = [
    "E501",  # 行太长
    "E741",  # 变量名太短
    "RUF001", # 中文字符串
    "RUF002", # 中文文档字符串
    "RUF003", # 中文注释
]

# 允许自动修复的规则
fixable = ["ALL"]
unfixable = []

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]  # 忽略未使用的导入
"tests/*" = ["E501"]  # 测试文件中忽略行长度限制

[tool.ruff.lint.isort]
known-first-party = ["src"]
