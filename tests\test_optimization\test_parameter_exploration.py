#!/usr/bin/env python
"""
测试参数探索功能

本模块测试超参数优化中的参数探索功能，包括参数依赖关系处理、按优先级顺序探索参数和OOM风险检查。

测试内容:
1. 参数配置
2. 参数依赖关系处理
3. 参数取值范围调整
4. OOM风险检查
"""

import logging
import os
import sys
import unittest
from unittest.mock import MagicMock

import optuna

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# 导入被测试的模块
from src.optimization.parameter_exploration import parameter_explorer
from src.utils.config_manager import ConfigManager

# 配置日志
logging.basicConfig(level=logging.DEBUG)


class TestParameterExploration(unittest.TestCase):
    """测试参数探索功能"""

    def setUp(self):
        """测试前的准备工作"""
        # 禁用日志输出，使测试输出更清晰
        logging.disable(logging.CRITICAL)

        # 创建模拟的Optuna试验
        self.trial = MagicMock(spec=optuna.trial.Trial)
        self.trial.number = 1

        # 创建模拟的配置管理器
        self.config = MagicMock(spec=ConfigManager)
        self.config.model = MagicMock()
        self.config.data = MagicMock()
        self.config.training = MagicMock()

    def tearDown(self):
        """测试后的清理工作"""
        # 恢复日志输出
        logging.disable(logging.NOTSET)

    def test_configure_parameters(self):
        """测试参数配置"""
        # 模拟suggest_categorical和suggest_float方法
        self.trial.suggest_categorical.side_effect = lambda name, choices: choices[0]
        self.trial.suggest_float.side_effect = lambda name, low, high, **kwargs: (low + high) / 2
        self.trial.suggest_int.side_effect = lambda name, low, high, **kwargs: (low + high) // 2

        # 调用参数配置函数
        config = parameter_explorer.configure_parameters(self.trial, self.config)

        # 验证结果
        self.assertEqual(config, self.config)  # 应该返回相同的配置对象

        # 验证调用
        self.trial.suggest_categorical.assert_called()
        self.trial.suggest_float.assert_called()
        # 不再期望suggest_int被调用，因为我们现在使用suggest_categorical和suggest_float
        # self.trial.suggest_int.assert_called()

    def test_direct_parameter_setting(self):
        """测试直接参数设置"""
        # 模拟suggest_categorical方法
        self.trial.suggest_categorical.side_effect = lambda name, choices: choices[0]

        # 模拟suggest_float方法
        self.trial.suggest_float.side_effect = lambda name, low, high, **kwargs: (low + high) / 2

        # 调用参数配置函数
        parameter_explorer.configure_parameters(self.trial, self.config)

        # 验证hidden_dim和noise_dim的取值是否被设置
        hidden_dim_set = False
        noise_dim_set = False

        for call in self.trial.suggest_categorical.call_args_list:
            args, kwargs = call
            if args[0] == 'model.hidden_dim':
                hidden_dim_set = True
            if args[0] == 'model.noise_dim':
                noise_dim_set = True

        self.assertTrue(hidden_dim_set, "hidden_dim应该被设置")
        self.assertTrue(noise_dim_set, "noise_dim应该被设置")

    def test_attention_heads_dependency(self):
        """测试注意力头数依赖关系"""
        # 模拟suggest_categorical方法，返回True表示使用自注意力
        self.trial.suggest_categorical.side_effect = lambda name, choices: True if name == 'model.use_self_attention' else choices[0]

        # 模拟suggest_int方法
        self.trial.suggest_int.side_effect = lambda name, low, high, **kwargs: (low + high) // 2

        # 模拟suggest_float方法
        self.trial.suggest_float.side_effect = lambda name, low, high, **kwargs: (low + high) / 2

        # 调用参数配置函数
        parameter_explorer.configure_parameters(self.trial, self.config)

        # 验证是否调用了suggest_categorical来设置n_heads
        n_heads_called = False
        for call in self.trial.suggest_categorical.call_args_list:
            args, kwargs = call
            if args[0] == 'model.n_heads':
                n_heads_called = True
                break

        self.assertTrue(n_heads_called, "当use_self_attention为True时，应该设置n_heads参数")

    def test_pruning_on_n_heads_incompatibility(self):
        """测试当 hidden_dim 与所有 n_heads 都不兼容时是否剪枝"""
        # 模拟 suggest_categorical 返回不兼容组合
        def mock_suggest_categorical(name, choices):
            if name == 'model.use_self_attention':
                return True
            if name == 'data.window_size':
                return 36 # 假设窗口大小不触发OOM
            if name == 'model.hidden_dim':
                return 97 # 假设 97 是一个可能的选项，但不能被 2, 4, 8 整除
            # 其他参数返回默认值
            return choices[0]

        self.trial.suggest_categorical.side_effect = mock_suggest_categorical
        # 假设 n_heads 的默认选项是 [2, 4, 8]
        parameter_explorer.default_choices['model.n_heads'] = [2, 4, 8]
        # 假设 hidden_dim 的默认选项包含 97
        parameter_explorer.default_choices['model.hidden_dim'] = [64, 96, 97, 128]

        # 断言调用 configure_parameters 会抛出 TrialPruned 异常
        with self.assertRaises(optuna.TrialPruned) as cm:
            parameter_explorer.configure_parameters(self.trial, self.config)
        self.assertIn("与任何可能的 n_heads", str(cm.exception))

        # 清理模拟的默认值
        parameter_explorer.default_choices['model.hidden_dim'] = [64, 96, 128] # 恢复原始值

    def test_no_pruning_on_compatible_combination(self):
        """测试兼容参数组合时不剪枝"""
        # 模拟 suggest_categorical 返回兼容组合
        def mock_suggest_categorical(name, choices):
            if name == 'model.use_self_attention':
                return True
            if name == 'data.window_size':
                return 48 # 兼容窗口大小
            if name == 'model.hidden_dim':
                return 128 # 兼容 hidden_dim
            if name == 'model.n_heads':
                # 确保传入的 choices 是兼容的 [2, 4, 8]
                self.assertTrue(all(128 % h == 0 for h in choices))
                return 4 # 选择一个兼容的 head
            return choices[0]

        self.trial.suggest_categorical.side_effect = mock_suggest_categorical
        parameter_explorer.default_choices['model.n_heads'] = [2, 4, 8]
        parameter_explorer.default_choices['model.hidden_dim'] = [64, 96, 128]

        # 调用参数配置函数，不应抛出异常
        try:
            parameter_explorer.configure_parameters(self.trial, self.config)
        except optuna.TrialPruned as e:
            self.fail(f"不应剪枝兼容的参数组合: {e}")

        # 验证 n_heads 是否被正确设置
        n_heads_set = False
        for call in self.trial.suggest_categorical.call_args_list:
            args, kwargs = call
            if args[0] == 'model.n_heads':
                n_heads_set = True
                self.assertEqual(args[1], [2, 4, 8]) # 验证传入的 choices 是兼容的
                break
        self.assertTrue(n_heads_set, "应该建议 n_heads")
        # 可以在 self.config 上验证最终设置的值，如果 mock 允许的话

    def test_pruning_on_oom_risk(self):
        """测试当参数组合触发 OOM 风险时是否剪枝"""
        # 模拟 suggest_categorical 返回触发 OOM Risk 1 的组合
        def mock_suggest_categorical(name, choices):
            if name == 'model.use_self_attention':
                return True
            if name == 'data.window_size':
                return 60 # 大窗口
            if name == 'model.hidden_dim':
                return 128 # 大于等于 128 的 hidden_dim
            return choices[0]

        self.trial.suggest_categorical.side_effect = mock_suggest_categorical
        parameter_explorer.default_choices['model.hidden_dim'] = [64, 96, 128]

        # 断言调用 configure_parameters 会抛出 TrialPruned 异常
        with self.assertRaises(optuna.TrialPruned) as cm:
            parameter_explorer.configure_parameters(self.trial, self.config)
        self.assertIn("OOM Risk 1", str(cm.exception))


    def test_window_size_options(self):
        """测试窗口大小选项"""
        # 模拟suggest_categorical方法
        self.trial.suggest_categorical.side_effect = lambda name, choices: choices[0]

        # 模拟suggest_int方法
        self.trial.suggest_int.side_effect = lambda name, low, high, **kwargs: (low + high) // 2

        # 模拟suggest_float方法
        self.trial.suggest_float.side_effect = lambda name, low, high, **kwargs: (low + high) / 2

        # 调用参数配置函数
        parameter_explorer.configure_parameters(self.trial, self.config)

        # 验证window_size的取值范围
        for call in self.trial.suggest_categorical.call_args_list:
            args, kwargs = call
            if args[0] == 'data.window_size':
                self.assertIn(args[1][0], [36, 48, 60])  # 窗口大小应该是预定义的值之一

    # 移除了 test_hidden_dim_constraints_with_attention 和 test_hidden_dim_constraints_without_attention
    # 因为它们测试的是已被移除的内部方法 _get_hidden_dim_choices_with_constraints
    # 新的剪枝逻辑通过 test_pruning_on_oom_risk 和 test_pruning_on_n_heads_incompatibility 进行测试


if __name__ == '__main__':
    unittest.main()
