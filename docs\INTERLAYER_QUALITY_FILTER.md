# 层级间特征质量过滤机制

## 概述

层级间特征质量过滤机制是特征工程系统的重要组成部分，用于在每个层级的空值处理后检测和删除低质量特征，确保传递到下一层级和最终特征集的数据质量。

## 问题背景

在层级化特征生成过程中，各个特征生成器可能会产生低质量特征：

1. **零变化特征**：所有值完全相同的特征列（方差为0）
2. **极低变化特征**：变化幅度极小的特征列（方差低于设定阈值）
3. **常数特征**：经过空值填充后变成常数的特征列

这些低质量特征会导致：
- 增加计算开销而不提供有用信息
- 影响后续特征生成器的性能
- 降低模型训练效果
- 浪费存储空间

## 解决方案

### 核心设计原则

1. **层级间隔离**：在每个层级的空值处理后立即执行质量过滤
2. **双重过滤**：既过滤传递给下一层的数据，也过滤添加到最终特征集的数据
3. **充分暴露问题**：当过滤后特征数量过少时直接抛出异常
4. **特征名称一致性**：确保过滤后的特征名称与张量维度保持一致

### 实现机制

#### 1. 配置结构

```yaml
feature_engineering:
  interlayer_quality_filter:
    enable: true                    # 启用特征质量过滤
    variance_threshold: 0.01        # 方差阈值，低于此值的特征将被删除
    min_features_threshold: 5       # 最小特征数量阈值
```

#### 2. 过滤时机

特征质量过滤在两个关键位置执行：

**传递给下一层级前**：
```python
# 层级间空值处理
current_layer_input = self._handle_interlayer_nan(current_layer_input, level)

# 层级间特征质量过滤
next_layer_feature_names = [f"level_{level}_feature_{i}" for i in range(current_layer_input.shape[1])]
current_layer_input, next_layer_feature_names = self._filter_low_quality_features(
    current_layer_input, next_layer_feature_names, level
)
```

**添加到最终特征集时**：
```python
# 对添加到最终特征集的衍生特征也进行空值处理
level_derived_combined_tensor = self._handle_interlayer_nan(level_derived_combined_tensor, level)

# 对添加到最终特征集的衍生特征进行质量过滤
level_derived_combined_tensor, filtered_derived_names = self._filter_low_quality_features(
    level_derived_combined_tensor, current_layer_derived_names, level
)
```

#### 3. 过滤策略

##### 方差计算
```python
feature_variances = torch.var(data, dim=0, unbiased=False)
```

##### 特征分类
- **零变化特征**：`variance == 0.0`
- **极低变化特征**：`variance < variance_threshold`

##### 阈值检查
```python
if remaining_feature_count < filter_config.min_features_threshold:
    raise ValueError(f"特征质量过滤后剩余特征数 ({remaining_feature_count}) 低于最小阈值")
```

## 配置参数

### 必需参数

- **enable** (bool): 是否启用特征质量过滤
- **variance_threshold** (float): 方差阈值，建议值 0.01
- **min_features_threshold** (int): 最小特征数量阈值，建议值 5

### 参数验证

系统会自动验证配置参数的合理性：
- `variance_threshold` 必须为非负数
- `min_features_threshold` 必须至少为1

## 日志记录

系统会记录详细的过滤信息：

```
Level 1: 开始特征质量过滤 | 原始特征数: 15
Level 1: 特征质量过滤完成
Level 1: 删除 3 个低质量特征，保留 12 个特征
Level 1: 删除的特征详情:
Level 1:   - const_feature_1: 零变化特征 (方差=0.0)
Level 1:   - low_var_feature: 极低变化特征 (方差=0.005000 < 0.01)
Level 1:   - const_feature_2: 零变化特征 (方差=0.0)
```

## 性能考虑

1. **计算效率**：使用PyTorch的向量化操作计算方差
2. **内存优化**：只在需要时创建新的张量
3. **设备一致性**：保持张量在原设备上进行计算

## 错误处理

### 配置错误
- 缺少配置项时抛出 `ValueError`
- 配置参数无效时抛出 `ValueError`

### 数据一致性错误
- 特征名称数量与张量维度不匹配时抛出 `ValueError`

### 质量阈值错误
- 过滤后特征数量低于最小阈值时抛出 `ValueError`

## 测试覆盖

### 单元测试

1. **功能禁用测试**：验证禁用时不进行过滤
2. **零变化特征测试**：验证常数特征被正确删除
3. **极低变化特征测试**：验证低方差特征被正确删除
4. **阈值强制执行测试**：验证最小特征数量阈值
5. **一致性检查测试**：验证特征名称与张量维度匹配
6. **配置验证测试**：验证配置参数的有效性
7. **边界情况测试**：验证各种边界条件

### 集成测试

1. **完整流程测试**：验证在实际特征工程中的工作
2. **多层级测试**：验证多层级中的正确过滤
3. **性能测试**：验证过滤操作的性能影响

## 与现有系统的集成

特征质量过滤机制与现有的层级间空值处理机制紧密集成：

1. **执行顺序**：空值处理 → 质量过滤
2. **数据流**：确保过滤后的数据正确传递
3. **名称管理**：维护特征名称与张量维度的一致性
4. **错误处理**：统一的异常处理策略

## 最佳实践

1. **阈值设置**：根据数据特性调整 `variance_threshold`
2. **最小特征数**：根据下游模型需求设置 `min_features_threshold`
3. **监控日志**：关注过滤日志以了解数据质量
4. **性能监控**：监控过滤操作对整体性能的影响

## 未来扩展

可以考虑的扩展功能：
- 支持更多质量检测指标（如偏度、峰度）
- 可配置的过滤策略
- 特征质量评分机制
- 自适应阈值调整
